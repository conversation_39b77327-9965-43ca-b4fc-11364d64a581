PODS:
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FasterImage (1.7.2):
    - FasterImage/Nuke (= 1.7.2)
    - FasterImage/NukeUI (= 1.7.2)
    - React-Core
  - FasterImage/Nuke (1.7.2):
    - React-Core
  - FasterImage/NukeUI (1.7.2):
    - React-Core
  - FBLazyVector (0.72.17)
  - FBReactNativeSpec (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.17)
    - RCTTypeSafety (= 0.72.17)
    - React-Core (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - Firebase/CoreOnly (10.7.0):
    - FirebaseCore (= 10.7.0)
  - Firebase/Messaging (10.7.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.7.0)
  - FirebaseAnalytics (10.29.0):
    - FirebaseAnalytics/AdIdSupport (= 10.29.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.7.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreExtension (10.7.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.7.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flipper (0.201.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - FlipperKit (0.201.0):
    - FlipperKit/Core (= 0.201.0)
  - FlipperKit/Core (0.201.0):
    - Flipper (~> 0.201.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.201.0):
    - Flipper (~> 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.201.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.201.0)
  - FlipperKit/FKPortForwarding (0.201.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.201.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
  - FlipperKit/FlipperKitLayoutPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutTextSearchable (0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.29.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.29.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.29.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.72.17):
    - hermes-engine/Pre-built (= 0.72.17)
  - hermes-engine/Pre-built (0.72.17)
  - libevent (2.1.12)
  - MMKV (2.2.2):
    - MMKVCore (~> 2.2.2)
  - MMKVCore (2.2.2)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - OpenSSL-Universal (1.1.1100)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.17)
  - RCTTypeSafety (0.72.17):
    - FBLazyVector (= 0.72.17)
    - RCTRequired (= 0.72.17)
    - React-Core (= 0.72.17)
  - React (0.72.17):
    - React-Core (= 0.72.17)
    - React-Core/DevSupport (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-RCTActionSheet (= 0.72.17)
    - React-RCTAnimation (= 0.72.17)
    - React-RCTBlob (= 0.72.17)
    - React-RCTImage (= 0.72.17)
    - React-RCTLinking (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - React-RCTSettings (= 0.72.17)
    - React-RCTText (= 0.72.17)
    - React-RCTVibration (= 0.72.17)
  - React-callinvoker (0.72.17)
  - React-Codegen (0.72.17):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.17)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/CoreModulesHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTBlob
    - React-RCTImage (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-debug (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-jsinspector (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
    - React-runtimeexecutor (= 0.72.17)
  - React-debug (0.72.17)
  - React-hermes (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.17)
    - React-jsi
    - React-jsiexecutor (= 0.72.17)
    - React-jsinspector (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - React-jsi (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - React-jsinspector (0.72.17)
  - React-logger (0.72.17):
    - glog
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-date-picker (5.0.11):
    - React-Core
  - react-native-flipper (0.201.0):
    - React-Core
  - react-native-mmkv (2.12.2):
    - MMKV (>= 1.3.3)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-webview (13.13.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-NativeModulesApple (0.72.17):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.17)
  - React-RCTActionSheet (0.72.17):
    - React-Core/RCTActionSheetHeaders (= 0.72.17)
  - React-RCTAnimation (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTAnimationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTAppDelegate (0.72.17):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.17):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTBlobHeaders (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTImage (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTImageHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTLinking (0.72.17):
    - React-Codegen (= 0.72.17)
    - React-Core/RCTLinkingHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTNetwork (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTNetworkHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTSettings (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTSettingsHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTText (0.72.17):
    - React-Core/RCTTextHeaders (= 0.72.17)
  - React-RCTVibration (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTVibrationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-rncore (0.72.17)
  - React-runtimeexecutor (0.72.17):
    - React-jsi (= 0.72.17)
  - React-runtimescheduler (0.72.17):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - ReactCommon/turbomodule/core (0.72.17):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNDateTimePicker (8.3.0):
    - React-Core
  - RNFBApp (17.5.0):
    - Firebase/CoreOnly (= 10.7.0)
    - React-Core
  - RNFBMessaging (17.5.0):
    - Firebase/Messaging (= 10.7.0)
    - FirebaseCoreExtension (= 10.7.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.12.0):
    - React-Core
  - RNImageCropPicker (0.41.6):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.6)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.6):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNNotifee (7.9.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.9.0)
  - RNNotifee/NotifeeCore (7.9.0):
    - React-Core
  - RNPermissions (5.4.2):
    - React-Core
  - RNReanimated (2.15.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.22.0):
    - React-Core
    - React-RCTImage
  - RNSentry (5.36.0):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-hermes
    - Sentry/HybridSDK (= 8.41.0)
  - RNSVG (13.9.0):
    - React-Core
  - Sentry/HybridSDK (8.41.0)
  - SocketRocket (0.6.1)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - "FasterImage (from `../node_modules/@candlefinance/faster-image`)"
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - FirebaseAnalytics
  - FirebaseCore
  - FirebaseMessaging
  - Flipper (= 0.201.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - FlipperKit (= 0.201.0)
  - FlipperKit/Core (= 0.201.0)
  - FlipperKit/CppBridge (= 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.201.0)
  - FlipperKit/FBDefines (= 0.201.0)
  - FlipperKit/FKPortForwarding (= 0.201.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.201.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.201.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.201.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.201.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.201.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.201.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-flipper (from `../node_modules/react-native-flipper`)
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - FlipperKit
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - libevent
    - MMKV
    - MMKVCore
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - Sentry
    - SocketRocket
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FasterImage:
    :path: "../node_modules/@candlefinance/faster-image"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-flipper:
    :path: "../node_modules/react-native-flipper"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FasterImage: 5822257844dadd582de1cec096e8b006f32047d0
  FBLazyVector: 66398fc2381d8fa1eee4c0f80d931587a7b927e8
  FBReactNativeSpec: 0f8cecf999d709dba7626bbf565b1b5f8f46a5c1
  Firebase: 0219acf760880eeec8ce479895bd7767466d9f81
  FirebaseAnalytics: 23717de130b779aa506e757edb9713d24b6ffeda
  FirebaseCore: e317665b9d744727a97e623edbbed009320afdd7
  FirebaseCoreExtension: f17247ba8c61e4d3c8d136b5e2de3cb4ac6a85b6
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: ac9062bcc35ed56e15a0241d8fd317022499baf8
  Flipper: c7a0093234c4bdd456e363f2f19b2e4b27652d44
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  FlipperKit: 37525a5d056ef9b93d1578e04bc3ea1de940094f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: f9de05ee17401e3355f68e8fc8b5064d429f5918
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 982096772bd947125ee3b4f72ace6cb9a33f1d02
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  MMKV: b4802ebd5a7c68fc0c4a5ccb4926fbdfb62d68e0
  MMKVCore: a255341a3746955f50da2ad9121b18cb2b346e61
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 01c639ec840ee03928b2d65f5cd5297d737b3834
  RCTTypeSafety: 9623592521a1576363baf3d6ab8d164cfe9062bf
  React: 3c0beeda318c3c515a6bb2c1f197b55bd731aa43
  React-callinvoker: 0cd6ff2cdd80255c82cd4628fc925df1e7133a1a
  React-Codegen: 20cfee78965306e4a5bb65d95958142eda116cfc
  React-Core: df691c59e0c8a3db4d138a51bb8862c52c8b14f1
  React-CoreModules: cebd223e814ac07bc1f597bbd2480167a2c7a130
  React-cxxreact: dec3959d439708cb7dd73b46a11ed64c3eea79da
  React-debug: 3a5091cbda7ffe5f11ad0443109810fcd1a3e885
  React-hermes: f3b6b278c4ff7e6664a86b2bf964a4dc4ae72d34
  React-jsi: 6ec4bd4cd929ae9d468b4984b0ae2c657aeeb2da
  React-jsiexecutor: 8dc585381e476c3ff2e9468f444c90c4d1d5b874
  React-jsinspector: 853b8631b908636bb09ef77cb217376c38a0c8ff
  React-logger: 9ca44bb5703bf2355f3c2d2e5e67bfe98ca2dc34
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-date-picker: 50eb8dfc6262c536fb1b2e26fa3a628211f44bf6
  react-native-flipper: 18d15fc2a82b4bb3270b6dd8100d99a8c32cb3b1
  react-native-mmkv: e2d73a7d37f23918066a4b985a94c9fafc3f20c1
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-webview: d2246ddbefdfa9c87d07cd4956a3aa3d17fb6e77
  React-NativeModulesApple: 2edfcbb25329e3eb5f76eb79d89010de7c1c6f1f
  React-perflogger: 785b0063af5178298a61b54bb46aae9a19c7bbb5
  React-RCTActionSheet: 84f37b34bd77249263ace75471d6664393c29972
  React-RCTAnimation: 5713910b6223154df4bba80a0bda4e2e671b00f8
  React-RCTAppDelegate: d3777d05bf6b65fed847536af520731c1a167dea
  React-RCTBlob: d4d3fb21c0bf1ce2f0308e05227ecd3f19266bf7
  React-RCTImage: 2e63a483be5d4e46a80dea3b17c9abee38006feb
  React-RCTLinking: e3ff685ee62187f8f61e938357307c1f890125b5
  React-RCTNetwork: a35842997a403edfdc1ec25b61a0e10a0526368d
  React-RCTSettings: aef81e0ac54268d2928ad31c4f91056cc75e5ce9
  React-RCTText: 7becec5f53f03b20da11f4b7e40e6bcfd476d134
  React-RCTVibration: defaae8016de9b3351a2a67ee8ef3fbdd643b0e1
  React-rncore: dfd20469cfad38e48b1c3cc9c4367db63f5231d7
  React-runtimeexecutor: 448409b5ae5a01b7793239f630051960c7dd39f9
  React-runtimescheduler: ff30efdf24f8ce62eb517a391ded3d99c4263bb0
  React-utils: 7959d4553163b61e01bbe83dbd80e58ca420aecb
  ReactCommon: 841449721eb2e004de2c3366844b0a03f329f2cb
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNDateTimePicker: a793ed8822283f576dd0a205a0916c5098c2611f
  RNFBApp: 0d8bf86673bbad0524d1ceac3944d71ccf48a0e4
  RNFBMessaging: 4b8cb1215465bacca36d0fcfc2f51e7e2aa1bc91
  RNGestureHandler: dec4645026e7401a0899f2846d864403478ff6a5
  RNImageCropPicker: 8e39c01f205e00d739c31e682f068aac315587bf
  RNNotifee: 935f3ea8c134c88cbf8b13ea0c97c72c09ad2116
  RNPermissions: f4df15c4027ee5093d0436c67868216c046da115
  RNReanimated: 9d6808928dc4de4652faea94320a6b20898975a8
  RNScreens: 68fd1060f57dd1023880bf4c05d74784b5392789
  RNSentry: 70aef1a4405eae756c7e3dda380f187cbd7ad43d
  RNSVG: 53c661b76829783cdaf9b7a57258f3d3b4c28315
  Sentry: 54d0fe6c0df448497c8ed4cce66ccf7027e1823e
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: ef534101bb891fb09bae657417f34d399c1efe38

PODFILE CHECKSUM: 61d808617c8a045ed53f01e5873bfabb6bdc1b5c

COCOAPODS: 1.16.2
