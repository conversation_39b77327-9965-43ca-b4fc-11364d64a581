<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>ReactNativeTemplate</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>NSCameraUsageDescription</key>
<string>We need your permission to use your camera</string>
<key>NSMicrophoneUsageDescription</key>
<string>Your message explaining why the app needs access to the microphone</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>We need your permission to access your photo library</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>We need your permission to save photos</string>

	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>Poppins-Black.ttf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-Regular.ttf</string>
		<string>Kalam-Bold.ttf</string>
		<string>Kalam-Light.ttf</string>
		<string>Kalam-Regular.ttf</string>
		<string>helvetica-compressed-5871d14b6903a.otf</string>
		<string>helvetica-rounded-bold-5871d05ead8de.otf</string>
		<string>Helvetica-Bold.ttf</string>
		<string>Helvetica-BoldOblique.ttf</string>
		<string>helvetica-light-587ebe5a59211.ttf</string>
		<string>Helvetica-Oblique.ttf</string>
		<string>Helvetica.ttf</string>
	</array>
</dict>
</plist>
