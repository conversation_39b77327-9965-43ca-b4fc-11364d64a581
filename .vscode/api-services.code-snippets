{
    // Place your vite-react-js-Template workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
    // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
    // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
    // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
    // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
    // Placeholders with the same ids are connected.
    // Example:
    // "Print to console": {
    // 	"scope": "javascript,typescript",
    // 	"prefix": "log",
    // 	"body": [
    // 		"console.log('$1');",
    // 		"$2"
    // 	],
    // 	"description": "Log output to console"
    // }
    "GET Async Thunk": {
        "prefix": "getApi",
        "body": [
            "export const ${1:methodName} = createAsyncThunk(\"${1:actionName}\", async ({_},{dispatch,getState,rejectWithValue}) => {",
            "\ttry {",
            "\t\tconst res = await getApi(${3:API_ENDPOINT});",
            "\t\treturn { res };",
            "\t} catch (error) {",
            "\t\treturn handleApiError(error);",
            "\t}",
            "})"
        ],
        "description": "Create Async Thunk"
    },
    "POST Async Thunk": {
        "prefix": "postApi",
        "body": [
            "export const ${1:methodName} = createAsyncThunk(\"${1:actionName}\", async ({...payload},{dispatch,getState,rejectWithValue}) => {",
            "\ttry {",
            "\t\tconst res = await postApi(${3:API_ENDPOINT},{...payload});",
            "\t\treturn { res };",
            "\t} catch (error) {",
            "\t\treturn handleApiError(error);",
            "\t}",
            "})"
        ],
        "description": "Create Async Thunk"
    },
    "PATCH Async Thunk": {
        "prefix": "patchApi",
        "body": [
            "export const ${1:methodName} = createAsyncThunk(\"${1:actionName}\", async ({...payload},{dispatch,getState}) => {",
            "\ttry {",
            "\t\tconst res = await patchApi(${3:API_ENDPOINT},{...payload});",
            "\t\treturn { res };",
            "\t} catch (error) {",
            "\t\treturn handleApiError(error);",
            "\t}",
            "})"
        ],
        "description": "Create Async Thunk"
    },
    "DELETE Async Thunk": {
        "prefix": "deleteApi",
        "body": [
            "export const ${1:methodName} = createAsyncThunk(\"${1:actionName}\", async ({...payload},{dispatch,getState}) => {",
            "\ttry {",
            "\t\tconst res = await deleteApi(${3:API_ENDPOINT},{...payload});",
            "\t\treturn { res };",
            "\t} catch (error) {",
            "\t\treturn handleApiError(error);",
            "\t}",
            "})"
        ],
        "description": "Create Async Thunk"
    }
}
