{
    "parser": "@babel/eslint-parser",
    "extends": [
        "eslint:recommended",
        "plugin:react/recommended",
        "plugin:import/recommended",
        "plugin:jsx-a11y/recommended",
        "eslint-config-prettier"
    ],
    "env": {
        // this is for the "document" and "window" global object
        "browser": true,
        "node": true,
        "es6": true
    },
    "rules": {
        "no-unused-vars": "warn",
        "react/react-in-jsx-scope": "off",
        "no-extra-boolean-cast": "off",
        "react/prop-types": "off"
    },
    "plugins": ["react", "react-native"],

    "parserOptions": {
        "ecmaVersion": 12,
        "requireConfigFile": false,
        "sourceType": "module"
    },
    "settings": {
        "import/ignore": [
            "node_modules/react-native/index\\.js$",
            "react-native"
        ],
        "react": {
            "version": "detect"
        },
        "import/resolver": {
            "alias": {
                "map": [["~", "./src"]],
                "extensions": [".js", ".jsx", ".ts", ".tsx"]
            },
            "node": {
                "extensions": [".js", ".jsx", ".ts", ".tsx"]
            }
        }
    },
    "globals": {
        "__CLIENT__": true,
        "__SERVER__": true,
        "__DEV__": true
    }
}
