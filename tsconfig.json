{
    "compilerOptions": {
        "allowJs": true,
        "checkJs": false,
        "moduleResolution": "node",
        "target": "es6", // or "es6" or later, depending on your needs
        "module": "commonjs", // or "esnext" if you're using ES modules
        "strict": true, // enables all strict type-checking options
        "jsx": "react-jsx", // or "react-jsx" if you're using React 17+
        "esModuleInterop": true, // enables interoperability between CommonJS and ES Modules
        "skipLibCheck": true, // skips type checking of declaration files
        "forceConsistentCasingInFileNames": true, // ensures consistent casing in file names
        "baseUrl": ".", // base directory for resolving non-relative module names
        "paths": {
            "~/*": ["src/*"]
        },
        "outDir": "./dist"
    },
    "include": ["src/**/*"], // include all files in the src directory
    "exclude": [
        "node_modules",
        "babel.config.js",
        "metro.config.js",
        "jest.config.js"
    ] // exclude these files/directories
}
