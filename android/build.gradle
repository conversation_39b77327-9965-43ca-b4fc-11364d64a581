// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
       ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "23.1.7779620"
        kotlin_version = "1.9.23"
        cmakeVersion = "3.22.1"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        // ...for google firebase notification
        classpath 'com.google.gms:google-services:4.3.15'
        // end google firebase notiifcation
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version" // <- add this line

    }
}


// ADD THIS OUTSIDE buildscript {}
allprojects {
    repositories {
        google()
        mavenCentral()
    }

    configurations.all {
        resolutionStrategy.force "androidx.camera:camera-core:1.3.4"
        resolutionStrategy.force "androidx.camera:camera-camera2:1.3.4"
        resolutionStrategy.force "androidx.camera:camera-lifecycle:1.3.4"
        resolutionStrategy.force "androidx.camera:camera-view:1.3.4"
        resolutionStrategy.force "androidx.camera:camera-video:1.3.4"
        resolutionStrategy.force "androidx.camera:camera-extensions:1.3.4"
        resolutionStrategy.force "androidx.lifecycle:lifecycle-livedata-core:2.6.2"
    }
}

