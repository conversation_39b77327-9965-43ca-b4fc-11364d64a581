// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
       ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "23.1.7779620"
        kotlin_version = "1.9.22"
        cmakeVersion = "3.22.1"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        // ...for google firebase notification
        classpath 'com.google.gms:google-services:4.3.15'
        // end google firebase notiifcation
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version" // <- add this line

    }
}
