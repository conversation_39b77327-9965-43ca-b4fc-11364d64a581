module.exports = function (api) {
    console.log('babel env - ' + api.env());

    let plugins = [
        [
            'module-resolver',
            {
                extensions: ['.ios.js', '.android.js', '.js', '.jsx', '.ts', '.tsx', '.json'],
                alias: {
                    '~': './src',
                },
            },
        ],
        'react-native-reanimated/plugin'
    ];

    if (!api.env('development')) {
        plugins.push('transform-remove-console');
    }

    return {
        presets: ['module:metro-react-native-babel-preset'],
        plugins: plugins,
    };
};
