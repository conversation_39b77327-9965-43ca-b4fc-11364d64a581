{"name": "ReactNativeTemplate", "version": "0.0.1", "private": true, "android_package": "com.hop_wellness_bussiness", "android_build_number": 1, "android_release_number": "1.0.0", "scripts": {"lint": "eslint .", "test": "jest", "ios": "react-native run-ios", "pod": "cd ios && pod install --repo-update && cd ..", "start": "react-native start --reset-cache", "start2": "react-native start --reset-cache --port 8082", "sentry-sourcemaps:upload": "bash upload-sentry-sourcemaps.sh", "android": "react-native run-android --active-arch-only", "android:release": "react-native run-android --variant=release", "android:clean": "cd android && ./gradlew clean && cd ..", "ios:clean": "cd ios && rm -rf Pods Podfile.lock && pod install", "android-apk:release": "cd android && ./gradlew assembleRelease && cd ..", "android-apk:debug": "cd android && ./gradlew assembleDebug && cd ..", "ios:simulator": "react-native run-ios --simulator=iPhone 14", "format": "npx prettier --write .", "clean": "rm -rf node_modules yarn.lock /tmp/metro-* && yarn cache clean", "depcheck": "npx npm-check", "upgrade": "npx @rnx-kit/align-deps --requirements react-native@<RN_VERSION> --write"}, "dependencies": {"@candlefinance/faster-image": "^1.6.2", "@notifee/react-native": "^7.7.1", "@react-native-async-storage/async-storage": "^1.18.2", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-firebase/app": "^17.5.0", "@react-native-firebase/messaging": "^17.4.3", "@react-navigation/native": "^6.0.8", "@react-navigation/native-stack": "^6.9.12", "@reduxjs/toolkit": "^1.9.5", "@sentry/react-native": "^5.9.1", "axios": "^1.4.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "formik": "^2.4.6", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "react": "18.2.0", "react-native": "^0.72.0", "react-native-big-calendar": "^4.15.1", "react-native-calendar-strip": "^2.2.6", "react-native-calendar-timetable": "^1.0.7", "react-native-calendars": "^1.1307.0", "react-native-collapsible": "^1.6.2", "react-native-date-picker": "^5.0.5", "react-native-element-dropdown": "^2.12.1", "react-native-gesture-handler": "2.12.0", "react-native-gifted-charts": "^1.4.61", "react-native-image-crop-picker": "^0.41.2", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.10.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.5", "react-native-reanimated": "2.15.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.5.3", "react-native-screens": "3.22.0", "react-native-signature-canvas": "^4.7.2", "react-native-svg": "13.9.0", "react-native-swiper-flatlist": "^3.2.5", "react-native-toast-message": "^2.1.6", "react-native-uuid": "^2.0.3", "react-native-vision-camera": "3.6.17", "react-native-webview": "^13.12.1", "react-native-wheely": "^0.6.0", "react-redux": "^8.0.5", "redux-persist": "^6.0.0", "rn-fetch-blob": "^0.12.0", "tailwind-merge": "^2.4.0", "twrnc": "^3.6.1", "yup": "^1.4.0"}, "dependenciesComments": {"@react-native-async-storage/async-storage": "for react native redux offline", "@react-native-firebase/app": "Base project dependency for all firebase projects", "react-native-safe-area-context": "Dependency for react-navigation/native", "react-native-screens": "Dependency for react-navigation/native", "@react-navigation/native-stack": "new library alternative of  @react-navigation/native", "@react-native-community/netinfo": "needed library for the redux offline"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "^0.76.5", "prettier": "^2.8.7", "prettier-plugin-tailwindcss": "^0.2.7", "react-native-flipper": "^0.201.0", "react-native-gradle-plugin": "^0.71.19", "react-native-mmkv-flipper-plugin": "^1.0.0", "react-native-svg-transformer": "^1.0.0", "react-test-renderer": "18.2.0", "redux-flipper": "^2.0.2", "tailwindcss": "^3.3.2", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}