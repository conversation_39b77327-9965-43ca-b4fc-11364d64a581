import { AppRegistry, LogBox } from 'react-native';
import Toast from 'react-native-toast-message';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { CommonResetLocalState } from '~/redux/actions/layout-actions';
import store, { persistor } from '~/redux/store';

import Text from '~/components/library/text';

import { config } from '~/scripts/toast';

import { isDevelopment } from '~/env';

import { name as appName } from './app.json';
import App from './src/app';

// import {InitializeNotification} from './src/scripts/notification';

LogBox.ignoreAllLogs(); //Ignore all log notifications

// initialize all the notification state handlers in the starting of app
// InitializeNotification();

const Root = () => {
    function removePersistLocalState() {
        store.dispatch(CommonResetLocalState());
    }

    return (
        <>
            <Provider store={store}>
                <PersistGate
                    loading={<Text>Loading...</Text>}
                    persistor={persistor}
                    onBeforeLift={removePersistLocalState}
                    onError={(error) =>
                        console.error('Error While Rehydrating:', error)
                    }
                    timeout={isDevelopment ? 12 * 1000 : undefined}
                >
                    <App />
                    <Toast config={config} />
                </PersistGate>
            </Provider>
        </>
    );
};

AppRegistry.registerComponent(appName, () => Root);
