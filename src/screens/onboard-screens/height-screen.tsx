import React, { useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SetOnboardingData } from '~/redux/slices/auth-slice';

import tw from '~/styles/tailwind';

import { GOAL_SCREEN, HEIGHT_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import BackButton from '~/components/common/back-button';
import HeightScroller from '~/components/onboarding-screen-comp/height-scroller';

import Alertify from '~/scripts/toast';

import { useAppDispatch } from '~/hooks/redux-hooks';

const HeightScreen = ({ props, navigation }: any) => {
    const dispatch = useAppDispatch();
    const [UserHeight, setUserHeight] = useState('');
    const HeightValue = (value: any) => {
        setUserHeight(value);
    };

    const handleNext = () => {
        if (UserHeight === '62 cm') {
            return Alertify.error('Please select height');
        }
        dispatch(SetOnboardingData({ height: UserHeight }));
        navigation.navigate(GOAL_SCREEN);
    };

    function handleSkip() {
        navigation.navigate(GOAL_SCREEN);
    }

    return (
        <View style={[tw`flex-1 `, styles.container]}>
            <View style={tw`mx-3`}>
                <BackButton />
            </View>
            <View style={tw`px-5  `}>
                <View style={[tw`mt-5 p-5`, styles.heightCard]}>
                    <Text
                        style={tw`text-24 text-center font-bold font-700 text-[#455560]`}
                    >
                        What's your height?
                    </Text>
                    <View style={tw`pt-5`}>
                        <HeightScroller updateHeight={HeightValue} />
                    </View>
                </View>
            </View>
            <View
                style={tw`flex flex-row w-full justify-between px-[5%] py-[5%] absolute bottom-5`}
            >
                <Button
                    style={tw`w-[33.3%] rounded-full bg-transparent border-2`}
                    onPress={handleSkip}
                >
                    <Text style={tw`text-black`}>Skip</Text>
                </Button>
                <Button
                    style={tw`w-[33.3%] rounded-full`}
                    onPress={handleNext}
                    nextImage
                >
                    Next
                </Button>
            </View>
        </View>
    );
};

export default HeightScreen;

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#F2F2F7',
        height: '100%',
    },

    heightCard: {
        backgroundColor: '#FFFFFF',
        borderRadius: 30,
    },
    heightText: {
        fontFamily: 'HelveticaNeue-Bold',
        // fontSize: 30,
        textAlign: 'center',
        color: '#FFFFFF',
    },
    button: {
        backgroundColor: '#D1FF70',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        // fontSize: 18,
        color: '#000000',
        fontFamily: 'HelveticaNeue-Bold',
        lineHeight: 21,
    },
    buttonImage: {
        width: 15,
        height: 15,
        marginLeft: 10,
        resizeMode: 'contain',
    },
    imageButtonContainer: {
        flexDirection: 'row',
        marginTop: 30,
        justifyContent: 'space-around',
        width: '100%',
        alignItems: 'center',
    },
    backImage: {
        height: 54,
        width: 54,
    },
});
