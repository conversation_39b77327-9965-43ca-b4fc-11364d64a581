import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import { StyleSheet, Text, View } from 'react-native';
import * as Yup from 'yup';
import { OnboardUserData } from '~/redux/actions/auth-actions';

import tw from '~/styles/tailwind';

import { DASHBOARD_STACK } from '~/constants/navigation-constant';

import { RootStackParamList } from '~/navigation/navigation-types';

import { Button } from '~/components/atoms';
import TextInput from '~/components/atoms/text-input';
import BackButton from '~/components/common/back-button';

import { useAppDispatch } from '~/hooks/redux-hooks';

type MeasurementScreen = NativeStackNavigationProp<
    RootStackParamList,
    'MEASUREMENT_SCREEN'
>;

interface MeasurementScreenProps {
    navigation: MeasurementScreen;
}

type HandleChangeFunction = (field: string) => (value: string) => void;

const initial_state = {
    chest: '',
    shoulder: '',
    arm: '',
    forearm: '',
    thigh: '',
    waist: '',
    hip: '',
    calf: '',
};

const validationSchema = Yup.object().shape({
    chest: Yup.number()
        .required('Chest measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    shoulder: Yup.number()
        .required('Shoulder measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    arm: Yup.number()
        .required('Arm measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    forearm: Yup.number()
        .required('Forearm measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    thigh: Yup.number()
        .required('Thigh measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    waist: Yup.number()
        .required('Waist measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    hip: Yup.number()
        .required('Hip measurement is required')
        .min(1, 'Measurement must be greater than 0'),
    calf: Yup.number()
        .required('Calf measurement is required')
        .min(1, 'Measurement must be greater than 0'),
});

const MeasurementScreen: React.FC<MeasurementScreenProps> = ({
    navigation,
}) => {
    const dispatch = useAppDispatch();

    function handleSkipButton() {
        dispatch(OnboardUserData({}));
        navigation.navigate(DASHBOARD_STACK);
    }

    function handleSubmit(values: any) {
        console.log({ values });
        dispatch(OnboardUserData({ measurement: values }))
            .unwrap()
            .then((data) => {
                navigation.navigate(DASHBOARD_STACK);
            });
    }

    const handleChangeText =
        (handleChange: HandleChangeFunction, fieldName: string) =>
        (text: string) => {
            let manipulatedValue = '';
            // Remove any non-digit and non-dot characters
            const cleanedText = text.replace(/[^0-9.]/g, '');

            // Split the text into parts before and after the dot
            const [integerPart, decimalPart] = cleanedText.split('.');

            if (decimalPart !== undefined) {
                // If there's a decimal part, format it
                const formattedInteger = integerPart.slice(0, 2);
                const formattedDecimal = decimalPart.slice(0, 1);
                manipulatedValue = `${formattedInteger}.${formattedDecimal}`;
            } else if (integerPart.length > 2) {
                // If integer part is longer than 2 digits, add the dot
                manipulatedValue = `${integerPart.slice(
                    0,
                    2
                )}.${integerPart.slice(2, 3)}`;
            } else {
                // Otherwise, just set the integer part
                manipulatedValue = integerPart;
            }
            handleChange(fieldName)(manipulatedValue);
        };

    return (
        <>
            <Formik
                initialValues={initial_state}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
            >
                {({
                    handleChange,
                    handleBlur,
                    handleSubmit,
                    values,
                    errors,
                    touched,
                }) => (
                    <View style={tw`bg-[#F2F2F7] h-[100%] flex-1`}>
                        <View style={tw`mx-3`}>
                            <BackButton />
                        </View>
                        <Text
                            style={[
                                tw`text-24 font-bold text-center font-700 text-[#455560]`,
                                styles.heading,
                            ]}
                        >
                            Your Measurement
                        </Text>
                        <Text
                            style={[
                                tw`text-12 mb-2 text-center font-700 text-[#72849AB2]`,
                            ]}
                        >
                            You can always change this later
                        </Text>

                        <Text style={styles.divider}></Text>
                        <View style={tw`w-full h-auto`}>
                            <View style={styles.inputContainer}>
                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Chest
                                    </Text>
                                    <TextInput
                                        value={values.chest}
                                        placeholder="Chest"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'chest'
                                        )}
                                        onBlur={handleBlur('chest')}
                                    />
                                    {touched.chest && errors.chest && (
                                        <Text style={styles.errorText}>
                                            {errors.chest}
                                        </Text>
                                    )}
                                </View>
                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Shoulder
                                    </Text>
                                    <TextInput
                                        value={values.shoulder}
                                        placeholder="Shoulder"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'shoulder'
                                        )}
                                        onBlur={handleBlur('shoulder')}
                                    />
                                    {touched.shoulder && errors.shoulder && (
                                        <Text style={styles.errorText}>
                                            {errors.shoulder}
                                        </Text>
                                    )}
                                </View>
                            </View>
                            <View style={styles.inputContainer}>
                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Arm
                                    </Text>
                                    <TextInput
                                        value={values.arm}
                                        placeholder="Arm"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'arm'
                                        )}
                                        onBlur={handleBlur('arm')}
                                    />
                                    {touched.arm && errors.arm && (
                                        <Text style={styles.errorText}>
                                            {errors.arm}
                                        </Text>
                                    )}
                                </View>

                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Forearm
                                    </Text>
                                    <TextInput
                                        value={values.forearm}
                                        placeholder="Forearm"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'forearm'
                                        )}
                                        onBlur={handleBlur('forearm')}
                                    />
                                    {touched.forearm && errors.forearm && (
                                        <Text style={styles.errorText}>
                                            {errors.forearm}
                                        </Text>
                                    )}
                                </View>
                            </View>
                            <View style={styles.inputContainer}>
                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Thigh
                                    </Text>
                                    <TextInput
                                        value={values.thigh}
                                        placeholder="Thigh"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'thigh'
                                        )}
                                        onBlur={handleBlur('thigh')}
                                    />
                                    {touched.thigh && errors.thigh && (
                                        <Text style={styles.errorText}>
                                            {errors.thigh}
                                        </Text>
                                    )}
                                </View>

                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Waist
                                    </Text>
                                    <TextInput
                                        value={values.waist}
                                        placeholder="Waist"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'waist'
                                        )}
                                        onBlur={handleBlur('waist')}
                                    />
                                    {touched.waist && errors.waist && (
                                        <Text style={styles.errorText}>
                                            {errors.waist}
                                        </Text>
                                    )}
                                </View>
                            </View>
                            <View style={styles.inputContainer}>
                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Hip
                                    </Text>
                                    <TextInput
                                        value={values.hip}
                                        placeholder="Hip"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'hip'
                                        )}
                                        onBlur={handleBlur('hip')}
                                    />
                                    {touched.hip && errors.hip && (
                                        <Text style={styles.errorText}>
                                            {errors.hip}
                                        </Text>
                                    )}
                                </View>

                                <View style={tw`w-[44%]`}>
                                    <Text style={[tw`text-14`, styles.label]}>
                                        Calf
                                    </Text>
                                    <TextInput
                                        value={values.calf}
                                        placeholder="Calf"
                                        endsWith="in"
                                        style={[tw`text-14 px-4`, styles.input]}
                                        keyboardType="numeric"
                                        onChangeText={handleChangeText(
                                            handleChange,
                                            'calf'
                                        )}
                                        onBlur={handleBlur('calf')}
                                    />
                                    {touched.calf && errors.calf && (
                                        <Text style={styles.errorText}>
                                            {errors.calf}
                                        </Text>
                                    )}
                                </View>
                            </View>
                        </View>
                        <View
                            style={tw`flex flex-row w-full justify-between px-[5%] py-[5%] absolute bottom-5`}
                        >
                            <Button
                                style={tw`w-[33.3%] rounded-full bg-transparent border-2`}
                                onPress={handleSkipButton}
                            >
                                <Text style={tw`text-black`}>Skip</Text>
                            </Button>

                            <Button
                                style={tw`w-[33.3%] rounded-full`}
                                onPress={handleSubmit}
                                nextImage
                            >
                                Next
                            </Button>
                        </View>
                    </View>
                )}
            </Formik>
        </>
    );
};

export default MeasurementScreen;

const styles = StyleSheet.create({
    headingContainer: {
        // alignItems: 'center',
    },
    heading: {
        fontFamily: 'HelveticaNeue-Bold',
        // fontSize: 30,
        // color: '#FFFFFF',
    },
    subHeading: {
        fontFamily: 'Helvetica',
        // fontSize: 13,
        color: '#CCCCCC',
        marginBottom: 8,
    },
    divider: {
        borderTopWidth: 1,
        borderTopColor: '#000000',
    },

    inputContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        width: '100%',
    },
    label: {
        color: '#455560',
        marginTop: 10,
        // fontSize: 14,
        fontFamily: 'Helvetica',
    },
    input: {
        borderWidth: 1,
        width: '100%',
        height: 42,
        borderColor: '#455560',
        marginTop: 6,
        borderRadius: 100,
        // paddingHorizontal: 20,
        color: '#000000',
        fontFamily: 'HelveticaNeue',
        // fontSize: 16,
        // marginRight: 14,
        // marginBottom: 20,
        backgroundColor: '#FFFFF',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    button: {
        backgroundColor: '#D1FF70',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        // fontSize: 18,
        color: '#000000',
        fontFamily: 'HelveticaNeue-Bold',
        lineHeight: 21,
    },
    buttonImage: {
        width: 15,
        height: 15,
        marginLeft: 10,
        resizeMode: 'contain',
    },
    imageButtonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },
    backImage: {
        height: 54,
        width: 54,
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: 4,
    },
});
