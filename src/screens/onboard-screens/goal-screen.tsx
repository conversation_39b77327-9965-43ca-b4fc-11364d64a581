import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SetOnboardingData } from '~/redux/slices/auth-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { ACTIVITY_LEVEL } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import BackButton from '~/components/common/back-button';

import Alertify from '~/scripts/toast';

import { useAppDispatch } from '~/hooks/redux-hooks';

const Goal = [
    {
        id: 1,
        GoalName: 'Lose Weight',
        Icon: `${Asset.LoseWeightIcon}`,
    },
    {
        id: 2,
        GoalName: 'Gain Weight',
        Icon: `${Asset.GainWeightIcon}`,
    },
    {
        id: 3,
        GoalName: 'Body Building',
        Icon: `${Asset.BodyBuildingIcon}`,
    },
    {
        id: 4,
        GoalName: 'Strength',
        Icon: `${Asset.StrengthIcon}`,
    },
    {
        id: 5,
        GoalName: 'Stamina and Mobility',
        Icon: `${Asset.Stamina_MobilityIcon}`,
    },
    {
        id: 6,
        GoalName: 'Injury Rehabilitation',
        Icon: `${Asset.InjuryRehabilitationIcon}`,
    },
];

interface GoalScreenProps {
    navigation: any;
}

const GoalScreen: React.FC<GoalScreenProps> = ({ navigation }) => {
    const dispatch = useAppDispatch();
    const [selectedGoal, setSelectedGoal] = useState('');
    const [Goals, setGoals] = useState([]);

    const handleNext = () => {
        if (selectedGoal === '') {
            return Alertify.error('Please select your goal');
        }
        dispatch(SetOnboardingData({ goal: selectedGoal }));
        navigation.navigate(ACTIVITY_LEVEL);
    };

    function handleSkip() {
        navigation.navigate(ACTIVITY_LEVEL);
    }

    return (
        <View style={styles.container}>
            <View style={tw`m-3`}>
                <BackButton heading={`What's your goal?`} />
            </View>
            <View style={[tw`px-5  `]}>
                {/* <Header title={"Goal Screen"}/> */}
                <Text
                    style={[
                        tw`text-24 mb-3 font-bold text-center font-700 text-[#455560]`,
                        styles.heading,
                    ]}
                >
                    What's your goal?
                </Text>
                <View style={styles.allCards}>
                    <View style={styles.card}>
                        {Goal.map((item, index) => {
                            return (
                                <TouchableOpacity
                                    key={item.id}
                                    onPress={() =>
                                        setSelectedGoal(item.GoalName)
                                    }
                                    style={[
                                        styles.itemCard,
                                        selectedGoal === item.GoalName &&
                                            styles.selectedCard, // Apply a different style for selected card
                                    ]}
                                >
                                    {item.Icon != null ? (
                                        <Image
                                            source={item.Icon}
                                            style={[
                                                tw``,
                                                styles.image,
                                                selectedGoal ===
                                                    item.GoalName &&
                                                    styles.selectedImage,
                                            ]}
                                        />
                                    ) : (
                                        ''
                                    )}
                                    <Text
                                        style={[
                                            tw`text-16 font-medium font-700`,
                                            styles.itemText,
                                        ]}
                                    >
                                        {item.GoalName}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </View>
            </View>
            <View
                style={tw`flex flex-row w-full justify-between px-[5%] py-[5%] absolute bottom-5`}
            >
                <Button
                    style={tw`w-[33.3%] rounded-full bg-transparent border-2`}
                    onPress={handleSkip}
                >
                    <Text style={tw`text-black`}>Skip</Text>
                </Button>
                <Button
                    style={tw`w-[33.3%] rounded-full`}
                    onPress={handleNext}
                    nextImage
                >
                    Next
                </Button>
            </View>
        </View>
    );
};

export default GoalScreen;

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#F2F2F7',
        height: '100%',
        flex: 1,
    },
    heading: {
        // color: '#FFFFFF',
        fontFamily: 'HelveticaNeue-Bold',
        // fontSize: 30,
        textAlign: 'center',
    },
    allCards: {
        flexDirection: 'column',
    },
    card: {
        marginTop: 10,
    },
    itemCard: {
        alignItems: 'center',
        justifyContent: 'flex-start',
        backgroundColor: '#FFFFFF',
        borderRadius: 50,
        paddingStart: 30,
        height: 70,
        flexDirection: 'row',
        marginBottom: 15,
    },
    image: {
        height: 30,
        width: 30,
        resizeMode: 'contain',
    },
    itemText: {
        color: '#000000',
        // fontSize: 24,
        marginLeft: 50,
    },
    button: {
        backgroundColor: '#D1FF70',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        // fontSize: 18,
        color: '#000000',
        lineHeight: 21,
    },
    buttonImage: {
        width: 15,
        height: 15,
        marginLeft: 10,
        resizeMode: 'contain',
    },
    imageButtonContainer: {
        flexDirection: 'row',

        justifyContent: 'space-between',
    },

    selectedCard: {
        borderColor: '#000000',
        borderWidth: 2,
    },
    selectedImage: {
        tintColor: 'black',
    },
});
