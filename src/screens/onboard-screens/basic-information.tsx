import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import { memo, useEffect, useState } from 'react';
import {
    Image,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';
import * as Yup from 'yup';
import { UploadImage } from '~/redux/actions/common-actions';
import {
    CityList,
    GetState,
    GetUserDetails,
    UpdateUserDetails,
} from '~/redux/actions/profile-actions';
import { SetUserDetails } from '~/redux/slices/profile-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';
import EditIcon from '~/assets/svg/edit-icon.svg';

import { WAIVER_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import TextInput from '~/components/atoms/text-input';
import BackButtonHeading from '~/components/common/back-button-heading';
import ImagePickerModal from '~/components/common/image-picker-modal';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { navigationRef } from '~/hooks/useLocation';

const ProfileImageComponent = () => {
    const [showModal, setModalVisible] = useState(false);
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        photo: state.profile_store.personalDetails.photo,
    }));

    function handleImagePicked(image) {
        console.log(image);
        dispatch(UploadImage({ image }))
            .unwrap()
            .then((data) => {
                const image = data.response.data;
                dispatch(SetUserDetails({ key: 'photo', value: image }));
            })
            .catch();
    }

    return (
        <View
            style={tw`flex flex-row gap-3 w-[90%] mx-auto pl-3 bg-[#000000] border border-white items-center rounded-[30px] h-[120px] justify-center`}
        >
            <Image
                source={renderProfileImage(store.photo)}
                style={tw`h-20 w-20 rounded-full object-cover`}
            />
            <TouchableOpacity
                onPress={() => setModalVisible(true)}
                style={tw`bg-white px-2 py-1.5 rounded-xl`}
            >
                <Text style={tw`text-black text-[14px]`}>
                    Change Profile Photo
                </Text>
            </TouchableOpacity>
            <ImagePickerModal
                visible={showModal}
                onClose={() => setModalVisible(false)}
                onImagePicked={(image) => handleImagePicked(image)}
            />
        </View>
    );
};

const genderData = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
];

function renderProfileImage(img: any) {
    if (img) {
        return { uri: img };
    }
    return Asset.ProfileIconNew;
}

const validationSchema = Yup.object().shape({
    firstName: Yup.string().required('First Name is required'),
    lastName: Yup.string().required('Last Name is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    mobile: Yup.string().required('Mobile number is required'),
});

const BasicInformationScreen = () => {
    const dispatch = useAppDispatch();
    const [date, setDate] = useState();
    const [open, setOpen] = useState(false);
    const [isEnabled, setIsEnabled] = useState(false);
    const [gender, setGender] = useState(null);
    const [stateId, setStateId] = useState(null);
    const [cityId, setCityId] = useState(null);
    const [CityOptions, setCityOptions] = useState([]);

    const store = useAppSelector((state) => ({
        firstName: state.profile_store.personalDetails.firstName,
        allStates: state.profile_store.allStates,
        cityList: state.profile_store.cityList,
        lastName: state.profile_store.personalDetails.lastName,
        gender: state.profile_store.personalDetails.gender,
        email: state.profile_store.personalDetails.email,
        mobile: state.profile_store.personalDetails.mobile,
        addressLine1: state.profile_store.personalDetails.address.addressLine1,
        addressLine2: state.profile_store.personalDetails.address.addressLine2,
        postalCode: state.profile_store.personalDetails.address.postalCode,
        city: state.profile_store.personalDetails.address.city,
        state: state.profile_store.personalDetails.address.state,
        emergencyContactPerson:
            state.profile_store.personalDetails.emergencyContactPerson,
        emergencyContactPhone:
            state.profile_store.personalDetails.emergencyContactPhone,
    }));

    const StateOptions = store.allStates?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const navigation = useNavigation();

    const handleSubmit = (values) => {
        dispatch(
            UpdateUserDetails({
                values,
                gender: gender || store.gender,
                date,
                stateId,
                cityId,
            })
        )
            .unwrap()
            .then(() => navigationRef.goBack());
    };

    const toggleSwitch = () => {
        setIsEnabled((previousState) => !previousState);
        if (!isEnabled) {
            navigation.navigate(WAIVER_SCREEN);
        }
    };

    function setVal(setValues) {
        setValues((prevValues) => {
            const newValues = {
                firstName: store.firstName,
                lastName: store.lastName,
                email: store.email,
                dob: store.dob,
                mobile: store.mobile,
                addressLine1: store.addressLine1,
                addressLine2: store.addressLine2,
                postalCode: store.postalCode,
                city: store.city,
                gender: store.gender,
                state: store.state,
                emergencyContactPerson: store.emergencyContactPerson,
                emergencyContactPhone: store.emergencyContactPhone,
            };
            // Only update if values have changed
            if (JSON.stringify(prevValues) !== JSON.stringify(newValues)) {
                return newValues;
            }
            return prevValues;
        });
    }

    useEffect(() => {
        dispatch(GetState({}));
        dispatch(GetUserDetails({}));
    }, []);

    useEffect(() => {
        if (stateId) {
            dispatch(CityList({ stateId: stateId }))
                .unwrap()
                .then((data) => {
                    console.log('result', data.data);
                    const CityOptions = data?.data?.map((item: any) => ({
                        value: item._id,
                        label: item.name,
                        id: item._id,
                    }));
                    setCityOptions(CityOptions);
                });
        }
    }, [stateId]);

    // Calculate the maximum date allowed (yesterday's date)
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() - 1);

    return (
        <Formik
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            initialValues={{
                firstName: store.firstName,
                lastName: store.lastName,
                gender: store.gender,
                email: store.email,
                dob: store.dob,
                mobile: store.mobile,
                addressLine1: store.addressLine1,
                addressLine2: store.addressLine2,
                postalCode: store.postalCode,
                city: store.city,
                state: store.state,
                emergencyContactPerson: store.emergencyContactPerson,
                emergencyContactPhone: store.emergencyContactPhone,
            }}
        >
            {({
                handleChange,
                handleBlur,
                handleSubmit,
                values,
                errors,
                touched,
                setValues,
                setFieldValue,
            }) => {
                return (
                    <>
                        <ScrollView>
                            <View style={tw`bg-#D0FF01] w-[100%] h-36 px-3`}>
                                <View style={tw`pt-5`}>
                                    <BackButtonHeading
                                        Heading={'Basic Information'}
                                    />
                                </View>
                                <View
                                    style={tw`flex flex-row items-center gap-3`}
                                ></View>
                            </View>

                            <View style={tw`bottom-[3%] w-[100%]`}>
                                <ProfileImageComponent />
                                <View style={tw`pt-7 px-5`}>
                                    <View
                                        style={tw`flex flex-row justify-between pb-2 items-center`}
                                    >
                                        <Text
                                            style={tw`text-[#455560] text-[15px] font-bold`}
                                        >
                                            Basic Information
                                        </Text>
                                        <EditIcon width={18} height={18} />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="First Name"
                                            placeholder="First Name"
                                            onChangeText={handleChange(
                                                'firstName'
                                            )}
                                            onBlur={handleBlur('firstName')}
                                            value={values.firstName}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                        {errors.firstName &&
                                            touched.firstName && (
                                                <Text style={styles.errorText}>
                                                    {errors.firstName}
                                                </Text>
                                            )}
                                    </View>
                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Last Name"
                                            placeholder="Last Name"
                                            onChangeText={handleChange(
                                                'lastName'
                                            )}
                                            onBlur={handleBlur('lastName')}
                                            value={values.lastName}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                        {errors.lastName &&
                                            touched.lastName && (
                                                <Text style={styles.errorText}>
                                                    {errors.lastName}
                                                </Text>
                                            )}
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <Text
                                            style={tw`text-black text-16 pb-2`}
                                        >
                                            DOB
                                        </Text>
                                        <TouchableOpacity
                                            style={tw`w-[100%] h-[32px] border-b border-[#45556066] pb-1 flex justify-center px-1`}
                                            onPress={() => setOpen(true)}
                                        >
                                            <Text
                                                style={tw`text-[#455560] text-[14px]`}
                                            >
                                                {date
                                                    ? date.toDateString()
                                                    : 'Enter the dob'}
                                            </Text>
                                            <DatePicker
                                                modal
                                                open={open}
                                                date={date || new Date()}
                                                maximumDate={maxDate}
                                                onConfirm={(date) => {
                                                    setOpen(false);
                                                    setDate(date);
                                                }}
                                                onCancel={() => {
                                                    setOpen(false);
                                                }}
                                                mode="date"
                                            />
                                        </TouchableOpacity>
                                    </View>
                                    <View style={tw`mt-5`}>
                                        <Text
                                            style={tw`text-black text-16 pb-2`}
                                        >
                                            Gender
                                        </Text>
                                        <View
                                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                                        >
                                            <Dropdown
                                                data={genderData}
                                                labelField="label"
                                                valueField="value"
                                                placeholder="Select gender"
                                                value={store.gender}
                                                onChange={(item) =>
                                                    setGender(item.value)
                                                }
                                            />
                                        </View>
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Phone Number"
                                            placeholder="Phone Number"
                                            onChangeText={handleChange(
                                                'mobile'
                                            )}
                                            onBlur={handleBlur('mobile')}
                                            value={values.mobile}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                        {errors.mobile && touched.mobile && (
                                            <Text style={styles.errorText}>
                                                {errors.mobile}
                                            </Text>
                                        )}
                                    </View>
                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Email"
                                            placeholder="Email"
                                            onChangeText={handleChange('email')}
                                            onBlur={handleBlur('email')}
                                            value={values.email}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                        {errors.email && touched.email && (
                                            <Text style={styles.errorText}>
                                                {errors.email}
                                            </Text>
                                        )}
                                    </View>
                                </View>
                                <View style={tw`pt-7 px-5`}>
                                    <View
                                        style={tw`flex flex-row justify-between pb-2  items-center`}
                                    >
                                        <Text
                                            style={tw`text-[#455560] text-[15px] font-bold`}
                                        >
                                            Address
                                        </Text>
                                        <EditIcon width={18} height={18} />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Address 1"
                                            placeholder="Address 1"
                                            value={values.addressLine1}
                                            onChangeText={handleChange(
                                                'addressLine1'
                                            )}
                                            onBlur={handleBlur('addressLine1')}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Address 2"
                                            placeholder="Address 2"
                                            value={values.addressLine2}
                                            onChangeText={handleChange(
                                                'addressLine2'
                                            )}
                                            onBlur={handleBlur('addressLine2')}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Postal Code"
                                            placeholder="Postal Code"
                                            value={values.postalCode}
                                            onChangeText={handleChange(
                                                'postalCode'
                                            )}
                                            onBlur={handleBlur('postalCode')}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label="Country"
                                            placeholder="Country"
                                            value={values.country || 'India'}
                                            onChangeText={handleChange(
                                                'country'
                                            )}
                                            onBlur={handleBlur('country')}
                                            style={tw`border-b border-[#45556066] pb-1 pointer-events-none`}
                                        />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <Text
                                            style={tw`text-black text-16 pb-2`}
                                        >
                                            State
                                        </Text>
                                        <View
                                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                                        >
                                            <Dropdown
                                                data={StateOptions}
                                                labelField="label"
                                                valueField="value"
                                                placeholder="Select State"
                                                value={values.state}
                                                onChange={(item) => {
                                                    setStateId(item.value);
                                                    setFieldValue(
                                                        'state',
                                                        item.value
                                                    );
                                                }}
                                            />
                                        </View>
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <Text
                                            style={tw`text-black text-16 pb-2`}
                                        >
                                            City
                                        </Text>
                                        <View
                                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                                        >
                                            <Dropdown
                                                data={CityOptions}
                                                labelField="label"
                                                valueField="value"
                                                placeholder="Select City"
                                                value={values.city}
                                                onChange={(item) => {
                                                    setCityId(item.value);
                                                    setFieldValue(
                                                        'city',
                                                        item.value
                                                    );
                                                }}
                                            />
                                        </View>
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label=" Emergency Contact Person"
                                            placeholder="Emergency Contact Person"
                                            value={
                                                values.emergencyContactPerson
                                            }
                                            onChangeText={handleChange(
                                                'emergencyContactPerson'
                                            )}
                                            onBlur={handleBlur(
                                                'emergencyContactPerson'
                                            )}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                    </View>

                                    <View style={tw`mt-5`}>
                                        <TextInput
                                            label=" Emergency Contact Person Number"
                                            placeholder="Emergency Contact Person Number"
                                            value={values.emergencyContactPhone}
                                            onChangeText={handleChange(
                                                'emergencyContactPhone'
                                            )}
                                            onBlur={handleBlur(
                                                'emergencyContactPhone'
                                            )}
                                            style={tw`border-b border-[#45556066] pb-1`}
                                        />
                                    </View>

                                    <View
                                        style={tw`flex flex-row justify-between  pt-7 items-end`}
                                    >
                                        <Text
                                            style={tw`text-[#455560] text-[14px]`}
                                        >
                                            I agree to the terms of the {'\n'}
                                            liability waiver
                                        </Text>
                                        <Switch
                                            trackColor={{
                                                false: '#767577',
                                                true: '#81b0ff',
                                            }}
                                            thumbColor={
                                                isEnabled
                                                    ? '#3E79F7'
                                                    : '#f4f3f4'
                                            }
                                            ios_backgroundColor="#3e3e3e"
                                            onValueChange={toggleSwitch}
                                            value={isEnabled}
                                            style={styles.switch}
                                        />
                                    </View>
                                </View>
                            </View>
                            <View
                                style={tw`flex justify-center flex-row py-16`}
                            >
                                <Button
                                    style={tw`w-[70%] rounded-full`}
                                    onPress={handleSubmit}
                                >
                                    Save
                                </Button>
                            </View>
                        </ScrollView>
                    </>
                );
            }}
        </Formik>
    );
};

export default BasicInformationScreen;

const styles = StyleSheet.create({
    input: {
        borderWidth: 1,
        height: 32,
        borderColor: 'lightgrey',
        marginTop: 6,
        borderRadius: 100,
        color: '#000000',
        fontFamily: 'HelveticaNeue',
        backgroundColor: '#FFFFF',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: 4,
    },
    switch: {
        transform: [{ scaleX: 1.5 }, { scaleY: 1.5 }],
    },
});
