import React, { useEffect, useState } from 'react';
import {
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    Vibration,
    View,
} from 'react-native';
import WheelPicker from 'react-native-wheely';
import { SetOnboardingData } from '~/redux/slices/auth-slice';

import tw from '~/styles/tailwind';

import { HEIGHT_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import BackButton from '~/components/common/back-button';

import Alertify from '~/scripts/toast';

import { useAppDispatch } from '~/hooks/redux-hooks';

function generateWightArray(start: number, end: number) {
    const weight_array = [];
    for (let i = start; i <= end; i++) {
        weight_array.push(i);
    }
    return weight_array;
}

const weight_array = generateWightArray(20, 150);
const weight_decimal_array = generateWightArray(0, 9);
const WeightScreen = ({ props, navigation }: any) => {
    const dispatch = useAppDispatch();
    const [UserHeight, setUserHeight] = useState(1);
    const [UserWeight, setUserWeight] = useState('');
    const [decimal_weight, setDecimalWeight] = useState(0);

    const HeightValue = (value: React.SetStateAction<number>) => {
        setUserHeight(value);
        props.updateInfo('height', value);
    };

    function handleWeight(index: number, type: string) {
        if (type === 'weight') {
            setUserWeight(weight_array[index]);
        }
        if (type === 'decimal') {
            setDecimalWeight(weight_decimal_array[index]);
        }
    }

    const handleNext = () => {
        if (UserWeight === '') {
            return Alertify.error('Enter Weight');
        }
        dispatch(
            SetOnboardingData({ weight: `${UserWeight}.${decimal_weight}` })
        );
        navigation.navigate(HEIGHT_SCREEN);
    };

    function handleSkip() {
        navigation.navigate(HEIGHT_SCREEN);
    }

    return (
        <View style={[tw`flex-1 `, styles.container]}>
            <View style={tw`mx-3`}>
                <BackButton />
            </View>
            <View style={tw`px-5 mt-5 `}>
                <View style={[tw` p-5 `, styles.weightCard]}>
                    <Text
                        style={tw`text-24 font-bold text-center font-700 text-[#455560]`}
                    >
                        {`What's your weight?`}
                    </Text>
                    <Text
                        style={tw`text-18 font-bold text-center font-700 text-black mt-2 `}
                    >
                        {props?.User_Information?.weight}
                        Kg
                    </Text>
                    <View style={tw`flex flex-row justify-center items-center`}>
                        <WheelPicker
                            selectedIndex={30}
                            options={weight_array}
                            onChange={(index) => {
                                handleWeight(index, 'weight');
                                Vibration.vibrate();
                            }}
                            selectedIndicatorStyle={tw`border-t-2 rounded-0 border-b-2 border-black bg-white`}
                            itemHeight={50}
                            itemTextStyle={tw`text-black  font-bold text-18 `}
                            containerProps={tw`mx-5 flex justify-center items-center`}
                        />
                        <WheelPicker
                            selectedIndex={0}
                            options={weight_decimal_array}
                            onChange={(index) => {
                                handleWeight(index, 'decimal');
                                Vibration.vibrate();
                            }}
                            selectedIndicatorStyle={tw`border-t-2 rounded-0 border-b-2 border-black bg-white `}
                            itemHeight={50}
                            itemTextStyle={tw`text-black  font-bold text-18 `}
                            containerProps={tw`mx-5 flex justify-center items-center`}
                        />
                    </View>
                </View>
            </View>
            <View
                style={tw`flex flex-row w-full justify-between px-[5%] py-[5%] absolute bottom-5`}
            >
                <Button
                    style={tw`w-[33.3%] rounded-full bg-transparent border-2`}
                    onPress={handleSkip}
                >
                    <Text style={tw`text-black`}>Skip</Text>
                </Button>
                <Button
                    style={tw`w-[33.3%] rounded-full`}
                    onPress={handleNext}
                    nextImage
                >
                    Next
                </Button>
            </View>
        </View>
    );
};

export default WeightScreen;

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#F2F2F7',
        height: '100%',
    },
    weightCard: {
        backgroundColor: 'white',
        borderRadius: 30,
    },

    weightScroller: {
        flex: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        paddingTop: 40,
    },
    weightScrollerText: {
        color: '#FFFFFF',
        fontFamily: 'HelveticaNeue-Bold',
        margin: 0,
        height: 50,
    },
    weightScrollerUnit: {
        color: '#FFFFFF',
        fontFamily: 'HelveticaNeue-',
    },
    heightCard: {
        backgroundColor: '#111111',
        borderRadius: 30,
        height: '44%',
    },
    heightText: {
        fontFamily: 'HelveticaNeue-Bold',
        // fontSize: 30,
        textAlign: 'center',
        color: '#FFFFFF',
    },
    button: {
        backgroundColor: '#000000',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        // fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 21,
    },
    buttonImage: {
        width: 25,
        height: 25,
        // marginLeft: 10,
        resizeMode: 'cover',
    },
    imageButtonContainer: {
        flexDirection: 'row',
        marginTop: 30,
        justifyContent: 'space-around',
        width: '100%',
        alignItems: 'center',
    },
    backImage: {
        height: 54,
        width: 54,
    },
});
