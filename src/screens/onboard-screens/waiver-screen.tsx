import React, { useRef, useState } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import SignatureScreen from 'react-native-signature-canvas';
import RNFetchBlob from 'rn-fetch-blob';

import tw from '~/styles/tailwind';

import XIcon from '~/assets/svg/x-svg.svg';

import { navigationRef } from '~/hooks/useLocation';

interface WaiverScreenProps {
    text: string;
    onOK: () => void;
}

const WaiverScreen: React.FC<WaiverScreenProps> = ({ text, onOK }) => {
    const [signature, setSign] = useState<string | null>(null);
    const ref = useRef<SignatureScreen | null>(null);

    const handleOK = (signature: string) => {
        console.log(signature);
        setSign(signature);
    };

    const handleEmpty = () => {
        alert('Kindly Affix your Signature!');
    };

    const handleClear = () => {
        console.log('clear success!');
        setSign(null);
        ref.current?.clearSignature(); // Clear the signature using the ref
        navigationRef.goBack();
    };

    const handleSave = async () => {
        if (Platform.OS === 'android') {
            const isReadGranted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
            );
            if (isReadGranted === PermissionsAndroid.RESULTS.GRANTED) {
                const dirs = RNFetchBlob.fs.dirs;
                console.log(dirs);
                const image_data = signature?.split('data:image/png;base64,');
                if (image_data) {
                    const filePath =
                        dirs.DownloadDir +
                        '/' +
                        'signature' +
                        new Date().getMilliseconds() +
                        '.png';
                    RNFetchBlob.fs
                        .writeFile(filePath, image_data[1], 'base64')
                        .then(() => {
                            console.log('got here', filePath);
                            // RNFetchBlob.ios.previewDocument("file://"+filePath)
                        })
                        .catch((errorMessage) => {
                            console.log(errorMessage);
                        });
                }
            }
        }

        if (Platform.OS === 'ios') {
            const dirs = RNFetchBlob.fs.dirs;
            console.log(dirs);
            const image_data = signature?.split('data:image/png;base64,');
            if (image_data) {
                const filePath =
                    dirs.DocumentDir +
                    '/' +
                    'signature' +
                    new Date().getMilliseconds() +
                    '.png';
                RNFetchBlob.fs
                    .writeFile(filePath, image_data[1], 'base64')
                    .then(() => {
                        RNFetchBlob.ios.previewDocument('file://' + filePath);
                        console.log(filePath);
                    })
                    .catch((errorMessage) => {
                        console.log(errorMessage);
                    });
            }
        }
    };

    return (
        <View style={tw`bg-[#ffffff] h-full`}>
            <View
                style={tw`bg-#D0FF01] w-[100%] h-[60px] flex flex-row gap-3 px-4 justify-between items-center border border-[#7d7d7d] border-b-1`}
            >
                <TouchableOpacity onPress={handleClear}>
                    <XIcon width={15} height={15} />
                </TouchableOpacity>
                <TouchableOpacity onPress={handleSave}>
                    <Text style={tw`text-[#455560] text-lg`}>Agree</Text>
                </TouchableOpacity>
            </View>
            <SafeAreaView style={styles.container}>
                <SignatureScreen
                    ref={ref}
                    webStyle={styles.signaturePad}
                    onOK={handleOK}
                    descriptionText="Signature"
                    onEmpty={handleEmpty}
                    confirmText="confirm"
                />
            </SafeAreaView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        height: 250,
        padding: 10,
    },
    signaturePad: {
        fontSize: 10,
        width: 700,
        height: 600,
        backgroundColor: '#fafafa',
        boxShadow:
            '0 1px 4px rgba(0, 0, 0, 0.27), 0 0 40px rgba(0, 0, 0, 0.08) inset',
    },
});

export default WaiverScreen;
