// import { useNavigation } from '@react-navigation/native';
import { SetStateAction, useState } from 'react';
import {
    Image,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SetOnboardingData } from '~/redux/slices/auth-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { WEIGHT_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import AgeScroller from '~/components/onboarding-screen-comp/age-scroller';

import Alertify from '~/scripts/toast';

import { useAppDispatch } from '~/hooks/redux-hooks';

const GenderScreen = ({ navigation }: any) => {
    const dispatch = useAppDispatch();
    const [selectedGender, setSelectedGender] = useState('male');
    const [age, setAge] = useState('1');

    const handleGenderSelection = (gender: SetStateAction<string>) => {
        setSelectedGender(gender);
    };
    const AgeValue = (value: SetStateAction<string>) => {
        console.log('value', value);
        setAge(value);
    };

    const handleNext = () => {
        if (age === '16') {
            return Alertify.error('Please select age');
        }
        dispatch(SetOnboardingData({ age, gender: selectedGender }));
        navigation.navigate(WEIGHT_SCREEN);
    };

    function handleSkip() {
        navigation.navigate(WEIGHT_SCREEN);
    }

    return (
        <>
            <SafeAreaView style={tw`flex-1 bg-[#F2F2F7]`}>
                <ScrollView>
                    <View style={tw` h-[90%]   px-5 pt-5 `}>
                        <View
                            style={[
                                tw`p-3 w-[100%]  mt-5 flex justify-center items-center `,
                                styles.genderCard,
                            ]}
                        >
                            <Text
                                style={[
                                    tw`text-24 font-700 font-bold`,
                                    styles.genderText,
                                ]}
                            >
                                Gender
                            </Text>
                            <View style={[tw`mt-5`, styles.genderOption]}>
                                <TouchableOpacity
                                    style={[
                                        tw`flex justify-center items-center `,
                                        styles.maleOption,
                                        selectedGender === 'male' &&
                                            styles.selectedOption,
                                    ]}
                                    onPress={() =>
                                        handleGenderSelection('male')
                                    }
                                >
                                    <Image
                                        source={require('../../assets/images/Mars.png')}
                                        style={[
                                            tw`w-10 h-10`,
                                            { resizeMode: 'contain' },
                                            selectedGender === 'male' &&
                                                styles.selectedImage,
                                        ]}
                                    />
                                    <Text
                                        style={[
                                            tw`text-12`,
                                            styles.maleText,
                                            selectedGender === 'male' &&
                                                styles.selectedText,
                                        ]}
                                    >
                                        Male
                                    </Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={[
                                        tw`flex justify-center items-center`,
                                        styles.femaleOption,
                                        selectedGender === 'female' &&
                                            styles.selectedOption,
                                    ]}
                                    onPress={() =>
                                        handleGenderSelection('female')
                                    }
                                >
                                    <Image
                                        source={require('../../assets/images/Venus.png')}
                                        style={[
                                            tw`w-10 h-10`,
                                            styles.femaleImage,
                                            selectedGender === 'female' &&
                                                styles.selectedImage,
                                        ]}
                                    />
                                    <Text
                                        style={[
                                            tw`text-12`,
                                            styles.maleText,
                                            selectedGender === 'female' &&
                                                styles.selectedText,
                                        ]}
                                    >
                                        Female
                                    </Text>
                                </TouchableOpacity>
                            </View>
                            <View style={[tw`mb-3 `, styles.genderOption]}>
                                <TouchableOpacity
                                    style={[
                                        tw`flex justify-center items-center`,
                                        styles.otherOption,
                                        selectedGender === 'other' &&
                                            styles.selectedOption,
                                    ]}
                                    onPress={() =>
                                        handleGenderSelection('other')
                                    }
                                >
                                    <Image
                                        source={require('../../assets/images/fa6-solid_transgender.png')}
                                        style={[
                                            tw`w-10 h-10`,
                                            { resizeMode: 'contain' },
                                            selectedGender === 'other' &&
                                                styles.selectedImage,
                                        ]}
                                    />
                                    <Text
                                        style={[
                                            tw`text-12 font-400`,
                                            styles.maleText,
                                            selectedGender === 'other' &&
                                                styles.selectedText,
                                        ]}
                                    >
                                        Other
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                        <View
                            style={[tw`p-3 w-[100%] py-5 mt-5`, styles.ageCard]}
                        >
                            <Text
                                style={[
                                    tw`text-24 font-400 font-bold`,
                                    styles.ageText,
                                ]}
                            >
                                How old are you?
                            </Text>
                            <View>
                                <AgeScroller updateAge={AgeValue} />
                            </View>
                        </View>
                    </View>
                </ScrollView>
                <View
                    style={tw`flex flex-row w-full justify-between px-[5%] absolute bottom-5`}
                >
                    <Button
                        style={tw`w-[33.3%] rounded-full bg-transparent border-2   `}
                        onPress={handleSkip}
                    >
                        <Text style={tw`text-black `}>Skip</Text>
                    </Button>
                    <Button
                        style={tw`w-[33.3%] rounded-full`}
                        onPress={handleNext}
                        nextImage
                    >
                        Next
                    </Button>
                </View>
            </SafeAreaView>
        </>
    );
};

export default GenderScreen;

const styles = StyleSheet.create({
    genderCard: {
        backgroundColor: '#FFFFFF',
        borderRadius: 30,
    },
    genderText: {
        color: '#455560',
        textAlign: 'center',
        // fontSize: 26,
    },
    genderOption: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        width: '100%',
    },
    maleOption: {
        backgroundColor: '#72849AB2',
        width: 100,
        height: 100,
        borderRadius: 100,
    },
    femaleOption: {
        backgroundColor: '#72849AB2',
        width: 100,
        height: 100,
        borderRadius: 100,
    },
    otherOption: {
        backgroundColor: '#72849AB2',
        width: 100,
        height: 100,
        borderRadius: 100,
    },
    selectedOption: {
        backgroundColor: '#000000',
        fontWeight: 700,
    },

    femaleImage: {
        resizeMode: 'contain',
    },
    selectedImage: {
        // tintColor: 'black',
    },
    maleText: {
        color: '#FFFFFF',
        fontSize: 14,
        fontWeight: 'bold',
        marginTop: 10,
    },
    selectedText: {
        color: 'white',
    },
    ageCard: {
        backgroundColor: 'white',
        borderRadius: 30,
    },
    ageText: {
        // fontSize: 26,
        textAlign: 'center',
        color: '#455560',
        marginBottom: 10,
    },
    pickerSelectStyles: {
        // fontSize: 16,
        paddingHorizontal: 10,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: 'gray',
        borderRadius: 8,
        color: 'black',
        paddingRight: 30,
        backgroundColor: '#ffffff',
    },
    button: {
        backgroundColor: '#000000',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        // fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 21,
    },
    buttonImage: {
        width: 25,
        height: 25,
        // marginLeft: 10,
        resizeMode: 'cover',
    },
});
