import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SetOnboardingData } from '~/redux/slices/auth-slice';

import tw from '~/styles/tailwind';

import { MEASUREMENT_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import BackButton from '~/components/common/back-button';

import Alertify from '~/scripts/toast';

import { useAppDispatch } from '~/hooks/redux-hooks';

const Activity = [
    {
        id: 1,
        ActivityName: 'Rookie',
    },
    {
        id: 2,
        ActivityName: 'Beginner',
    },
    {
        id: 3,
        ActivityName: 'Intermediate',
    },
    {
        id: 4,
        ActivityName: 'Advance',
    },
    {
        id: 5,
        ActivityName: 'True Beast',
    },
];

const ActivityLevel = ({ navigation }: any) => {
    const dispatch = useAppDispatch();
    const [selectedGoal, setSelectedGoal] = useState('');
    const handleNext = () => {
        if (selectedGoal === '') {
            return Alertify.error('Select your activity level');
        }
        dispatch(SetOnboardingData({ activityLevel: selectedGoal }));
        navigation.navigate(MEASUREMENT_SCREEN);
    };

    function handleSkip() {
        navigation.navigate(MEASUREMENT_SCREEN);
    }

    return (
        <View style={styles.container}>
            <View style={tw`m-3`}>
                <BackButton heading={`Activity Level`} />
            </View>
            <Text
                style={[
                    tw`text-24 mb-3 font-bold text-center font-700 text-[#455560]`,
                    styles.heading,
                ]}
            >
                Activity Level
            </Text>
            <View style={[tw`px-5 `]}>
                <View style={styles.allCards}>
                    <View style={styles.card}>
                        {Activity.map((item, index) => (
                            <TouchableOpacity
                                key={item.ActivityName}
                                onPress={() =>
                                    setSelectedGoal(item.ActivityName)
                                }
                                style={[
                                    styles.itemCard,
                                    selectedGoal === item.ActivityName &&
                                        styles.selectedCard,
                                ]}
                            >
                                <Text
                                    style={[
                                        tw`text-16 font-700`,
                                        styles.itemText,
                                    ]}
                                >
                                    {item.ActivityName}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </View>

            <View
                style={tw`flex flex-row w-full justify-between px-[5%] py-[5%] absolute bottom-5`}
            >
                <Button
                    style={tw`w-[33.3%] rounded-full bg-transparent border-2`}
                    onPress={handleSkip}
                >
                    <Text style={tw`text-black`}>Skip</Text>
                </Button>
                <Button
                    style={tw`w-[33.3%] rounded-full`}
                    onPress={handleNext}
                    nextImage
                >
                    Next
                </Button>
            </View>
        </View>
    );
};

export default ActivityLevel;

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#F2F2F7',
        height: '100%',
    },
    allCards: {
        flexDirection: 'column',
    },
    card: {
        marginTop: 10,
    },
    itemCard: {
        backgroundColor: '#ffffff',
        borderRadius: 50,
        height: 70,
        flexDirection: 'row',
        marginBottom: 15,
        alignItems: 'center',
        justifyContent: 'center',
    },
    itemText: {
        color: '#000000',
        fontFamily: 'Helvetica',
        // fontSize: 24,
        // marginLeft: 100,
        textAlign: 'center',
        fontWeight: '500',
    },
    selectedCard: {
        borderColor: '#000000',
        borderWidth: 2,
    },
    // activityCard: {
    //     paddingVertical: 30,
    //     paddingHorizontal: 40,
    //     backgroundColor: '#1E1E1E',
    //     borderRadius: 30,
    //     height: 401,
    // },
    heading: {
        fontFamily: 'HelveticaNeue-Bold',
        // fontSize: 30,
        textAlign: 'center',
        // color: '#FFFFFF',
    },
    button: {
        backgroundColor: '#D1FF70',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        // fontSize: 18,
        color: '#000000',
        fontFamily: 'HelveticaNeue-Bold',
        lineHeight: 21,
    },
    buttonImage: {
        width: 9,
        height: 15,
        marginLeft: 15,
    },
    imageButtonContainer: {
        flexDirection: 'row',

        justifyContent: 'space-between',
    },
    backImage: {
        height: 54,
        width: 54,
    },
    optionCard: {
        paddingVertical: 14,
        paddingHorizontal: 25,
        backgroundColor: '#4E4E4E',
        opacity: 100,
        borderRadius: 30,
        height: 60,
        flexDirection: 'row',
    },
    activitymage: {
        height: 30,
        width: 32,
        marginRight: 36,
    },
    activitymage2: {
        height: 32,
        width: 32,
        marginRight: 36,
    },
    activitymage3: {
        height: 35,
        width: 35,
        marginRight: 36,
    },
});
