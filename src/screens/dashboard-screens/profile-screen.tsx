import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import {
    Image,
    LayoutAnimation,
    Platform,
    Text,
    TouchableOpacity,
    UIManager,
    View,
} from 'react-native';
import { Logout } from '~/redux/actions/auth-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import {
    ACTIVITY_LEVEL,
    BASIC_INFORMATION_SCREEN,
    MEASUREMENT_SCREEN,
    WEIGHT_SCREEN,
} from '~/constants/navigation-constant';

import ProfileHeader from '~/components/common/profile-header';

import { useAppDispatch } from '~/hooks/redux-hooks';

const scheduleData = [
    {
        tabName: 'Upcoming',
        content: `You don't have anything booked`,
    },
    {
        tabName: 'Previous',
        content: `You don't have anything booked`,
    },
];

const data = [
    {
        id: 1,
        type: 'Basic Information',
        screenName: BASIC_INFORMATION_SCREEN,
    },
    {
        id: 2,
        type: 'Height Weight',
        screenName: WEIGHT_SCREEN,
    },
    {
        id: 3,
        type: 'activity level',
        screenName: ACTIVITY_LEVEL,
    },
    {
        id: 4,
        type: 'measurement',
        screenName: MEASUREMENT_SCREEN,
    },
    // {
    //     id: 5,
    //     type: 'reviews',
    //     screenName: WEIGHT_SCREEN,
    // },
    // {
    //     id: 6,
    //     type: 'contact',
    //     screenName: WEIGHT_SCREEN,
    // },
    // {
    //     id: 7,
    //     type: 'notification',
    //     screenName: WEIGHT_SCREEN,
    // },
    // {
    //     id: 8,
    //     type: 'setting',
    //     screenName: WEIGHT_SCREEN,
    // },
    {
        id: 9,
        type: 'sign out',
        screenName: 'Sign Out',
    },
];

if (
    Platform.OS === 'android' &&
    UIManager.setLayoutAnimationEnabledExperimental
) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
}

const ProfileScreen = ({ navigation }: any) => {
    const dispatch = useAppDispatch();
    function handleNavigation(item: any) {
        if (item.type === 'sign out') {
            dispatch(Logout());
        } else {
            navigation.navigate(item.screenName);
        }
    }
    const [selected, setSelected] = useState('Profile');

    const [showDetails, setShowDetails] = useState(null);

    const handleShowHide = (index: any) => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setShowDetails(showDetails === index ? null : index);
    };
    // const navigation = useNavigation();

    return (
        <>
            <ProfileHeader />

            <View style={tw`bg-[#fafafa] pt-10 `}>
                <View
                    style={tw`flex flex-row justify-start mb-10 items-center border-[#2C2C2E4D] border-b`}
                >
                    <TouchableOpacity onPress={() => setSelected('Profile')}>
                        <Text
                            style={tw.style(
                                `font-bold text-[20px] w-36 border-b-2 text-center pb-1`,
                                selected === 'Profile'
                                    ? 'border-[#D9FD51]'
                                    : 'border-transparent' // Change border color
                            )}
                        >
                            Profile
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setSelected('Schedule')}>
                        <Text
                            style={tw.style(
                                `font-bold text-[20px] w-36 border-b-2 text-center pb-1`,
                                selected === 'Schedule'
                                    ? 'border-[#D9FD51]'
                                    : 'border-transparent' // Change border color
                            )}
                        >
                            Schedule
                        </Text>
                    </TouchableOpacity>
                </View>
                {selected === 'Profile' ? (
                    <>
                        {data.map((item) => {
                            return (
                                <TouchableOpacity
                                    key={item.id}
                                    style={tw`flex flex-row gap-3 items-center px-5 py-3 border-b border-[#4555604D]`}
                                    onPress={() => handleNavigation(item)}
                                >
                                    <Text
                                        style={tw`text-[#455560] uppercase text-[15px] font-bold`}
                                    >
                                        {item.type}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                    </>
                ) : (
                    <>
                        <View>
                            {scheduleData.map((data, index) => (
                                <View
                                    key={index}
                                    style={tw`bg-[P#FAFAFA] p-4 rounded-lg `}
                                >
                                    <TouchableOpacity
                                        style={tw``}
                                        onPress={() => handleShowHide(index)}
                                    >
                                        <View
                                            style={tw`flex flex-row justify-between items-center border-b pb-2 border-[#D8D8D8]`}
                                        >
                                            <Text
                                                style={tw`text-14 text-[#455560] font-bold px-2`}
                                            >
                                                {data.tabName}
                                            </Text>
                                            <View style={tw``}>
                                                {showDetails === index ? (
                                                    <Image
                                                        style={tw` w-6 h-6`}
                                                        source={Asset.DownArrow}
                                                        resizeMode="cover"
                                                    />
                                                ) : (
                                                    <Image
                                                        style={tw` w-6 h-6`}
                                                        source={Asset.UpArrows}
                                                    />
                                                )}
                                            </View>
                                        </View>
                                    </TouchableOpacity>

                                    {showDetails === index && (
                                        <>
                                            <View
                                                style={tw`bg-[#F5F5F5] mt-5 p-5 rounded-lg`}
                                            >
                                                <Text
                                                    style={tw`text-center text-black`}
                                                >
                                                    {data.content}
                                                </Text>
                                            </View>
                                            <TouchableOpacity
                                                onPress={() =>
                                                    navigation.navigate(
                                                        'BOOK_CLASSES'
                                                    )
                                                }
                                                style={tw`mt-5 bg-[#D9FD51] p-5 rounded-full`}
                                            >
                                                <Text
                                                    style={tw`text-center text-black text-md font-semibold`}
                                                >
                                                    Explore
                                                </Text>
                                            </TouchableOpacity>
                                        </>
                                    )}
                                </View>
                            ))}
                        </View>
                    </>
                )}
            </View>
        </>
    );
};

export default ProfileScreen;
