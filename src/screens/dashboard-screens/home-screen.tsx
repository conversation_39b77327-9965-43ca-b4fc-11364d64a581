import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import {
    Button,
    Image,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    FacilityDetails,
    GetAllFacilitiesByStaffId,
} from '~/redux/actions/facility-actions';

import tw from '~/styles/tailwind';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

import CheckInGraph from '../facility/check-in-graph';
import SalesGraph from '../facility/sales-graph';

// Add this if not already

const App = () => {
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        userId: state.auth_store.userId,
        facilityId: state.auth_store.facilityId,
        facilityDetailData: state.facility_store.facilityDetailsData,
    }));
    const [loader, startLoader, endLoader] = useLoader();

    const [activeTab, setActiveTab] = useState<'sales' | 'checkins'>('sales');

    useFocusEffect(
        useCallback(() => {
            if (store.facilityId) {
                startLoader();
                dispatch(FacilityDetails({ facilityId: store.facilityId }))
                    .unwrap()
                    .then(() => {})
                    .catch(() => {})
                    .finally(endLoader);
            }
        }, [])
    );

    return (
        <ScrollView>
            {/* Tab Buttons */}
            <View style={tw`flex flex-row items-center px-5 gap-4 py-5`}>
                <View style={tw`w-[30%]`}>
                    <Image
                        style={tw`w-24 h-24 rounded-full `}
                        source={{
                            uri: store.facilityDetailData?.profilePicture,
                        }}
                    />
                </View>
                <View style={tw`w-[70%] flex flex-col gap-1 pr-5`}>
                    <Text style={tw`text-xl text-MainTextColor font-bold`}>
                        {store.facilityDetailData?.facilityName}
                    </Text>
                    <Text style={tw`text-lg text-MainTextColor leading-5`}>
                        {`${store.facilityDetailData?.cityName?.[0]}, ${store.facilityDetailData?.stateName?.[0]}, ${store.facilityDetailData?.address?.postalCode}`}
                    </Text>
                </View>
            </View>
            <View style={tw`flex flex-row gap-2  p-5`}>
                <TouchableOpacity
                    onPress={() => setActiveTab('sales')}
                    style={[
                        tw` w-[30%] flex justify-center items-center ${
                            activeTab === 'sales'
                                ? ''
                                : 'border border-[#455560] border-opacity-20'
                        }  `,
                        {
                            backgroundColor:
                                activeTab === 'sales' ? '#8143D1' : '#f0f0f0',
                            padding: 10,
                            marginHorizontal: 5,
                            borderRadius: 5,
                        },
                    ]}
                >
                    <Text
                        style={{
                            color: activeTab === 'sales' ? '#fff' : '#455560',
                        }}
                    >
                        Sales
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => setActiveTab('checkins')}
                    style={[
                        tw` w-[30%] flex justify-center items-center ${
                            activeTab === 'checkins'
                                ? ''
                                : 'border border-[#455560] border-opacity-20'
                        }  `,
                        {
                            backgroundColor:
                                activeTab === 'checkins'
                                    ? '#8143D1'
                                    : '#f0f0f0',
                            padding: 10,
                            marginHorizontal: 5,
                            borderRadius: 5,
                        },
                    ]}
                >
                    <Text
                        style={{
                            color:
                                activeTab === 'checkins' ? '#fff' : '#455560',
                        }}
                    >
                        Check Ins
                    </Text>
                </TouchableOpacity>
            </View>

            {/* Tab Content */}
            <View>
                {activeTab === 'sales' ? <SalesGraph /> : <CheckInGraph />}
            </View>
        </ScrollView>
    );
};

export default App;
