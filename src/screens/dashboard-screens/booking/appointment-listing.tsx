import React, { memo, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

import tw from '~/styles/tailwind';

import LocationIcon from '~/assets/svg/location_Icon.svg';

import BackButtonHeading from '~/components/common/back-button-heading';
import HorizontalCalendar from '~/components/common/horizontal-calendar';

import Classes from './Classes';
import AppointmentDetailList from './appointment-detail-list';
import Appointments from './appointments';
import BookingTabs from './booking-tabs';
import Bookings from './bookings';
import Courses from './courses';

const LocationData = [
    { label: 'Ggn' },
    { label: 'Delhi' },
    { label: 'Faridabad' },
];

const AppointmentListing = () => {
    const [activeTab, setActiveTab] = useState('Appointment');

    const handleTabPress = (tab: string) => {
        setActiveTab(tab);
    };

    return (
        <ScrollView>
            <View style={tw`bg-#D0FF01]  h-40 pt-5  px-4 shadow-md`}>
                <View style={tw`flex  gap-8 flex-col h-full`}>
                    <BackButtonHeading Heading={`Book ${activeTab}`} />
                    <View
                        style={tw`flex flex-row justify-between item-center gap-5`}
                    >
                        <View style={tw`flex flex-row  item-center gap-4`}>
                            <LocationIcon />
                            <View style={tw``}>
                                <View
                                    style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                                >
                                    <Dropdown
                                        style={tw`w-[140px]`}
                                        data={LocationData}
                                        labelField="label"
                                        valueField="value"
                                    />
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </View>

            <BookingTabs {...{ activeTab, setActiveTab, handleTabPress }} />
            <HorizontalCalendar />

            {/* Tab content */}
            <View style={tw``}>
                {activeTab === 'Classes' && <Classes />}
                {activeTab === 'Appointment' && <AppointmentDetailList />}
                {activeTab === 'Bookings' && <Bookings />}
                {activeTab === 'Courses' && <Courses />}
            </View>
        </ScrollView>
    );
};

export default memo(AppointmentListing);
