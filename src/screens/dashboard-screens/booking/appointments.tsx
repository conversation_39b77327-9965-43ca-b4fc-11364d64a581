import { useFocusEffect } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import React, { act, memo, useCallback, useState } from 'react';
import {
    Image,
    LayoutAnimation,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { ServiceCategoryList } from '~/redux/actions/booking-action';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { ClassType } from '~/constants/enums';
import { APPOINTMENT_LISTING } from '~/constants/navigation-constant';

import { capitalizeFirstLetter } from '~/components/common/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

const AppointmentData = [
    {
        id: 1,
        tabName: 'Boxing',
    },
    {
        id: 2,
        tabName: 'Yoga',
    },
    {
        id: 3,
        tabName: 'CrossFit',
    },
    {
        id: 4,
        tabName: 'Pilates',
    },
    {
        id: 5,
        tabName: 'HIIT',
    },
];

const SubEventData = [
    {
        id: 1,
        appointmentId: 1,
        subEventName: 'Boxing Plus',
        subEventDetail: 'Access to all coaches',
        trainerCount: '3',
    },
    {
        id: 2,
        appointmentId: 1,
        subEventName: 'Boxing Basics',
        subEventDetail: 'Introductory boxing lessons',
        trainerCount: '2',
    },
    {
        id: 3,
        appointmentId: 2,
        subEventName: 'Yoga Flex',
        subEventDetail: 'Flexibility and breathing techniques',
        trainerCount: '5',
    },
    {
        id: 4,
        appointmentId: 2,
        subEventName: 'Yoga for Beginners',
        subEventDetail: 'Basic yoga poses and techniques',
        trainerCount: '4',
    },
    {
        id: 5,
        appointmentId: 5,
        subEventName: 'HIIT Pro',
        subEventDetail: 'High-intensity training sessions',
        trainerCount: '4',
    },
    {
        id: 6,
        appointmentId: 3,
        subEventName: 'Strength Max',
        subEventDetail: 'Muscle building with trainers',
        trainerCount: '6',
    },
    {
        id: 7,
        appointmentId: 4,
        subEventName: 'Pilates Core',
        subEventDetail: 'Core strengthening Pilates',
        trainerCount: '2',
    },
];

const Appointments = () => {
    const [showDetails, setShowDetails] = useState(null);
    const dispatch = useAppDispatch();
    const navigation = useNavigation();

    const store = useAppSelector((state) => ({
        serviceCategoryData: state.booking_store.serviceCategoryData,
        serviceCategoryDataCount: state.booking_store.serviceCategoryDataCount,
    }));

    console.log('Store------------------', store.serviceCategoryData);

    const handleShowHide = (index: any) => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setShowDetails(showDetails === index ? null : index);
    };

    useFocusEffect(
        useCallback(() => {
            // startLoaderType();
            dispatch(
                ServiceCategoryList({
                    page: 1,
                    pageSize: 10,
                    classType: ClassType.PERSONAL_APPOINTMENT,
                })
            )
                .unwrap()
                .then(() => {})
                .catch(() => {})
                .finally();
        }, [])
    );

    return (
        <View style={tw``}>
            {store.serviceCategoryData?.map(
                (appointment: any, index: number) => (
                    <View key={index} style={tw` py-4 rounded-lg`}>
                        <TouchableOpacity
                            style={tw``}
                            onPress={() => handleShowHide(index)}
                        >
                            <View
                                style={tw`flex flex-row justify-between items-center ${
                                    showDetails === index ? '' : 'border-b'
                                } pb-2 border-[#D8D8D8]`}
                            >
                                <Text
                                    style={tw`text-lg text-[#455560] font-semibold pl-4 `}
                                >
                                    {capitalizeFirstLetter(appointment.name)}
                                </Text>
                                <View style={tw`pr-4`}>
                                    {showDetails === index ? (
                                        <Image
                                            style={tw`w-4`}
                                            source={Asset.MinusImage}
                                            resizeMode="cover"
                                        />
                                    ) : (
                                        <Image
                                            style={tw`w-4 h-4`}
                                            source={Asset.PLusImage}
                                        />
                                    )}
                                </View>
                            </View>
                        </TouchableOpacity>

                        {showDetails === index &&
                            appointment.appointmentType?.length > 0 && (
                                <View style={tw` rounded-lg px-2 `}>
                                    {appointment?.appointmentType?.map(
                                        (subEvent: any, subIndex: number) => (
                                            <TouchableOpacity
                                                onPress={() =>
                                                    navigation.navigate(
                                                        APPOINTMENT_LISTING
                                                    )
                                                } // Navigate to the desired screen
                                                style={tw`${
                                                    showDetails === index
                                                        ? 'border-b'
                                                        : ''
                                                } border-[#D8D8D8] pb-2 pl-2 py-2`}
                                                key={subEvent.id}
                                            >
                                                <Text
                                                    style={tw`text-[15px] mb-1 text-[#455560]`}
                                                >
                                                    {subEvent.name}
                                                </Text>
                                                <View
                                                    style={tw`flex flex-row items-center gap-2`}
                                                >
                                                    <Text
                                                        style={tw`text-sm text-[#455560]`}
                                                    >
                                                        {`${subEvent?.name} duration ${subEvent.durationInMinutes} min.`}
                                                    </Text>

                                                    <View
                                                        style={tw`flex flex-row justify-center bg-black items-center rounded-full h-[25px] w-[25px]`}
                                                    >
                                                        <Text
                                                            style={tw`text-sm text-[#fff]`}
                                                        >
                                                            {
                                                                subEvent.trainerCount
                                                            }
                                                        </Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        )
                                    )}
                                </View>
                            )}
                    </View>
                )
            )}
        </View>
    );
};

export default memo(Appointments);
