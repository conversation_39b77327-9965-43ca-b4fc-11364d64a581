import { useNavigation } from '@react-navigation/native';
import React, { memo } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

import tw from '~/styles/tailwind';

import { BOOKING_PAYMENT } from '~/constants/navigation-constant';

const StartTime = [
    { label: '07:00 AM' },
    { label: '09:00 AM' },
    { label: '11:00 AM' },
];
const EndTime = [
    { label: '07:00 AM' },
    { label: '09:00 AM' },
    { label: '11:00 AM' },
];
const Bookings = () => {
    const navigation = useNavigation();
    return (
        <View style={tw``}>
            <Text
                style={tw`text-xl text-[#455560] py-3 text-center font-bold uppercase`}
            >
                gym booking
            </Text>
            <View
                style={tw`border-b border-[#7d7d7d] pb-2.5
                `}
            >
                <Text
                    style={tw`text-md text-[#455560] text-center  font-semibold uppercase`}
                >
                    Saturday, August 6 &nbsp;
                    <Text style={tw`font-normal`}>(60 Min)</Text>
                </Text>
            </View>

            <View>
                <Text style={tw`text-base pt-5 pb-4 px-4`}>Details</Text>
                <View
                    style={tw`flex flex-row justify-between border-b border-[#7d7d7d] pb-4`}
                >
                    <Text style={tw`text-[15px] font-semibold px-4`}>
                        Choose Start Time
                    </Text>
                    <View
                        style={tw`w-[144px] border border-gray-400 rounded-full mr-4 px-1`}
                    >
                        <Dropdown
                            style={tw`w-[140px] h-7`}
                            data={StartTime}
                            placeholder="Select Time"
                            labelField="label"
                            valueField="value"
                            fontSize={12}
                            selectedTextStyle={{ fontSize: 12 }}
                            itemTextStyle={{ fontSize: 12 }}
                            placeholderStyle={{ fontSize: 12 }}
                        />
                    </View>
                </View>
            </View>
            <View>
                <View
                    style={tw`flex flex-row justify-between border-b border-[#7d7d7d] pt-8 pb-4`}
                >
                    <Text style={tw`text-[15px] font-semibold px-4`}>
                        End Time
                    </Text>
                    <View
                        style={tw`w-[144px] border border-gray-400 rounded-full mr-4 px-1`}
                    >
                        <Dropdown
                            style={tw`w-[140px] h-7`}
                            data={EndTime}
                            placeholder="Select Time"
                            labelField="label"
                            valueField="value"
                            fontSize={12}
                            selectedTextStyle={{ fontSize: 12 }}
                            itemTextStyle={{ fontSize: 12 }}
                            placeholderStyle={{ fontSize: 12 }}
                        />
                    </View>
                </View>
            </View>
            <View>
                <View
                    style={tw`flex flex-row justify-between border-b border-[#7d7d7d] pt-8 pb-4`}
                >
                    <Text style={tw`text-[15px] font-semibold px-4`}>
                        Duration
                    </Text>
                    <View
                        style={tw`w-[144px]  border border-gray-400 rounded-full mr-4`}
                    >
                        <TextInput
                            style={tw` text-xs h-7 p-0 px-1.5`}
                            placeholder="Enter Duration"
                            keyboardType="numeric"
                        />
                    </View>
                </View>
            </View>
            <View>
                <View
                    style={tw`flex flex-col gap-2 border-b border-[#7d7d7d] pt-8 pb-4`}
                >
                    <Text style={tw`text-[15px] font-semibold px-4`}>
                        Notes
                    </Text>
                    <Text style={tw`text-[13px] font-normal px-4`}>
                        Lorem ipsum dolor sit amet consectetur. Bibendum sed
                        egestas consequat quis maecenas dolor lectus commodo.
                        Gravida massa nulla cur
                    </Text>
                </View>
            </View>

            <View style={tw` flex flex-row justify-center py-3`}>
                <TouchableOpacity
                    onPress={() => navigation.navigate('BOOKING_PAYMENT')}
                    style={tw`mt-5 w-[60%] bg-[#D9FD51] p-4 rounded-full `}
                >
                    <Text style={tw`text-center text-black text-md font-bold`}>
                        Confirm Booking
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default memo(Bookings);
