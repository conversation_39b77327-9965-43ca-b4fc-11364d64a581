import { useNavigation } from '@react-navigation/native';
import React, { memo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

const TodaySessionData = [
    {
        id: 1,
        time: '7:00 am',
        duration: '60 min',
        instructor: '<PERSON><PERSON><PERSON>',
        classType: 'Morning Group Yoga',
        date: '06-SEPT',
    },
    {
        id: 2,
        time: '8:30 am',
        duration: '45 min',
        instructor: '<PERSON><PERSON><PERSON>',
        classType: 'Cardio Blast',
        date: '06-SEPT',
    },
    {
        id: 3,
        time: '10:00 am',
        duration: '90 min',
        instructor: '<PERSON><PERSON>',
        classType: 'Strength Training',
        date: '06-SEPT',
    },
];
const UpcomingSessionData = [
    {
        id: 1,
        time: '7:00 am',
        duration: '60 min',
        instructor: '<PERSON><PERSON><PERSON>',
        classType: 'Morning Group Yoga',
        date: '06-SEPT',
    },
    {
        id: 2,
        time: '8:00 am',
        duration: '45 min',
        instructor: '<PERSON><PERSON><PERSON>',
        classType: 'Cardio Blast',
        date: '06-SEPT',
    },
    {
        id: 3,
        time: '9:30 am',
        duration: '90 min',
        instructor: '<PERSON><PERSON>',
        classType: 'Strength Training',
        date: '06-SEPT',
    },
    {
        id: 4,
        time: '11:00 am',
        duration: '30 min',
        instructor: 'Neha Sharma',
        classType: 'Quick Abs Workout',
        date: '06-SEPT',
    },
    {
        id: 5,
        time: '5:00 pm',
        duration: '60 min',
        instructor: 'Vikram Singh',
        classType: 'Evening Meditation',
        date: '06-SEPT',
    },
];

const Courses = () => {
    const navigation = useNavigation();

    return (
        <View style={tw``}>
            <Text style={tw`text-lg px-4 py-2 text-[#455560] font-bold `}>
                {`Today's Session(s)`}
            </Text>

            {TodaySessionData.map((item, index) => {
                return (
                    <TouchableOpacity
                        onPress={() => navigation.navigate('CLASSES_DETAIL')}
                    >
                        <View
                            key={index}
                            style={tw`flex flex-row gap-4 items-center border-b border-[#7d7d7d] py-3`}
                        >
                            <View
                                style={tw`flex flex-col gap-1.5 pl-4 w-[30%]`}
                            >
                                <Text
                                    style={tw`text-sm text-[#455560]  uppercase`}
                                >
                                    {item.date}
                                </Text>
                                <Text
                                    style={tw`text-sm text-[#455560] font-bold uppercase`}
                                >
                                    {item.time}
                                </Text>
                                <Text
                                    style={tw`text-sm text-[#455560] uppercase`}
                                >
                                    {item.duration}
                                </Text>
                            </View>
                            <View style={tw`flex flex-col w-[70%] gap-1.5`}>
                                <Text
                                    style={tw`text-sm text-[#455560] font-bold uppercase`}
                                >
                                    {item.classType}
                                </Text>
                                <Text style={tw`text-sm text-[#455560] `}>
                                    {item.instructor}
                                </Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                );
            })}

            <View style={tw`py-5`}>
                <Text style={tw`text-lg px-4 py-2 text-[#455560] font-bold `}>
                    {`Upcoming Session(s)`}
                </Text>
                {UpcomingSessionData.map((item, index) => {
                    return (
                        <View
                            key={index}
                            style={tw`flex flex-row gap-4 items-center border-b border-[#7d7d7d] py-3`}
                        >
                            <View
                                style={tw`flex flex-col gap-1.5 pl-4 w-[30%]`}
                            >
                                <Text
                                    style={tw`text-sm text-[#455560]  uppercase`}
                                >
                                    {item.date}
                                </Text>
                                <Text
                                    style={tw`text-sm text-[#455560] font-bold uppercase`}
                                >
                                    {item.time}
                                </Text>
                                <Text
                                    style={tw`text-sm text-[#455560] uppercase`}
                                >
                                    {item.duration}
                                </Text>
                            </View>
                            <View style={tw`flex flex-col w-[70%] gap-1.5`}>
                                <Text
                                    style={tw`text-sm text-[#455560] font-bold uppercase`}
                                >
                                    {item.classType}
                                </Text>
                                <Text style={tw`text-sm text-[#455560] `}>
                                    {item.instructor}
                                </Text>
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

export default memo(Courses);
