import React, { memo, useState } from 'react';
import {
    Image,
    LayoutAnimation,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

const scheduleData = [
    {
        tabName: 'Upcoming',
        content: `You don't have anything booked`,
    },
    {
        tabName: 'Previous',
        content: `You don't have anything booked`,
    },
];

const BookingPayment = () => {
    const [showDetails, setShowDetails] = useState(null);

    const handleShowHide = (index) => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setShowDetails(showDetails === index ? null : index);
    };
    return (
        <View style={tw``}>
            <View
                style={tw`bg-#D0FF01]  h-26 flex flex-col justify-center  px-4 shadow-md`}
            >
                <BackButtonHeading Heading={'Payment Methods'} />
            </View>
            <View
                style={tw`flex flex-row py-7  border-b border-[#7d7d7d] justify-between px-6`}
            >
                <Text style={tw`text-[#455560] text-[18px] font-bold `}>
                    Total Payble
                </Text>
                <Text style={tw`text-[#455560] text-[18px] font-bold `}>
                    ₹ 518.00
                </Text>
            </View>
            {scheduleData.map((data, index) => (
                <View key={index} style={tw`bg-[P#FAFAFA] p-4 rounded-lg `}>
                    <TouchableOpacity
                        style={tw``}
                        onPress={() => handleShowHide(index)}
                    >
                        <View
                            style={tw`flex flex-row justify-between items-center border-b pb-2 border-[#D8D8D8]`}
                        >
                            <Text
                                style={tw`text-14 text-[#455560] font-bold px-2`}
                            >
                                {data.tabName}
                            </Text>
                            <View style={tw``}>
                                {showDetails === index ? (
                                    <Image
                                        style={tw` w-6 h-6`}
                                        source={Asset.DownArrow}
                                        resizeMode="cover"
                                    />
                                ) : (
                                    <Image
                                        style={tw` w-6 h-6`}
                                        source={Asset.UpArrows}
                                    />
                                )}
                            </View>
                        </View>
                    </TouchableOpacity>

                    {showDetails === index && (
                        <>
                            <View style={tw`bg-[#F5F5F5] mt-5 p-5 rounded-lg`}>
                                <Text style={tw`text-center text-black`}>
                                    {data.content}
                                </Text>
                            </View>
                            <TouchableOpacity
                                style={tw`mt-5 bg-[#D9FD51] p-5 rounded-full`}
                            >
                                <Text
                                    style={tw`text-center text-black text-md font-semibold`}
                                >
                                    Explore
                                </Text>
                            </TouchableOpacity>
                        </>
                    )}
                </View>
            ))}
        </View>
    );
};

export default memo(BookingPayment);
