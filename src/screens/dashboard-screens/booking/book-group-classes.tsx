import {
    useFocusEffect,
    useNavigation,
    useRoute,
} from '@react-navigation/native';
import dayjs from 'dayjs';
import React, { useCallback, useMemo, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';
import tw from 'twrnc';
import {
    courseListForPackage,
    roomListingByScheduling,
    roomListingByServiceCategory,
    serviceCategorybyOrganization,
} from '~/redux/actions/booking-action';
import {
    BookedCalendarData,
    ClassesSchedulingDetails,
    createClassesScheduling,
    updateClassesScheduling,
} from '~/redux/actions/scheduling-actions';
import { GetStaffListBySubType } from '~/redux/actions/staff-actions';
import {
    ClearBookingType,
    SetBookingType,
} from '~/redux/slices/scheduling-slice';

import { Asset } from '~/assets';

import { ClassType, UserRole } from '~/constants/enums';

import { Button, TextInput } from '~/components/atoms';
import TimePicker from '~/components/common/time-picker';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { navigationRef } from '~/hooks/useLocation';

const BookGropuClassesScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { type, scheduleId, isDetails, bookClassType }: any =
        route.params || {};
    const dispatch = useAppDispatch();
    const [formData, setFormData] = useState<any>({
        location: null,
        client: null,
        trainer: null,
        phone: '',
        email: '',
        package: null,
        classCapacity: null,
        serviceCategory: null,
        room: null,
        startDate: new Date(),
        startTime: '',
        endTime: '',
        duration: null,
        notes: '',
        payLater: false,
        confirmation: false,
    });
    const [errors, setErrors] = useState<any>({});
    const [startDate, setStartDate] = useState(new Date());
    const [location, setLocationState] = useState<string | null>(null);
    const [openStartDatePicker, setOpenStartDatePicker] = useState(false);
    const [durationOptions, setDurationOptions] = useState<any>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const debouncedRequest = useDebounce((callback: any) => callback(), 300);
    const [isFormInitialized, setIsFormInitialized] = useState(false);

    const isViewMode: any = type === 'view';
    const isEditMode: any = type === 'edit';

    const store = useAppSelector((state) => ({
        userRole: state.auth_store.role,
        userId: state.auth_store.userId,
        organizationId: state.auth_store.organizationId,
        facilityList: state.facility_store.facilityList || [],
        selectedFacility: state.facility_store.facility?._id,
        selectedTrainer: state.scheduling_store.selectedTrainer,
        classTypes: state.facility_store.classTypes,
        customerList: state.client_store.clientListByStaff,
        customeDetails: state.client_store.clientDetails,
        pricingByUserAndSubType: state.booking_store.pricingByUserAndSubType,
        serviceCategoryByPackage:
            state.booking_store.servicecategoryByPricingList,
        roomList: state.booking_store.roomListByScheduling,
        serviceCategoryByOrganization:
            state.booking_store.servicecategoryByOrganization,
        serviceCategoryByOrganizationCount:
            state.booking_store.servicecategoryByOrganizationCount,
        staffListBySubType: state.staff_store.staffListBySubType,
        courseLitsForPackage: state.booking_store.courseListForPackage,
        courseLitsForPackageCount:
            state.booking_store.courseListForPackageCount,
    }));

    console.log('Store--------------', store.courseLitsForPackage);

    useFocusEffect(
        useCallback(() => {
            if (store.selectedFacility) {
                setFormData((prev: any) => ({
                    ...prev,
                    location: store.selectedFacility,
                }));
            }
        }, [store.selectedFacility])
    );

    useFocusEffect(
        useCallback(() => {
            if (!scheduleId) {
                dispatch(ClearBookingType());
            }
        }, [scheduleId])
    );

    const transformServiceTypeData = () => {
        return (
            store.serviceCategoryByOrganization?.flatMap((category: any) => [
                {
                    label: category.name,
                    value: category._id,
                    type: 'category',
                    isSelectable: true,
                },
                ...category.appointmentType.map((subType: any) => ({
                    label: `${subType.name} - ${subType.durationInMinutes} min`,
                    value: subType._id,
                    type: 'subType',
                    categoryId: category._id,
                    duration: subType.durationInMinutes,
                    isSelectable: false,
                })),
            ]) || []
        );
    };

    useFocusEffect(
        useCallback(() => {
            const fetchSchedulingDetails = async () => {
                if (scheduleId && (isViewMode || isEditMode)) {
                    try {
                        await dispatch(
                            ClassesSchedulingDetails({ scheduleId })
                        ).then((res: any) => {
                            const scheduleData = res.payload?.data?.data;

                            console.log(
                                'Res------ ddvdfdgdgd dsfdfsfs -------',
                                scheduleData
                            );

                            if (scheduleData) {
                                const selectedSubType =
                                    transformServiceTypeData().find(
                                        (item: any) =>
                                            item.type === 'subType' &&
                                            item.value ===
                                                scheduleData.subType?._id
                                    );

                                let newDurationOptions = [];
                                if (selectedSubType) {
                                    for (let i = 1; i <= 5; i++) {
                                        newDurationOptions.push({
                                            label: `${
                                                selectedSubType.duration * i
                                            } min`,
                                            value: selectedSubType.duration * i,
                                        });
                                    }
                                }
                                setDurationOptions(newDurationOptions);
                                dispatch(
                                    SetBookingType(scheduleData.classType)
                                );

                                const payload = {
                                    classType: ClassType.CLASSES,
                                    facilityId: scheduleData.facility?._id,
                                    serviceId:
                                        scheduleData.serviceCategory?._id,
                                    subTypeId: scheduleData.subType?._id,
                                    date: dayjs(scheduleData.date)
                                        .startOf('day')
                                        .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                                    startTime: scheduleData.from,
                                    endTime: scheduleData.to,
                                };

                                if (store.userRole !== UserRole.Trainer) {
                                    dispatch(
                                        GetStaffListBySubType({ payload })
                                    );
                                }

                                if (
                                    scheduleData.serviceCategory &&
                                    scheduleData.facility &&
                                    scheduleData.date &&
                                    scheduleData.to
                                ) {
                                    const reqData = {
                                        serviceId:
                                            scheduleData.serviceCategory?._id,
                                        facilityId: scheduleData.facility?._id,
                                        classType: ClassType.CLASSES,
                                        date: dayjs(scheduleData.date).format(
                                            'YYYY-MM-DDTHH:mm:ss[Z]'
                                        ),
                                        startTime: scheduleData.from,
                                        endTime: scheduleData.to,
                                    };

                                    dispatch(roomListingByScheduling(reqData));
                                }
                                setFormData({
                                    location:
                                        scheduleData.facility?._id || null,
                                    package: scheduleData.courseId || null,
                                    classCapacity:
                                        scheduleData.capacity?.toString() || '',
                                    serviceCategory:
                                        scheduleData.serviceCategory?._id ||
                                        null,
                                    subType: scheduleData.subType?._id || null,
                                    room: scheduleData.room?._id || null,
                                    trainer: scheduleData.trainer?._id || null,
                                    startDate: dayjs(
                                        scheduleData.date
                                    ).toDate(),
                                    startTime: scheduleData.from || '',
                                    endTime: scheduleData.to || '',
                                    duration: scheduleData.duration || '',
                                    notes: scheduleData.notes || '',
                                });

                                setStartDate(dayjs(scheduleData.date).toDate());
                                setIsFormInitialized(true);
                            }
                        });
                    } catch (error) {
                        console.error(
                            'Error fetching scheduling details:',
                            error
                        );
                    } finally {
                        // stopLoader(); Uncomment and implement if you have a loader function
                    }
                }
            };

            fetchSchedulingDetails();
        }, [scheduleId, type])
    );

    const facilityOptions = store.facilityList?.map((facility: any) => ({
        label: facility.name,
        value: facility._id,
    }));

    const staffOption = store.staffListBySubType?.map((staff: any) => ({
        label: `${staff.firstName}  ${staff.lastName}`,
        value: staff._id,
    }));

    const packageOptions =
        store.courseLitsForPackage?.map((pkg: any) => ({
            label: pkg.name,
            value: pkg._id,
            ...pkg,
        })) || [];

    const roomOptions = store.roomList?.map((room: any) => ({
        label: room.roomName,
        value: room._id,
    }));

    console.log('Reoomssjncvsjnvc-------', roomOptions);

    useFocusEffect(
        useCallback(() => {
            if (scheduleId && (isViewMode || isEditMode)) return;
            if (store.roomList && store.roomList?.length > 0) {
                const defaultRoom = store.roomList?.[0];
                setFormData((prev: any) => ({
                    ...prev,
                    room: defaultRoom?._id,
                }));
            }
        }, [store.roomList])
    );

    const DatePickerComponent = ({
        label,
        date,
        onDateChange,
        open,
        setOpen,
    }: {
        label: any;
        date: Date;
        onDateChange: (date: Date) => void;
        open: boolean;
        setOpen: (open: boolean) => void;
    }) => (
        <View style={tw`w-[45%]`}>
            <Text style={tw`text-black  pb-2`}>{label}</Text>
            <TouchableOpacity
                style={tw`w-[100%] h-[32px] flex justify-center px-1`}
                onPress={() => setOpen(true)}
            >
                <Text style={tw`text-[#455560] text-[14px]`}>
                    {dayjs(date).format('MMM D, YYYY')}
                </Text>
                <DatePicker
                    modal
                    open={open}
                    date={date}
                    onConfirm={(date) => {
                        setOpen(false);
                        onDateChange(date);
                    }}
                    onCancel={() => setOpen(false)}
                    mode="date"
                />
            </TouchableOpacity>
        </View>
    );

    useFocusEffect(
        useCallback(() => {
            const reqData = {
                // organizationId: store.organizationId,
                classType: ClassType.CLASSES,
                pageSize: 50,
                page: 1,
            };

            dispatch(serviceCategorybyOrganization({ reqData }));
            const payload = {
                page: 1,
                pageSize: 50,
            };
            dispatch(courseListForPackage(payload));
        }, [])
    );

    const { client, subType, serviceCategory } = formData;

    useFocusEffect(
        useCallback(() => {
            if (isViewMode) return;
            const { startTime, endTime, subType, serviceCategory, location } =
                formData;

            if (store.userRole !== UserRole.Trainer) {
                if (
                    startTime &&
                    endTime &&
                    subType &&
                    serviceCategory &&
                    startDate
                ) {
                    const formattedStart = dayjs(
                        `${dayjs(startDate).format('YYYY-MM-DD')} ${startTime}`,
                        'YYYY-MM-DD HH:mm'
                    );
                    const formattedEnd = dayjs(
                        `${dayjs(startDate).format('YYYY-MM-DD')} ${endTime}`,
                        'YYYY-MM-DD HH:mm'
                    );

                    if (formattedEnd.isAfter(formattedStart)) {
                        const payload = {
                            classType: ClassType.CLASSES,
                            facilityId: location,
                            serviceId: serviceCategory,
                            subTypeId: subType,
                            date: dayjs(startDate)
                                .startOf('day')
                                .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                            startTime,
                            endTime,
                        };

                        dispatch(GetStaffListBySubType({ payload })).then(
                            (res: any) => {
                                console.log('Res-----------', res);
                                const staffList = res?.payload?.res?.data?.data;
                                if (staffList && staffList.length > 0) {
                                    setFormData((prev: any) => ({
                                        ...prev,
                                        trainer: staffList[0]._id,
                                    }));
                                }
                            }
                        );
                    } else {
                        console.warn(
                            'End time is not after start time — skipping API call'
                        );
                    }
                }
            }
        }, [
            formData.startTime,
            formData.endTime,
            formData.subType,
            formData.serviceCategory,
            startDate,
        ])
    );

    useFocusEffect(
        useCallback(() => {
            if (!formData.startTime) {
                const now = dayjs();
                const rounded =
                    now.minute() <= 45
                        ? now
                              .minute(Math.ceil(now.minute() / 15) * 15)
                              .second(0)
                        : now.add(1, 'hour').minute(0).second(0);

                const formatted = rounded.format('HH:mm');

                setFormData((prev: any) => ({
                    ...prev,
                    startTime: formatted,
                }));
            }
        }, [])
    );

    useFocusEffect(
        useCallback(() => {
            if (isViewMode) return;
            const { startTime, endTime, subType, serviceCategory, location } =
                formData;

            console.log('Form data------------------', formData);

            if (
                startTime &&
                endTime &&
                startDate &&
                subType &&
                serviceCategory &&
                location
            ) {
                const payload = {
                    classType: ClassType.CLASSES,
                    facilityId: location,
                    serviceId: serviceCategory,
                    date: dayjs(startDate)
                        .startOf('day')
                        .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                    startTime,
                    endTime,
                };

                dispatch(roomListingByScheduling(payload));
            }
        }, [
            formData.startTime,
            formData.endTime,
            formData.subType,
            formData.serviceCategory,
            startDate,
            formData.location,
        ])
    );

    const handleChange = (field: string, value: any) => {
        setFormData((prev: any) => ({
            ...prev,
            [field]: value,
        }));

        if (errors[field]) {
            setErrors((prevErrors: any) => ({
                ...prevErrors,
                [field]: '',
            }));
        }

        if (field === 'package') {
            setFormData((prev: any) => ({
                ...prev,
                remainingSession: null,
                // startTime: '',
                // endTime: '',
            }));

            const selectedPackage = store.pricingByUserAndSubType?.find(
                (pkg: any) => pkg._id === value
            );
            if (selectedPackage) {
                setFormData((prev: any) => ({
                    ...prev,
                    remainingSession: selectedPackage.remainingSession || 0,
                }));
            }
        }

        if (field === 'startTime' || field === 'duration') {
            let startTime = field === 'startTime' ? value : formData.startTime;
            let duration = field === 'duration' ? value : formData.duration;

            if (startTime && duration) {
                const formattedStartDate =
                    dayjs(startDate).format('YYYY-MM-DD');

                const startMoment = dayjs(
                    `${formattedStartDate} ${startTime}`,
                    'YYYY-MM-DD HH:mm'
                );

                if (startMoment.isValid()) {
                    const endMoment = startMoment.add(duration, 'minute');
                    const endTime = endMoment.format('HH:mm');

                    setFormData((prev: any) => ({
                        ...prev,
                        startTime: startTime,
                        duration: duration,
                        endTime: endTime,
                    }));
                } else {
                    setFormData((prev: any) => ({
                        ...prev,
                        endTime: '',
                    }));
                }
            }
        }
    };

    const validateForm = () => {
        let newErrors: any = {};
        if (!formData.location) newErrors.location = 'Location is required';
        if (!formData.subType) newErrors.subType = 'Sub type is required';
        if (!formData.serviceCategory)
            newErrors.serviceCategory = 'Service Category is required';
        if (!formData.startTime) newErrors.startTime = 'Start Time is required';
        if (!formData.duration) newErrors.duration = 'Duration is required';
        if (!formData.classCapacity)
            newErrors.classCapacity = 'Class capacity is required';
        // if (!startDate) newErrors.duration = 'Start Date is required';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const resetFields = () => {
        setFormData({
            location: null,
            trainer: null,
            phone: '',
            email: '',
            package: null,
            serviceCategory: null,
            room: null,
            startDate: new Date(),
            startTime: '',
            endTime: '',
            duration: null,
            classCapacity: null,
            notes: '',
        });
        setStartDate(new Date());
        setErrors({});
    };

    const handleSubmit = () => {
        if (!validateForm()) return;

        startLoader();
        const payload = {
            facilityId: formData.location,
            trainerId:
                store.userRole !== UserRole.Trainer
                    ? formData.trainer
                    : store.userId,
            classType: ClassType.CLASSES,
            ...(formData.room && { roomId: formData.room }),
            ...(formData.notes && { notes: formData.notes }),
            dateRange: 'Single',
            serviceCategory: formData.serviceCategory,
            subType: formData.subType,
            duration: formData.duration,
            date: dayjs(startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: formData.startTime,
            to: formData.endTime,
            // ...(!scheduleId && { checkIn: false }),
            capacity: parseInt(formData.classCapacity, 10),
        };

        console.log('Submitting Payload:', payload);
        if (scheduleId && type) {
            dispatch(
                updateClassesScheduling({
                    payload: { ...payload, scheduleId: scheduleId },
                })
            )
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        resetFields();
                        navigationRef.goBack();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [formData.location],
                            })
                        );
                    }
                })
                .catch(() => {})
                .finally(endLoader);
        } else {
            dispatch(createClassesScheduling({ payload }))
                .unwrap()
                .then((res: any) => {
                    // console.log('Res------------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        resetFields();
                        navigationRef.goBack();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [formData.location],
                            })
                        );
                    }
                })
                .catch(() => {})
                .finally(endLoader);
        }
    };

    console.log('form data----------------', formData);

    return (
        <ScrollView style={tw`flex-1 bg-white`}>
            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Location
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={facilityOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Branch"
                        disable={isViewMode}
                        value={formData?.location}
                        onChange={(item) =>
                            handleChange('location', item.value)
                        }
                        // value={location || store.selectedFacility}
                        // onChange={(item) => setLocationState(item.value)}
                    />
                </View>
                {location ||
                    (store.selectedFacility && (
                        <Text style={tw`text-red-500`}>{errors.location}</Text>
                    ))}
            </View>

            <View style={tw`mt-4`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Service Type
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={transformServiceTypeData()}
                        labelField="label"
                        valueField="value"
                        placeholder="Select service category"
                        disable={isViewMode}
                        value={formData?.subType}
                        // onChange={(item) =>
                        //     handleChange('serviceCategory', item.value)
                        // }
                        renderItem={(item) => (
                            <View
                                style={tw`p-2 ${
                                    item.type === 'category'
                                        ? 'bg-gray-200'
                                        : 'bg-white'
                                }`}
                            >
                                <Text
                                    style={tw`${
                                        item.type === 'category'
                                            ? 'font-bold text-black'
                                            : 'text-gray-700'
                                    }`}
                                >
                                    {item.label}
                                </Text>
                            </View>
                        )}
                        onChange={(item) => {
                            if (item.type === 'category') {
                                setFormData((prev: any) => ({
                                    ...prev,
                                    subType: '',
                                }));
                                return;
                            }

                            if (item.type === 'subType') {
                                const selectedSubTypeDuration = item.duration;
                                const selectedCategoryId = item.categoryId;

                                const generatedDurationOptions = Array.from(
                                    { length: 5 },
                                    (_, i) => ({
                                        label: `${
                                            selectedSubTypeDuration * (i + 1)
                                        } min`,
                                        value:
                                            selectedSubTypeDuration * (i + 1),
                                    })
                                );

                                const defaultDuration =
                                    generatedDurationOptions[0]?.value;

                                let calculatedEndTime = '';
                                if (formData.startTime && defaultDuration) {
                                    const formattedStartDate =
                                        dayjs(startDate).format('YYYY-MM-DD');
                                    const startMoment = dayjs(
                                        `${formattedStartDate} ${formData.startTime}`,
                                        'YYYY-MM-DD HH:mm'
                                    );

                                    if (startMoment.isValid()) {
                                        calculatedEndTime = startMoment
                                            .add(defaultDuration, 'minute')
                                            .format('HH:mm');
                                    }
                                }

                                setFormData((prev: any) => ({
                                    ...prev,
                                    subType: item.value,
                                    serviceCategory: selectedCategoryId,
                                    duration: defaultDuration,
                                    // startTime: '',
                                    endTime: calculatedEndTime,
                                }));

                                setDurationOptions(generatedDurationOptions);

                                // const payload = {
                                //     classType: ClassType.CLASSES,
                                //     facilityId: formData.location,
                                //     serviceId: formData.serviceCategory,
                                //     date: dayjs(formData.startDate)
                                //         .startOf('day')
                                //         .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                                //     startTime: formData.startTime,
                                //     endTime: calculatedEndTime,
                                // };

                                // // Dispatch room listing
                                // dispatch(roomListingByScheduling(payload));
                            }
                        }}
                    />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.serviceCategory}
                    </Text>
                )}
            </View>

            <View style={tw`mt-9`}>
                <View
                    style={tw`w-[100%] flex flex-row justify-between items-end border-b border-[#45556066] pb-1.5`}
                >
                    <DatePickerComponent
                        label={
                            <Text
                                style={tw`text-[#455560] font-medium  text-[13px]`}
                            >
                                Start Date
                            </Text>
                        }
                        date={startDate}
                        onDateChange={setStartDate}
                        open={isViewMode ? false : openStartDatePicker}
                        setOpen={setOpenStartDatePicker}
                    />
                    <Image
                        source={Asset.Calender}
                        style={[tw`w-5 h-5 -translate-y-1`]}
                    />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.subType}
                    </Text>
                )}
            </View>

            <View
                style={tw`mt-5  w-[100%] flex flex-row gap-1 items-start justify-between`}
            >
                <View style={tw`w-[45%]`}>
                    {isViewMode ? (
                        <View style={tw`mt-2 border-b border-gray-300 `}>
                            <Text
                                style={tw`text-[#455560] font-medium mb-2  text-[13px]`}
                            >
                                Start Time
                            </Text>
                            <Text style={tw`text-[#455560] text-[13px] pb-2`}>
                                {formData?.startTime || '--'}
                            </Text>
                        </View>
                    ) : (
                        <TimePicker
                            isOpen
                            placeholder={'Start Time'}
                            title={
                                <Text
                                    style={tw`text-[#455560] font-medium  text-[13px]`}
                                >
                                    Start Time
                                </Text>
                            }
                            value={formData?.startTime}
                            onChange={(value) =>
                                handleChange('startTime', value)
                            }
                        />
                    )}
                    {errors?.startTime && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.startTime}
                        </Text>
                    )}
                </View>
                <View style={tw` w-[45%] pt-2 mt-0.5`}>
                    <Text
                        style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                    >
                        Duration
                    </Text>
                    <View style={tw`w-[100%]  pb-1.5 border-b border-gray-400`}>
                        <Dropdown
                            data={durationOptions}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Duration"
                            disable={isViewMode}
                            value={formData?.duration}
                            onChange={(item) =>
                                handleChange('duration', item.value)
                            }
                        />
                    </View>
                    {errors?.duration && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.duration}
                        </Text>
                    )}
                </View>
            </View>

            <View style={tw`mt-5 flex flex-row gap-1 items-center`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-1`}>
                    End Time :
                </Text>
                <TextInput
                    value={formData?.endTime || ''}
                    editable={false}
                    style={tw``}
                />
            </View>

            {(store.userRole === UserRole.FRONT_DESK_ADMIN ||
                store.userRole === UserRole.ORGANIZATION ||
                store.userRole === UserRole.WEB_MASTER) && (
                <View style={tw`mt-5`}>
                    <Text
                        style={tw`text-[#455560] font-medium  text-[13px] pb-1`}
                    >
                        Staff
                    </Text>
                    <View
                        style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                    >
                        <Dropdown
                            data={staffOption}
                            labelField="label"
                            valueField="value"
                            disable={isViewMode}
                            placeholder="Select Staff"
                            value={formData?.trainer}
                            onChange={(item) =>
                                handleChange('trainer', item.value)
                            }
                        />
                    </View>
                    {errors?.trainer && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.trainer}
                        </Text>
                    )}
                </View>
            )}

            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px]  pb-2`}>
                    Room
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={roomOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Room"
                        disable={isViewMode}
                        value={formData?.room}
                        onChange={(item) => handleChange('room', item.value)}
                    />
                </View>
            </View>

            <View style={tw`mt-5  border-b border-gray-400`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Class Capacity
                </Text>
                <TextInput
                    placeholder="Enter class capacity"
                    editable={isViewMode ? false : true}
                    style={tw``}
                    value={formData?.classCapacity}
                    onChangeText={(text) => {
                        handleChange('classCapacity', text);
                    }}
                />
            </View>
            {errors?.classCapacity && (
                <Text style={tw`text-red-500 text-[11px]`}>
                    {errors?.classCapacity}
                </Text>
            )}

            <View
                style={tw`mt-5 ${
                    isViewMode && 'mb-10'
                } border-b border-gray-400`}
            >
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Notes
                </Text>
                <TextInput
                    placeholder="Enter Notes"
                    editable={isViewMode ? false : true}
                    style={tw``}
                    value={formData?.notes}
                    onChangeText={(text) => {
                        handleChange('notes', text);
                    }}
                />
            </View>

            {!isViewMode && (
                <View
                    style={tw`flex justify-center items-center mt-10 mb-[8%]`}
                >
                    <Button
                        // nextImage
                        style={[tw`w-[70%] rounded-full font-bold`]}
                        onPress={handleSubmit}
                        loading={loader}
                    >
                        {isEditMode ? 'Edit' : 'Save'}
                    </Button>
                </View>
            )}
        </ScrollView>
    );
};

export default BookGropuClassesScreen;
