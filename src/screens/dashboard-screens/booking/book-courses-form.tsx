import {
    useFocusEffect,
    useNavigation,
    useRoute,
} from '@react-navigation/native';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';
import tw from 'twrnc';
import {
    ServiceCategoryListByPackageId,
    activeServiceCategoyListPricing,
    courseListForPackage,
    roomListingByScheduling,
    roomListingByServiceCategory,
    serviceCategorybyOrganization,
} from '~/redux/actions/booking-action';
import {
    BookedCalendarData,
    CourseSchedulingDetails,
    createCoursesScheduling,
    updateCoursesScheduling,
} from '~/redux/actions/scheduling-actions';
import { GetStaffListBySubType } from '~/redux/actions/staff-actions';
import {
    ClearBookingType,
    SetBookingType,
} from '~/redux/slices/scheduling-slice';

import { Asset } from '~/assets';

import { ClassType, UserRole } from '~/constants/enums';

import { Button, TextInput } from '~/components/atoms';
import TimePicker from '~/components/common/time-picker';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { navigationRef } from '~/hooks/useLocation';

const BookCoursesScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { type, scheduleId, isDetails, bookClassType }: any =
        route.params || {};
    const dispatch = useAppDispatch();
    const [formData, setFormData] = useState<any>({
        location: null,
        client: null,
        trainer: null,
        phone: '',
        email: '',
        package: null,
        classCapacity: null,
        serviceCategory: null,
        room: null,
        startDate: new Date(),
        startTime: '',
        endTime: '',
        duration: null,
        notes: '',
        payLater: false,
        confirmation: false,
    });
    const [errors, setErrors] = useState<any>({});
    const [startDate, setStartDate] = useState(new Date());
    const [location, setLocationState] = useState<string | null>(null);
    const [openStartDatePicker, setOpenStartDatePicker] = useState(false);
    const [durationOptions, setDurationOptions] = useState<any>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const debouncedRequest = useDebounce((callback: any) => callback(), 300);
    const [isFormInitialized, setIsFormInitialized] = useState(false);

    const isViewMode: any = type === 'view';
    const isEditMode: any = type === 'edit';

    const store = useAppSelector((state) => ({
        userRole: state.auth_store.role,
        userId: state.auth_store.userId,
        organizationId: state.auth_store.organizationId,
        facilityList: state.facility_store.facilityList || [],
        facilityListByOrg: state.facility_store.facilityListByOrg || [],
        selectedFacility: state.facility_store.facility?._id,
        selectedTrainer: state.scheduling_store.selectedTrainer,
        classTypes: state.facility_store.classTypes,
        customerList: state.client_store.clientListByStaff,
        customeDetails: state.client_store.clientDetails,
        pricingByUserAndSubType: state.booking_store.pricingByUserAndSubType,
        serviceCategoryByPackage:
            state.booking_store.servicecategoryByPricingList,
        roomList: state.booking_store.roomListByScheduling,
        serviceCategoryByOrganization:
            state.booking_store.servicecategoryByOrganization,
        serviceCategoryByOrganizationCount:
            state.booking_store.servicecategoryByOrganizationCount,
        serviceCategoryByPackageId:
            state.booking_store.serviceCategoryByPackage,
        staffListBySubType: state.staff_store.staffListBySubType,
        courseLitsForPackage: state.booking_store.courseListForPackage,
        courseLitsForPackageCount:
            state.booking_store.courseListForPackageCount,
    }));

    console.log('Store--------------', store.courseLitsForPackage);

    useFocusEffect(
        useCallback(() => {
            if (store.selectedFacility) {
                setFormData((prev: any) => ({
                    ...prev,
                    location: store.selectedFacility,
                }));
            }
        }, [store.selectedFacility])
    );

    useFocusEffect(
        useCallback(() => {
            if (!scheduleId) {
                dispatch(ClearBookingType());
            }
        }, [scheduleId])
    );

    useFocusEffect(
        useCallback(() => {
            // const reqData = {
            //     // organizationId: store.organizationId,
            //     classType: ClassType.COURSES,
            //     pageSize: 50,
            //     page: 1,
            // };

            // dispatch(serviceCategorybyOrganization({ reqData }));
            const payload = {
                page: 1,
                pageSize: 50,
                isActive: true,
            };
            dispatch(courseListForPackage(payload))
                .unwrap()
                .then((res: any) => {
                    setFormData((prev: any) => ({
                        ...prev,
                        package: res?.data?.list[0]?._id,
                    }));
                    dispatch(
                        activeServiceCategoyListPricing({
                            packageId: res?.data?.list[0]?._id,
                        })
                    )
                        .unwrap()
                        .then((res: any) => {
                            setFormData((prev: any) => ({
                                ...prev,
                                serviceCategory: res?.data?.data?.list[0]?._id,
                                subType:
                                    res?.data?.data?.list[0]?.appointmentType[0]
                                        ?._id,
                            }));
                        });
                });
        }, [])
    );

    const transformServiceTypeData = () => {
        return (
            store.serviceCategoryByPackageId
                ?.filter(
                    (category: any) =>
                        Array.isArray(category.appointmentType) &&
                        category.appointmentType.length > 0
                )
                ?.flatMap((category: any) => [
                    {
                        label: category.name,
                        value: category._id,
                        type: 'category',
                        isSelectable: true,
                    },
                    ...category.appointmentType.map((subType: any) => ({
                        label: `${subType.name} - ${subType.durationInMinutes} min`,
                        value: subType._id,
                        type: 'subType',
                        categoryId: category._id,
                        duration: subType.durationInMinutes,
                        isSelectable: false,
                    })),
                ]) || []
        );
    };

    console.log(
        'store.serviceCategoryByPackageId----------------------',
        store.serviceCategoryByPackageId
    );

    const fetchSchedulingDetails = async () => {
        try {
            const res: any = await dispatch(
                CourseSchedulingDetails({ scheduleId })
            );

            const scheduleData = res.payload?.data?.data;

            if (!scheduleData) return;

            const selectedSubType = transformServiceTypeData().find(
                (item: any) =>
                    item.type === 'subType' &&
                    item.value === scheduleData.subTypeId
            );

            let newDurationOptions = [];
            if (selectedSubType) {
                for (let i = 1; i <= 5; i++) {
                    newDurationOptions.push({
                        label: `${selectedSubType.duration * i} min`,
                        value: selectedSubType.duration * i,
                    });
                }
            }
            setDurationOptions(newDurationOptions);

            dispatch(SetBookingType(scheduleData.classType));
            dispatch(
                ServiceCategoryListByPackageId({
                    packageId: scheduleData.packageId,
                })
            );
            dispatch(
                activeServiceCategoyListPricing({
                    packageId: scheduleData.packageId,
                })
            );

            const payload = {
                classType: ClassType.COURSES,
                facilityId: scheduleData.facilityId,
                serviceId: scheduleData.serviceCategoryId,
                subTypeId: scheduleData.subTypeId,
                date: dayjs(scheduleData.date)
                    .startOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                startTime: scheduleData.from,
                endTime: scheduleData.to,
            };

            if (store.userRole !== UserRole.Trainer) {
                dispatch(GetStaffListBySubType({ payload }));
            }

            dispatch(
                roomListingByScheduling({
                    serviceId: scheduleData.serviceCategoryId,
                    facilityId: scheduleData.facilityId,
                    classType: ClassType.COURSES,
                    date: dayjs(scheduleData.date).format(
                        'YYYY-MM-DDTHH:mm:ss[Z]'
                    ),
                    startTime: scheduleData.from,
                    endTime: scheduleData.to,
                })
            );

            setFormData({
                location: scheduleData.facilityId || null,
                package: scheduleData.packageId || null,
                classCapacity: scheduleData.classCapacity || '',
                serviceCategory: scheduleData.serviceCategoryId || null,
                subType: scheduleData.subTypeId || null,
                room: scheduleData.roomId || null,
                trainer: scheduleData.trainerId || null,
                startDate: dayjs(scheduleData.date).toDate(),
                startTime: scheduleData.from || '',
                endTime: scheduleData.to || '',
                duration: scheduleData.duration || '',
                notes: scheduleData.notes || '',
            });

            setStartDate(dayjs(scheduleData.date).toDate());
            setIsFormInitialized(true);
        } catch (error) {
            console.error('Error fetching scheduling details:', error);
        }
    };

    useEffect(() => {
        if (
            scheduleId &&
            (isEditMode || isViewMode) &&
            store.serviceCategoryByPackageId &&
            store.serviceCategoryByPackageId.length > 0 &&
            !isFormInitialized
        ) {
            fetchSchedulingDetails();
        }
    }, [scheduleId, isEditMode, isViewMode, store.serviceCategoryByPackageId]);

    // useFocusEffect(
    //     useCallback(() => {
    //         const fetchSchedulingDetails = async () => {
    //             if (
    //                 !store.serviceCategoryByPackageId ||
    //                 store.serviceCategoryByPackageId.length === 0
    //             ) {
    //                 console.log('Service category data is not available yet.');
    //                 return;
    //             }
    //             if (scheduleId && (isViewMode || isEditMode)) {
    //                 try {
    //                     await dispatch(
    //                         CourseSchedulingDetails({ scheduleId })
    //                     ).then((res: any) => {
    //                         const scheduleData = res.payload?.data?.data;

    //                         console.log(
    //                             'Res------ ddvdfdgdgd dsfdfsfs -------',
    //                             scheduleData
    //                         );

    //                         console.log(
    //                             'transformServiceTypeData()---------------',
    //                             transformServiceTypeData()
    //                         );

    //                         if (scheduleData) {
    //                             const selectedSubType =
    //                                 transformServiceTypeData().find(
    //                                     (item: any) =>
    //                                         item.type === 'subType' &&
    //                                         item.value ===
    //                                             scheduleData.subTypeId
    //                                 );

    //                             console.log(
    //                                 'selectedSubType------------',
    //                                 selectedSubType
    //                             );

    //                             let newDurationOptions = [];
    //                             if (selectedSubType) {
    //                                 for (let i = 1; i <= 5; i++) {
    //                                     newDurationOptions.push({
    //                                         label: `${
    //                                             selectedSubType.duration * i
    //                                         } min`,
    //                                         value: selectedSubType.duration * i,
    //                                     });
    //                                 }
    //                             }
    //                             setDurationOptions(newDurationOptions);
    //                             dispatch(
    //                                 SetBookingType(scheduleData.classType)
    //                             );

    //                             dispatch(
    //                                 ServiceCategoryListByPackageId({
    //                                     packageId: scheduleData.packageId,
    //                                 })
    //                             );
    //                             dispatch(
    //                                 activeServiceCategoyListPricing({
    //                                     packageId: scheduleData.packageId,
    //                                 })
    //                             );

    //                             const payload = {
    //                                 classType: ClassType.COURSES,
    //                                 facilityId: scheduleData.facilityId,
    //                                 serviceId: scheduleData.serviceCategoryId,
    //                                 subTypeId: scheduleData.subTypeId,
    //                                 date: dayjs(scheduleData.date)
    //                                     .startOf('day')
    //                                     .format('YYYY-MM-DDTHH:mm:ss[Z]'),
    //                                 startTime: scheduleData.from,
    //                                 endTime: scheduleData.to,
    //                             };

    //                             if (store.userRole !== UserRole.Trainer) {
    //                                 dispatch(
    //                                     GetStaffListBySubType({ payload })
    //                                 );
    //                             }

    //                             if (
    //                                 scheduleData.serviceCategoryId &&
    //                                 scheduleData.facilityId &&
    //                                 scheduleData.date &&
    //                                 scheduleData.to
    //                             ) {
    //                                 const reqData = {
    //                                     serviceId:
    //                                         scheduleData.serviceCategoryId,
    //                                     facilityId: scheduleData.facilityId,
    //                                     classType: ClassType.COURSES,
    //                                     date: dayjs(scheduleData.date).format(
    //                                         'YYYY-MM-DDTHH:mm:ss[Z]'
    //                                     ),
    //                                     startTime: scheduleData.from,
    //                                     endTime: scheduleData.to,
    //                                 };
    //                                 console.log(
    //                                     'ResqData-------------',
    //                                     reqData
    //                                 );

    //                                 dispatch(roomListingByScheduling(reqData));
    //                             }
    //                             setFormData({
    //                                 location: scheduleData.facilityId || null,
    //                                 package: scheduleData.packageId || null,
    //                                 classCapacity:
    //                                     scheduleData.classCapacity || '',
    //                                 serviceCategory:
    //                                     scheduleData.serviceCategoryId || null,
    //                                 subType: scheduleData.subTypeId || null,
    //                                 room: scheduleData.roomId || null,
    //                                 trainer: scheduleData.trainerId || null,
    //                                 startDate: dayjs(
    //                                     scheduleData.date
    //                                 ).toDate(),
    //                                 startTime: scheduleData.from || '',
    //                                 endTime: scheduleData.to || '',
    //                                 duration: scheduleData.duration || '',
    //                                 notes: scheduleData.notes || '',
    //                             });
    //                             setStartDate(dayjs(scheduleData.date).toDate());
    //                             setIsFormInitialized(true);
    //                         }
    //                     });
    //                 } catch (error) {
    //                     console.error(
    //                         'Error fetching scheduling details:',
    //                         error
    //                     );
    //                 } finally {
    //                     // stopLoader(); Uncomment and implement if you have a loader function
    //                 }
    //             }
    //         };

    //         fetchSchedulingDetails();
    //     }, [scheduleId, type, store.serviceCategoryByOrganization])
    // );

    const facilityOptions =
        store.userRole === UserRole.ORGANIZATION
            ? store.facilityListByOrg?.map((facility: any) => ({
                  label: facility.facilityName,
                  value: facility._id,
              }))
            : store.facilityList?.map((facility: any) => ({
                  label: facility.name,
                  value: facility._id,
              }));

    const staffOption = store.staffListBySubType?.map((staff: any) => ({
        label: `${staff.firstName}  ${staff.lastName}`,
        value: staff._id,
    }));

    const packageOptions =
        store.courseLitsForPackage
            ?.filter(
                (pkg: any) => pkg.isActive === true && pkg.isExpired === false
            )
            .map((pkg: any) => ({
                label: pkg.name,
                value: pkg._id,
                ...pkg,
            })) || [];

    const roomOptions = store.roomList?.map((room: any) => ({
        label: room.roomName,
        value: room._id,
    }));

    console.log('Reoomssjncvsjnvc-------', roomOptions);

    useFocusEffect(
        useCallback(() => {
            if (scheduleId && (isViewMode || isEditMode)) return;
            if (store.roomList && store.roomList?.length > 0) {
                const defaultRoom = store.roomList?.[0];
                setFormData((prev: any) => ({
                    ...prev,
                    room: defaultRoom?._id,
                }));
            }
        }, [store.roomList])
    );

    const DatePickerComponent = ({
        label,
        date,
        onDateChange,
        open,
        setOpen,
    }: {
        label: any;
        date: Date;
        onDateChange: (date: Date) => void;
        open: boolean;
        setOpen: (open: boolean) => void;
    }) => (
        <View style={tw`w-[45%]`}>
            <Text style={tw`text-black  pb-2`}>{label}</Text>
            <TouchableOpacity
                style={tw`w-[100%] h-[32px] flex justify-center px-1`}
                onPress={() => setOpen(true)}
            >
                <Text style={tw`text-[#455560] text-[14px]`}>
                    {dayjs(date).format('MMM D, YYYY')}
                </Text>
                <DatePicker
                    modal
                    open={open}
                    date={date}
                    onConfirm={(date) => {
                        setOpen(false);
                        onDateChange(date);
                    }}
                    onCancel={() => setOpen(false)}
                    mode="date"
                />
            </TouchableOpacity>
        </View>
    );

    const { client, subType, serviceCategory } = formData;

    useFocusEffect(
        useCallback(() => {
            if (isViewMode) return;
            const { startTime, endTime, subType, serviceCategory, location } =
                formData;

            if (store.userRole !== UserRole.Trainer) {
                if (
                    startTime &&
                    endTime &&
                    subType &&
                    serviceCategory &&
                    startDate
                ) {
                    const formattedStart = dayjs(
                        `${dayjs(startDate).format('YYYY-MM-DD')} ${startTime}`,
                        'YYYY-MM-DD HH:mm'
                    );
                    const formattedEnd = dayjs(
                        `${dayjs(startDate).format('YYYY-MM-DD')} ${endTime}`,
                        'YYYY-MM-DD HH:mm'
                    );

                    if (formattedEnd.isAfter(formattedStart)) {
                        const payload = {
                            classType: ClassType.COURSES,
                            facilityId: location,
                            serviceId: serviceCategory,
                            subTypeId: subType,
                            date: dayjs(startDate)
                                .startOf('day')
                                .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                            startTime,
                            endTime,
                        };

                        dispatch(GetStaffListBySubType({ payload })).then(
                            (res: any) => {
                                console.log('Res-----------', res);
                                const staffList = res?.payload?.res?.data?.data;
                                if (staffList && staffList.length > 0) {
                                    setFormData((prev: any) => ({
                                        ...prev,
                                        trainer: staffList[0]._id,
                                    }));
                                }
                            }
                        );
                    } else {
                        console.warn(
                            'End time is not after start time — skipping API call'
                        );
                    }
                }
            }
        }, [
            formData.startTime,
            formData.endTime,
            formData.subType,
            formData.serviceCategory,
            startDate,
        ])
    );

    useFocusEffect(
        useCallback(() => {
            if (!formData.startTime) {
                const now = dayjs();
                const rounded =
                    now.minute() <= 45
                        ? now
                              .minute(Math.ceil(now.minute() / 15) * 15)
                              .second(0)
                        : now.add(1, 'hour').minute(0).second(0);

                const formatted = rounded.format('HH:mm');

                setFormData((prev: any) => ({
                    ...prev,
                    startTime: formatted,
                }));
            }
        }, [])
    );

    useFocusEffect(
        useCallback(() => {
            if (isViewMode) return;
            const { startTime, endTime, subType, serviceCategory, location } =
                formData;

            if (
                startTime &&
                endTime &&
                startDate &&
                subType &&
                serviceCategory &&
                location
            ) {
                const start = dayjs(
                    `${dayjs(startDate).format('YYYY-MM-DD')} ${startTime}`
                );
                const end = dayjs(
                    `${dayjs(startDate).format('YYYY-MM-DD')} ${endTime}`
                );

                if (end.isAfter(start)) {
                    const payload = {
                        classType: ClassType.COURSES,
                        facilityId: location,
                        serviceId: serviceCategory,
                        date: dayjs(startDate)
                            .startOf('day')
                            .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                        startTime,
                        endTime,
                    };

                    dispatch(roomListingByScheduling(payload));
                }
            }
        }, [
            formData.startTime,
            formData.endTime,
            formData.subType,
            formData.serviceCategory,
            startDate,
            formData.location,
        ])
    );

    const handleChange = (field: string, value: any) => {
        setFormData((prev: any) => ({
            ...prev,
            [field]: value,
        }));

        if (errors[field]) {
            setErrors((prevErrors: any) => ({
                ...prevErrors,
                [field]: '',
            }));
        }

        if (field === 'package') {
            setFormData((prev: any) => ({
                ...prev,
                remainingSession: null,
                // startTime: '',
                // endTime: '',
                serviceCategory: null,
                subType: null,
                duration: null,
            }));

            const selectedPackage = store.pricingByUserAndSubType?.find(
                (pkg: any) => pkg._id === value
            );
            if (selectedPackage) {
                setFormData((prev: any) => ({
                    ...prev,
                    remainingSession: selectedPackage.remainingSession || 0,
                }));
            }
            dispatch(
                activeServiceCategoyListPricing({
                    packageId: value,
                })
            )
                .unwrap()
                .then((res: any) => {
                    console.log('Res-----------------', res);
                    // if (!scheduleId && res?.data?.data?.list?.length > 0) {
                    //     const categories = res.data.data?.list;
                    //     const firstCategory = categories[0];
                    //     const firstSubType =
                    //         firstCategory?.appointmentType?.[0];

                    //     if (firstCategory && firstSubType) {
                    //         // Generate duration options (5 multiples)
                    //         const generatedDurationOptions = Array.from(
                    //             { length: 5 },
                    //             (_, i) => ({
                    //                 label: `${
                    //                     firstSubType.durationInMinutes * (i + 1)
                    //                 } min`,
                    //                 value:
                    //                     firstSubType.durationInMinutes *
                    //                     (i + 1),
                    //             })
                    //         );

                    //         const defaultDuration =
                    //             generatedDurationOptions[0]?.value;

                    //         // Calculate default endTime based on startTime and duration
                    //         let calculatedEndTime = '';
                    //         if (formData.startTime && defaultDuration) {
                    //             const formattedStartDate =
                    //                 dayjs(startDate).format('YYYY-MM-DD');
                    //             const startMoment = dayjs(
                    //                 `${formattedStartDate} ${formData.startTime}`,
                    //                 'YYYY-MM-DD HH:mm'
                    //             );
                    //             if (startMoment.isValid()) {
                    //                 calculatedEndTime = startMoment
                    //                     .add(defaultDuration, 'minute')
                    //                     .format('HH:mm');
                    //             }
                    //         }

                    //         // Update formData with first service type
                    //         setDurationOptions(generatedDurationOptions);
                    //         setFormData((prev: any) => ({
                    //             ...prev,
                    //             serviceCategory: firstCategory._id,
                    //             subType: firstSubType._id,
                    //             duration: defaultDuration,
                    //             endTime: calculatedEndTime,
                    //         }));

                    //         // Fetch room list for default service
                    //         dispatch(
                    //             roomListingByServiceCategory({
                    //                 serviceCategoryId: firstCategory._id,
                    //                 facilityId: store.selectedFacility,
                    //             })
                    //         );
                    //     }
                    // }
                });
        }

        if (field === 'startTime' || field === 'duration') {
            let startTime = field === 'startTime' ? value : formData.startTime;
            let duration = field === 'duration' ? value : formData.duration;

            if (startTime && duration) {
                const formattedStartDate =
                    dayjs(startDate).format('YYYY-MM-DD');

                const startMoment = dayjs(
                    `${formattedStartDate} ${startTime}`,
                    'YYYY-MM-DD HH:mm'
                );

                if (startMoment.isValid()) {
                    const endMoment = startMoment.add(duration, 'minute');
                    const endTime = endMoment.format('HH:mm');

                    setFormData((prev: any) => ({
                        ...prev,
                        startTime: startTime,
                        duration: duration,
                        endTime: endTime,
                    }));
                } else {
                    setFormData((prev: any) => ({
                        ...prev,
                        endTime: '',
                    }));
                }
            }
        }
    };

    const validateForm = () => {
        let newErrors: any = {};
        if (!formData.location) newErrors.location = 'Location is required';
        if (!formData.package) newErrors.package = 'Package is required';
        if (!formData.subType) newErrors.subType = 'Sub type is required';
        if (!formData.serviceCategory)
            newErrors.serviceCategory = 'Service Category is required';
        if (!formData.startTime) newErrors.startTime = 'Start Time is required';
        if (!formData.duration) newErrors.duration = 'Duration is required';
        if (!formData.classCapacity)
            newErrors.classCapacity = 'Class capacity is required';
        // if (!startDate) newErrors.duration = 'Start Date is required';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const resetFields = () => {
        setFormData({
            location: null,
            trainer: null,
            phone: '',
            email: '',
            package: null,
            serviceCategory: null,
            room: null,
            startDate: new Date(),
            startTime: '',
            endTime: '',
            duration: null,
            classCapacity: null,
            notes: '',
        });
        setStartDate(new Date());
        setErrors({});
    };

    const handleSubmit = () => {
        if (!validateForm()) return;

        startLoader();
        const payload = {
            facilityId: formData.location,
            organizationId: store.organizationId,
            trainerId:
                store.userRole !== UserRole.Trainer
                    ? formData.trainer
                    : store.userId,
            classType: ClassType.COURSES,
            ...(formData.room && { roomId: formData.room }),
            ...(formData.notes && { notes: formData.notes }),
            dateRange: 'Single',
            courseId: formData.package,
            serviceCategory: formData.serviceCategory,
            subType: formData.subType,
            duration: formData.duration,
            date: dayjs(startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: formData.startTime,
            to: formData.endTime,
            // ...(!scheduleId && { checkIn: false }),
            classCapacity: parseInt(formData.classCapacity, 10),
        };

        console.log('Submitting Payload:', payload);
        if (scheduleId && type) {
            dispatch(
                updateCoursesScheduling({
                    payload: { ...payload, schedulingId: scheduleId },
                })
            )
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        resetFields();
                        navigationRef.goBack();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [formData.location],
                            })
                        );
                    }
                })
                .catch(() => {})
                .finally(endLoader);
        } else {
            dispatch(createCoursesScheduling({ payload }))
                .unwrap()
                .then((res: any) => {
                    // console.log('Res------------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        resetFields();
                        navigationRef.goBack();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [formData.location],
                            })
                        );
                    }
                })
                .catch(() => {})
                .finally(endLoader);
        }
    };

    console.log('form data----------------', formData);

    return (
        <ScrollView style={tw`flex-1 bg-white`}>
            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Location
                </Text>
                <View
                    style={tw`w-[100%] border-b border-[#45556066] ${
                        isViewMode === true ? 'pb-0' : 'pb-1.5'
                    }`}
                >
                    <Dropdown
                        style={tw`${
                            isViewMode === true ? 'bg-gray-100 py-1.5' : ''
                        }`}
                        data={facilityOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Branch"
                        disable={isViewMode}
                        value={formData?.location}
                        onChange={(item) =>
                            handleChange('location', item.value)
                        }
                        // value={location || store.selectedFacility}
                        // onChange={(item) => setLocationState(item.value)}
                    />
                </View>
                {location ||
                    (store.selectedFacility && (
                        <Text style={tw`text-red-500`}>{errors.location}</Text>
                    ))}
            </View>

            <View style={tw`mt-4`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Package
                </Text>
                <View
                    style={tw` w-[100%] border-b border-[#45556066] ${
                        isViewMode === true ? 'pb-0' : 'pb-1.5'
                    }`}
                >
                    <Dropdown
                        style={tw`${
                            isViewMode === true ? 'bg-gray-100 py-1.5' : ''
                        }`}
                        data={packageOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Package"
                        disable={isViewMode}
                        value={formData?.package}
                        onChange={(item) => handleChange('package', item.value)}
                    />
                </View>
            </View>

            <View style={tw`mt-9`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Service
                </Text>
                <View
                    style={tw`w-[100%] border-b border-[#45556066] ${
                        isViewMode === true ? 'pb-0' : 'pb-1.5'
                    }`}
                >
                    <Dropdown
                        style={tw`${
                            isViewMode === true ? 'bg-gray-100 py-1.5' : ''
                        }`}
                        data={transformServiceTypeData()}
                        labelField="label"
                        valueField="value"
                        placeholder="Select service category"
                        disable={isViewMode}
                        value={formData?.subType}
                        // onChange={(item) =>
                        //     handleChange('serviceCategory', item.value)
                        // }
                        renderItem={(item) => (
                            <View
                                style={tw`p-2 ${
                                    item.type === 'category'
                                        ? 'bg-gray-200'
                                        : 'bg-white'
                                }`}
                            >
                                <Text
                                    style={tw`${
                                        item.type === 'category'
                                            ? 'font-bold text-black'
                                            : 'text-gray-700'
                                    }`}
                                >
                                    {item.label}
                                </Text>
                            </View>
                        )}
                        onChange={(item) => {
                            if (item.type === 'category') {
                                setFormData((prev: any) => ({
                                    ...prev,
                                    subType: '',
                                }));
                                return;
                            }

                            if (item.type === 'subType') {
                                const selectedSubTypeDuration = item.duration;
                                const selectedCategoryId = item.categoryId;

                                const generatedDurationOptions = Array.from(
                                    { length: 5 },
                                    (_, i) => ({
                                        label: `${
                                            selectedSubTypeDuration * (i + 1)
                                        } min`,
                                        value:
                                            selectedSubTypeDuration * (i + 1),
                                    })
                                );

                                const defaultDuration =
                                    generatedDurationOptions[0]?.value;

                                let calculatedEndTime = '';
                                if (formData.startTime && defaultDuration) {
                                    const formattedStartDate =
                                        dayjs(startDate).format('YYYY-MM-DD');
                                    const startMoment = dayjs(
                                        `${formattedStartDate} ${formData.startTime}`,
                                        'YYYY-MM-DD HH:mm'
                                    );

                                    if (startMoment.isValid()) {
                                        calculatedEndTime = startMoment
                                            .add(defaultDuration, 'minute')
                                            .format('HH:mm');
                                    }
                                }

                                setFormData((prev: any) => ({
                                    ...prev,
                                    subType: item.value,
                                    serviceCategory: selectedCategoryId,
                                    duration: defaultDuration,
                                    // startTime: '',
                                    endTime: calculatedEndTime,
                                }));

                                setDurationOptions(generatedDurationOptions);

                                // Dispatch room listing
                                dispatch(
                                    roomListingByServiceCategory({
                                        serviceCategoryId: selectedCategoryId,
                                        facilityId: store.selectedFacility,
                                    })
                                );
                            }
                        }}
                    />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.serviceCategory}
                    </Text>
                )}
            </View>

            <View style={tw`mt-9`}>
                <View
                    style={tw`w-[100%] flex flex-row justify-between items-end border-b border-[#45556066] pb-1.5`}
                >
                    <DatePickerComponent
                        label={
                            <Text
                                style={tw`text-[#455560] font-medium  text-[13px]`}
                            >
                                Start Date
                            </Text>
                        }
                        date={startDate}
                        onDateChange={setStartDate}
                        open={isViewMode ? false : openStartDatePicker}
                        setOpen={setOpenStartDatePicker}
                    />
                    <Image
                        source={Asset.Calender}
                        style={[tw`w-5 -translate-y-1 h-5`]}
                    />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.subType}
                    </Text>
                )}
            </View>

            <View
                style={tw`mt-5   w-[100%] flex flex-row items-start justify-between`}
            >
                <View style={tw`w-[45%] `}>
                    {isViewMode ? (
                        <View style={tw`mt-2 border-b border-gray-300 `}>
                            <Text
                                style={tw`text-[#455560] font-medium mb-2  text-[13px]`}
                            >
                                Start Time
                            </Text>
                            <Text
                                style={tw`text-[#455560] text-[13px] py-1.5 ${
                                    isViewMode === true ? 'bg-gray-100' : ''
                                }`}
                            >
                                {formData?.startTime || '--'}
                            </Text>
                        </View>
                    ) : (
                        <TimePicker
                            isOpen
                            placeholder="Start Time"
                            title={
                                <Text
                                    style={tw`text-[#455560] font-medium  text-[13px]`}
                                >
                                    Start Time
                                </Text>
                            }
                            value={formData?.startTime}
                            onChange={(value) =>
                                handleChange('startTime', value)
                            }
                        />
                    )}
                    {errors?.startTime && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.startTime}
                        </Text>
                    )}
                </View>
                <View style={tw` w-[45%] pt-3 mt-0.5`}>
                    <View style={tw`flex flex-col gap-1`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-1`}
                        >
                            Duration
                        </Text>
                        <View>
                            <Dropdown
                                style={tw`border-b border-gray-400  ${
                                    isViewMode === true
                                        ? 'bg-gray-100 py-1.5'
                                        : ''
                                }`}
                                data={durationOptions}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Duration"
                                disable={isViewMode}
                                value={formData?.duration}
                                onChange={(item) =>
                                    handleChange('duration', item.value)
                                }
                            />
                        </View>
                    </View>
                    {errors?.duration && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.duration}
                        </Text>
                    )}
                </View>
            </View>

            <View style={tw`mt-5 flex flex-row gap-3 items-center`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    End Time :
                </Text>
                <TextInput
                    value={formData?.endTime || ''}
                    editable={false}
                    style={tw``}
                />
            </View>

            {(store.userRole === UserRole.FRONT_DESK_ADMIN ||
                store.userRole === UserRole.ORGANIZATION ||
                store.userRole === UserRole.WEB_MASTER) && (
                <View style={tw`mt-5`}>
                    <Text
                        style={tw`text-[#455560] font-medium  text-[13px]  pb-2`}
                    >
                        Staff
                    </Text>
                    <View
                        style={tw`w-[100%] border-b border-[#45556066]  ${
                            isViewMode === true ? 'bg-gray-100' : ''
                        }`}
                    >
                        <Dropdown
                            style={tw`  ${
                                isViewMode === true ? 'bg-gray-100 py-1.5' : ''
                            }`}
                            data={staffOption}
                            labelField="label"
                            valueField="value"
                            disable={isViewMode}
                            placeholder="Select Staff"
                            value={formData?.trainer}
                            onChange={(item) =>
                                handleChange('trainer', item.value)
                            }
                        />
                    </View>
                    {errors?.trainer && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.trainer}
                        </Text>
                    )}
                </View>
            )}

            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px]  pb-2`}>
                    Room
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066]`}>
                    <Dropdown
                        style={tw`  ${
                            isViewMode === true ? 'bg-gray-100 py-1.5' : ''
                        }`}
                        data={roomOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Room"
                        disable={isViewMode}
                        value={formData?.room}
                        onChange={(item) => handleChange('room', item.value)}
                    />
                </View>
            </View>

            <View style={tw`mt-5  border-b border-gray-400`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Class Capacity
                </Text>
                <TextInput
                    style={tw`${isViewMode === true ? 'bg-gray-100' : ''}`}
                    placeholder="Enter class capacity"
                    editable={isViewMode ? false : true}
                    value={formData?.classCapacity}
                    onChangeText={(text) => {
                        handleChange('classCapacity', text);
                    }}
                />
            </View>
            {errors?.classCapacity && (
                <Text style={tw`text-red-500 text-[11px]`}>
                    {errors?.classCapacity}
                </Text>
            )}

            <View
                style={tw`mt-5 ${
                    isViewMode && 'mb-10'
                } border-b border-gray-400`}
            >
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Notes
                </Text>
                <TextInput
                    style={tw`  ${
                        isViewMode === true ? 'bg-gray-100 py-1.5' : ''
                    }`}
                    placeholder="Enter Notes"
                    editable={isViewMode ? false : true}
                    style={tw``}
                    value={formData?.notes}
                    onChangeText={(text) => {
                        handleChange('notes', text);
                    }}
                />
            </View>

            {!isViewMode && (
                <View
                    style={tw`flex justify-center items-center mt-10 mb-[8%]`}
                >
                    <Button
                        // nextImage
                        style={[tw`w-[70%] rounded-full font-bold`]}
                        onPress={handleSubmit}
                        loading={loader}
                    >
                        {isEditMode ? 'Edit' : 'Save'}
                    </Button>
                </View>
            )}
        </ScrollView>
    );
};

export default BookCoursesScreen;
