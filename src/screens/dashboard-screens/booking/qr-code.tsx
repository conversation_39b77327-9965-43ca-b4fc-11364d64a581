import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
    Alert,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    Camera,
    useCameraDevices,
    useCameraPermission,
    useCodeScanner,
} from 'react-native-vision-camera';

import { BOOK_APPOINTMENT } from '~/constants/navigation-constant';

import { navigationRef } from '~/hooks/useLocation';

export default function QRScanner({
    onScanned,
}: {
    onScanned?: (data: string) => void;
}) {
    const { hasPermission, requestPermission } = useCameraPermission();
    const devices = useCameraDevices();
    const [scanned, setScanned] = useState(false);
    const [isInitialized, setIsInitialized] = useState(false);
    const confirmingRef = useRef(false);

    // Get the back camera device with fallback
    const device = devices.find((d) => d.position === 'back') ?? devices[0];

    const parsePurchaseIdFromScan = (
        raw: string | undefined | null
    ): string | null => {
        if (!raw) return null;

        // Case 1: JSON string like {"purchase_id":"..."}
        try {
            const asJson = JSON.parse(raw);
            if (
                asJson &&
                typeof asJson === 'object' &&
                typeof asJson.purchase_id === 'string'
            ) {
                return asJson.purchase_id;
            }
        } catch (_e) {}

        try {
            const maybeUrl = raw.startsWith('http')
                ? raw
                : `https://dummy.local/?${raw}`;
            const url = new URL(maybeUrl);
            const fromQuery = url.searchParams.get('purchase_id');
            if (fromQuery) return fromQuery;
        } catch (_e) {}

        const m = raw.match(
            /purchase[_-]?id["']?\s*[:=]\s*["']?([a-zA-Z0-9_-]+)/i
        );
        if (m?.[1]) return m[1];

        return null;
    };

    // Handle camera initialization
    useEffect(() => {
        const initializeCamera = async () => {
            if (!hasPermission) {
                const permission = await requestPermission();
                if (!permission) {
                    return;
                }
            }

            // Small delay to ensure camera is ready
            setTimeout(() => {
                setIsInitialized(true);
            }, 100);
        };

        initializeCamera();
    }, [hasPermission, requestPermission]);

    // Reset scanned state when component focuses
    useFocusEffect(
        useCallback(() => {
            setScanned(false);
            confirmingRef.current = false;
        }, [])
    );

    const confirmAndNavigate = (purchaseId: string, rawValue?: string) => {
        if (confirmingRef.current) return;
        confirmingRef.current = true;

        Alert.alert(
            'Confirm Purchase',
            `Proceed with purchase ID:\n${purchaseId}?`,
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                    onPress: () => {
                        confirmingRef.current = false;
                        // allow scanning again if user cancels
                        setScanned(false);
                    },
                },
                {
                    text: 'Proceed',
                    style: 'default',
                    onPress: () => {
                        // If caller wants the full raw value too
                        try {
                            onScanned?.(rawValue ?? purchaseId);
                        } catch {}
                        navigationRef.navigate(
                            BOOK_APPOINTMENT as never,
                            { purchaseId } as never
                        );
                        confirmingRef.current = false;
                    },
                },
            ],
            { cancelable: false }
        );
    };

    const codeScanner = useCodeScanner({
        codeTypes: ['qr', 'ean-13'],
        onCodeScanned: (codes) => {
            if (!codes?.length || scanned) return;

            const raw = codes[0]?.value;
            console.log('Codes detected:', codes);

            const purchaseId = parsePurchaseIdFromScan(raw);
            if (purchaseId) {
                setScanned(true);
                confirmAndNavigate(purchaseId, raw);
            } else {
                // No purchase_id — optionally show a brief message or ignore
                // Keep scanning in this case
                console.log('No purchase_id found in payload:', raw);
            }
        },
    });

    // Handle permission states
    if (!hasPermission) {
        return (
            <View style={styles.centerContainer}>
                <Text style={styles.statusText}>
                    Camera permission is required to scan QR codes
                </Text>
                <TouchableOpacity
                    style={styles.button}
                    onPress={async () => {
                        const granted = await requestPermission();
                        if (!granted) {
                            navigationRef.goBack();
                        }
                    }}
                >
                    <Text style={styles.buttonText}>Grant Permission</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={styles.button}
                    onPress={() => navigationRef.goBack()}
                >
                    <Text style={styles.buttonText}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    // Handle loading state
    if (!device || !isInitialized) {
        return (
            <View style={styles.centerContainer}>
                <Text style={styles.statusText}>
                    {!device
                        ? 'No camera available on this device'
                        : 'Initializing camera...'}
                </Text>
                <TouchableOpacity
                    style={styles.button}
                    onPress={() => navigationRef.goBack()}
                >
                    <Text style={styles.buttonText}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            {/* Camera */}
            <Camera
                device={device}
                isActive={isInitialized && !scanned}
                style={StyleSheet.absoluteFill}
                codeScanner={codeScanner}
            />

            {/* Overlay */}
            <View style={styles.overlay}>
                <View style={styles.scanFrame} />
                <Text style={styles.instructionText}>
                    {scanned ? 'Processing...' : 'Point at QR code'}
                </Text>
            </View>

            {/* Controls */}
            <View style={styles.controls}>
                <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => navigationRef.goBack()}
                >
                    <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'black',
    },
    centerContainer: {
        flex: 1,
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'black',
    },
    statusBar: {
        position: 'absolute',
        top: 50,
        left: 10,
        right: 10,
        zIndex: 1000,
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: 5,
        borderRadius: 5,
    },
    statusText: {
        color: 'white',
        fontSize: 16,
        textAlign: 'center',
    },
    debugText: {
        color: 'white',
        fontSize: 12,
        textAlign: 'center',
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 100,
    },
    scanFrame: {
        width: 250,
        height: 250,
        borderWidth: 2,
        borderColor: '#00ff00',
        borderRadius: 10,
        backgroundColor: 'transparent',
    },
    instructionText: {
        color: 'white',
        fontSize: 16,
        marginTop: 20,
        textAlign: 'center',
    },
    controls: {
        position: 'absolute',
        bottom: 50,
        left: 20,
        right: 20,
        zIndex: 200,
    },
    cancelButton: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        marginBottom: 10,
    },
    retryButton: {
        backgroundColor: '#007AFF',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
    },
    button: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        marginTop: 20,
    },
    buttonText: {
        fontSize: 16,
        fontWeight: '600',
        color: 'black',
    },
});
