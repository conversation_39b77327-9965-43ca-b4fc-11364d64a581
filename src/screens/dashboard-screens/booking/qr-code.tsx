import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    PermissionsAndroid,
    StatusBar,
    Text,
    TouchableOpacity,
    Vibration,
    View,
} from 'react-native';
import QRCodeScanner from 'react-native-qrcode-scanner';
import { qrCodePurchaseDetails } from '~/redux/actions/booking-action';

import tw from '~/styles/tailwind';

import { BOOK_APPOINTMENT } from '~/constants/navigation-constant';

import { useAppDispatch } from '~/hooks/redux-hooks';
import { navigationRef } from '~/hooks/useLocation';

// your tailwind setup

const { height } = Dimensions.get('window');

const QRScannerComponent = ({
    onScanSuccess = () => {},
    onScanError = () => {},
    title = 'Scan QR Code',
    subtitle = 'Position the QR code within the frame to scan',
    vibrationEnabled = true,
    customOverlay = null,
    scanDelay = 1000,
}: any) => {
    const [scanning, setScanning] = useState(true);
    const [lastScannedData, setLastScannedData] = useState('');
    const [scanCount, setScanCount] = useState(0);
    const [hasPermission, setHasPermission] = useState(false);
    const dispatch = useAppDispatch();

    const onSuccess = (e: any) => {
        let scannedData;
        try {
            scannedData = JSON.parse(e.data);
        } catch (error) {
            console.error('Failed to parse scanned data:', error);
            return;
        }

        const purchaseId = scannedData.purchase_id;
        if (!purchaseId) return;

        setLastScannedData(purchaseId);
        setScanCount((prev) => prev + 1);
        setScanning(false);

        if (vibrationEnabled) Vibration.vibrate(100);

        Alert.alert(
            'Confirm Scan',
            `Do you want to proceed with Purchase ID: ${purchaseId}?`,
            [
                {
                    text: 'Cancel',
                    onPress: () => {
                        setScanning(true);
                        setLastScannedData('');
                    },
                    style: 'cancel',
                },
                {
                    text: 'Confirm',
                    onPress: () => {
                        dispatch(qrCodePurchaseDetails({ purchaseId }))
                            .unwrap()
                            .then(() => {
                                (navigationRef as any).navigate(
                                    BOOK_APPOINTMENT
                                );
                            })
                            .catch((error: any) => {
                                onScanError(error);
                            });

                        setTimeout(() => {
                            setScanning(true);
                            setLastScannedData('');
                        }, scanDelay);
                    },
                },
            ],
            { cancelable: false }
        );

        onScanSuccess(purchaseId);
    };

    useEffect(() => {
        const requestCameraPermission = async () => {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.CAMERA,
                    {
                        title: 'Camera Permission',
                        message:
                            'This app needs camera permission to scan QR codes.',
                        buttonNeutral: 'Ask Me Later',
                        buttonNegative: 'Cancel',
                        buttonPositive: 'OK',
                    }
                );
                setHasPermission(
                    granted === PermissionsAndroid.RESULTS.GRANTED
                );
            } catch (err) {
                console.warn(err);
                setHasPermission(false);
            }
        };
        requestCameraPermission();
    }, []);

    if (!hasPermission) {
        return (
            <View
                style={tw`flex-1 justify-center items-center bg-gray-100 p-5`}
            >
                <Text style={tw`text-lg text-gray-800 text-center mb-5`}>
                    Camera permission is required to scan QR codes
                </Text>
                <TouchableOpacity
                    style={tw`bg-blue-600 px-6 py-3 rounded-lg`}
                    onPress={async () => {
                        const granted = await PermissionsAndroid.request(
                            PermissionsAndroid.PERMISSIONS.CAMERA
                        );
                        setHasPermission(
                            granted === PermissionsAndroid.RESULTS.GRANTED
                        );
                    }}
                >
                    <Text style={tw`text-white text-base font-semibold`}>
                        Grant Permission
                    </Text>
                </TouchableOpacity>
            </View>
        );
    }

    const renderTopContent = () => (
        <View
            style={tw`absolute top-0 w-full bg-primary bg-opacity-80 py-10 items-center px-5`}
        >
            <Text style={tw`text-white text-2xl font-bold text-center mb-2`}>
                {title}
            </Text>
            <Text
                style={tw`text-gray-300 text-base text-center mb-3 leading-6`}
            >
                {subtitle}
            </Text>
            {/* {scanCount > 0 && (
                <Text
                    style={tw`text-green-500 bg-green-200 bg-opacity-30 px-3 py-1 rounded-full`}
                >
                    Scans: {scanCount}
                </Text>
            )} */}
        </View>
    );

    const renderCustomMarker = () => (
        <View style={tw`w-64 h-64 justify-center items-center relative`}>
            <View
                style={[
                    tw`absolute w-12 h-12 border-4 border-blue-500`,
                    {
                        top: 0,
                        left: 0,
                        borderBottomWidth: 0,
                        borderRightWidth: 0,
                    },
                ]}
            />
            <View
                style={[
                    tw`absolute w-12 h-12 border-4 border-blue-500`,
                    {
                        top: 0,
                        right: 0,
                        borderBottomWidth: 0,
                        borderLeftWidth: 0,
                    },
                ]}
            />
            <View
                style={[
                    tw`absolute w-12 h-12 border-4 border-blue-500`,
                    {
                        bottom: 0,
                        left: 0,
                        borderTopWidth: 0,
                        borderRightWidth: 0,
                    },
                ]}
            />
            <View
                style={[
                    tw`absolute w-12 h-12 border-4 border-blue-500`,
                    {
                        bottom: 0,
                        right: 0,
                        borderTopWidth: 0,
                        borderLeftWidth: 0,
                    },
                ]}
            />

            <View style={tw`w-full h-1 bg-blue-500 opacity-80`} />

            {!scanning && (
                <View
                    style={tw`absolute justify-center items-center bg-black bg-opacity-70 p-5 rounded-lg`}
                >
                    <ActivityIndicator size="large" color="#4CAF50" />
                    <Text style={tw`text-white text-base mt-2`}>
                        Processing...
                    </Text>
                </View>
            )}
        </View>
    );

    return (
        <View style={tw`flex-1 bg-primary`}>
            <QRCodeScanner
                onRead={onSuccess}
                topContent={customOverlay || renderTopContent()}
                bottomContent={<View />}
                cameraStyle={tw`h-96 `}
                showMarker
                customMarker={renderCustomMarker()}
                checkAndroid6Permissions={false}
                cameraProps={{
                    ratio: '16:9',
                    captureAudio: false,
                }}
                containerStyle={tw`flex-1`}
                cameraContainerStyle={tw`flex-1`}
                reactivate={scanning}
                reactivateTimeout={scanDelay}
            />

            <View style={tw`absolute bottom-10 w-full px-5`}>
                <TouchableOpacity
                    style={tw`bg-red-600 py-3 rounded-lg items-center`}
                    onPress={() => (navigationRef as any).goBack()}
                >
                    <Text style={tw`text-white text-base font-semibold`}>
                        Cancel
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default QRScannerComponent;
