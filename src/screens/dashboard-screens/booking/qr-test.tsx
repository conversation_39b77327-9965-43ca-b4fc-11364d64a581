import { useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import {
    Camera,
    useCameraDevice,
    useCameraDevices,
} from 'react-native-vision-camera';

import tw from '~/styles/tailwind';

import { navigationRef } from '~/hooks/useLocation';

const QrtestScreen = () => {
    const devices = useCameraDevices();
    console.log('Devices-------', devices);
    const device = useCameraDevice('back');

    const checkPermission = async () => {
        const newCameraPermission = await Camera.requestCameraPermission();
        console.log('Status-------', newCameraPermission);
    };

    useEffect(() => {
        checkPermission();
    }, []);

    return (
        <View style={{ flex: 1 }}>
            <Camera
                style={StyleSheet.absoluteFill}
                device={device}
                isActive={true}
                // codeScanner={codeScanner}
            />
        </View>
    );
};

export default QrtestScreen;
