import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import {
    ScrollView,
    Switch,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import tw from 'twrnc';

import TimePicker from '~/components/common/time-picker';

const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

const capitalize = (word: any) => word.charAt(0).toUpperCase() + word.slice(1);

const MultipleScheduleRN = ({
    durationOptions = [],
    workingHours,
    setWorkingHours,
    selectedDay,
    setSelectedDay,
}: any) => {
    const [duplicateSwitch, setDuplicateSwitch] = useState(false);

    const handleStartTimeChange = (val: any, index: number) => {
        const start = val;
        const duration =
            workingHours[selectedDay]?.[index]?.durationInMinutes || 30;
        const end = dayjs(val, 'HH:mm').add(duration, 'minute').format('HH:mm');
        const updated = [...workingHours[selectedDay]];
        updated[index] = { from: start, to: end, durationInMinutes: duration };
        setWorkingHours({ ...workingHours, [selectedDay]: updated });
    };

    const handleDurationChange = (val: any, index: number) => {
        const fromTime = workingHours[selectedDay]?.[index]?.from;
        const from = dayjs(fromTime, 'HH:mm');
        const end = from.add(val, 'minute').format('HH:mm');
        const updated = [...workingHours[selectedDay]];
        updated[index] = { ...updated[index], durationInMinutes: val, to: end };
        setWorkingHours({ ...workingHours, [selectedDay]: updated });
    };

    const handleDuplicate = (checked: any) => {
        setDuplicateSwitch(checked);
        if (checked && workingHours[selectedDay]?.[0]) {
            const duplicateSlot = workingHours[selectedDay][0];
            const updates = {};
            daysOfWeek.forEach((day) => {
                if (day !== selectedDay) updates[day] = [duplicateSlot];
            });
            setWorkingHours({ ...workingHours, ...updates });
        } else {
            const updates = {};
            daysOfWeek.forEach((day) => {
                if (day !== selectedDay) updates[day] = [];
            });
            setWorkingHours({ ...workingHours, ...updates });
        }
    };

    return (
        <ScrollView style={tw`mt-4`}>
            <Text style={tw`text-[#1A3353] font-bold text-[16px] mb-2`}>
                Select Days
            </Text>
            <View style={tw`flex-row flex-wrap gap-2 mb-4`}>
                {daysOfWeek.map((day) => (
                    <TouchableOpacity
                        key={day}
                        onPress={() => setSelectedDay(day)}
                        style={tw.style(
                            'w-10 h-10 rounded-full items-center justify-center border',
                            selectedDay === day
                                ? 'bg-[#455560] border-[#455560]'
                                : 'bg-white border-gray-400'
                        )}
                    >
                        <Text
                            style={tw.style(
                                selectedDay === day
                                    ? 'text-white'
                                    : 'text-black'
                            )}
                        >
                            {capitalize(day[0])}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {workingHours[selectedDay]?.map((slot, index) => (
                <View
                    key={index}
                    style={tw`mb-6 p-4 rounded-xl border border-gray-200 bg-white`}
                >
                    <Text style={tw`text-[#455560] mb-2`}>Start Time</Text>
                    <TimePicker
                        placeholder="Start Time"
                        value={slot.from}
                        onChange={(val) => handleStartTimeChange(val, index)}
                    />

                    <Text style={tw`text-[#455560] mt-4 mb-2`}>Duration</Text>
                    <Dropdown
                        style={tw`border border-gray-300 rounded-md px-2 py-1`}
                        data={durationOptions}
                        value={slot.durationInMinutes}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Duration"
                        onChange={(item) =>
                            handleDurationChange(item.value, index)
                        }
                    />

                    <Text style={tw`text-[#455560] mt-4 mb-2`}>End Time</Text>
                    <TextInput
                        style={tw`border border-gray-200 p-2 rounded-md bg-gray-100`}
                        value={slot.to || ''}
                        editable={false}
                    />
                </View>
            ))}

            <View style={tw`flex-row items-center mb-8`}>
                <Switch
                    value={duplicateSwitch}
                    onValueChange={handleDuplicate}
                />
                <Text style={tw`ml-3 text-[#1A3353] text-sm`}>
                    Duplicate to all days
                </Text>
            </View>
        </ScrollView>
    );
};

export default MultipleScheduleRN;
