import { useNavigation } from '@react-navigation/native';
import React, { memo } from 'react';
import {
    Image,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

const StartTime = [
    { label: '07:00 AM' },
    { label: '09:00 AM' },
    { label: '11:00 AM' },
];
const EndTime = [
    { label: '07:00 AM' },
    { label: '09:00 AM' },
    { label: '11:00 AM' },
];
const ClassesDetail = () => {
    const navigation = useNavigation();

    return (
        <ScrollView>
            <View
                style={tw`bg-#D0FF01]  h-46 pt-5 pb-3 border-b border-[#455560] flex flex-col justify-between px-4 shadow-md`}
            >
                <BackButtonHeading Heading={'Book Classes'} />
                <View>
                    <Text style={tw`text-center text-lg text-[#455560]`}>
                        GGN Center
                    </Text>
                    <Text
                        style={tw`text-center text-lg text-[#455560] font-bold`}
                    >
                        Morning Group Yoga Class
                    </Text>
                    <Text
                        style={tw`text-center text-[16px] text-[#455560] font-semibold`}
                    >
                        Saturday, August 6
                    </Text>
                </View>
            </View>

            {/* ======================================2nd section============================ */}

            <View style={tw`flex flex-row pt-4 pb-2  `}>
                <View style={tw`w-[33.33%] pl-3  flex flex-col   `}>
                    <Text style={tw`text-black`}>Staff</Text>
                    {/* <View style={tw`flex flex-row items-center gap-2`}>
                        <Image style={tw`w-9 h-9`} source={Asset.Avatar} />
                        <Text style={tw`text-[#455560]`}>Mr. Abhay</Text>
                    </View> */}
                </View>
                <View style={tw`w-[33.33%] pl-3  flex flex-col   `}>
                    <Text style={tw`text-black`}>Location</Text>
                    {/* <View style={tw`flex flex-row items-center gap-2`}>
                        <Text style={tw`text-[#455560]`}>
                            GGN Center, Delhi
                        </Text>
                    </View> */}
                </View>
                <View style={tw`w-[33.33%] pl-3  flex flex-col   `}>
                    <Text style={tw`text-black`}>Available</Text>
                    {/* <View style={tw`flex flex-row items-center gap-2`}>
                        <Image style={tw`w-9 h-9`} source={Asset.Avatar} />
                        <Text style={tw`text-[#455560]`}>35 Seats</Text>
                    </View> */}
                </View>
            </View>
            <View
                style={tw`flex flex-row  border-b border-[#455560] pb-4 items-center `}
            >
                <View style={tw`w-[33.33%] pl-3  flex flex-col   `}>
                    <View style={tw`flex flex-row items-center gap-2`}>
                        <Image
                            style={tw`w-9 h-9 rounded-full`}
                            source={Asset.Avatar}
                        />
                        <Text style={tw`text-[#455560] text-sm  font-semibold`}>
                            Mr. Abhay
                        </Text>
                    </View>
                </View>
                <View style={tw`w-[33.33%] pl-3  flex flex-col   `}>
                    <View style={tw`flex flex-row items-center gap-2`}>
                        <Text style={tw`text-[#455560]  text-sm font-semibold`}>
                            GGN Center, Delhi
                        </Text>
                    </View>
                </View>
                <View style={tw`w-[33.33%] pl-3  flex flex-col   `}>
                    <View style={tw`flex flex-row items-center gap-2`}>
                        <Image style={tw`w-4 h-4`} source={Asset.Tick} />
                        <Text style={tw`text-[#455560] text-sm  font-semibold`}>
                            35 Seats
                        </Text>
                    </View>
                </View>
            </View>

            {/* ------------------------------------time inputs---------------------------------------- */}
            <View>
                <Text style={tw`text-base pt-5 pb-4 px-4`}>Details</Text>
                <View
                    style={tw`flex flex-row justify-between border-b border-[#7d7d7d] pb-4`}
                >
                    <Text
                        style={tw`text-[15px] text-[#455560] font-semibold px-4`}
                    >
                        Start Time
                    </Text>
                    <View
                        style={tw`w-[144px] border border-gray-400 rounded-full mr-4 px-1`}
                    >
                        <Dropdown
                            style={tw`w-[140px] h-7`}
                            data={StartTime}
                            placeholder="Select Time"
                            labelField="label"
                            valueField="value"
                            fontSize={12}
                            selectedTextStyle={{ fontSize: 12 }}
                            itemTextStyle={{ fontSize: 12 }}
                            placeholderStyle={{ fontSize: 12 }}
                        />
                    </View>
                </View>
            </View>
            <View>
                <View
                    style={tw`flex flex-row justify-between border-b border-[#7d7d7d] pt-8 pb-4`}
                >
                    <Text
                        style={tw`text-[15px] text-[#455560] font-semibold px-4`}
                    >
                        End Time
                    </Text>
                    <View
                        style={tw`w-[144px] border border-gray-400 rounded-full mr-4 px-1`}
                    >
                        <Dropdown
                            style={tw`w-[140px] h-7`}
                            data={EndTime}
                            placeholder="Select Time"
                            labelField="label"
                            valueField="value"
                            fontSize={12}
                            selectedTextStyle={{ fontSize: 12 }}
                            itemTextStyle={{ fontSize: 12 }}
                            placeholderStyle={{ fontSize: 12 }}
                        />
                    </View>
                </View>
            </View>
            <View>
                <View
                    style={tw`flex flex-row justify-between border-b border-[#7d7d7d] pt-8 pb-4`}
                >
                    <Text
                        style={tw`text-[15px] text-[#455560] font-semibold px-4`}
                    >
                        Duration
                    </Text>
                    <View
                        style={tw`w-[144px]  border border-gray-400 rounded-full mr-4`}
                    >
                        <TextInput
                            style={tw` text-xs h-7 p-0 px-1.5`}
                            placeholder="Enter Duration"
                            keyboardType="numeric"
                        />
                    </View>
                </View>
            </View>
            <View>
                <View
                    style={tw`flex flex-col gap-2 border-b border-[#7d7d7d] pt-8 pb-4`}
                >
                    <Text
                        style={tw`text-[15px] text-[#455560] font-semibold px-4`}
                    >
                        Notes
                    </Text>
                    <Text
                        style={tw`text-[13px] text-[#455560] font-normal px-4`}
                    >
                        Lorem ipsum dolor sit amet consectetur. Bibendum sed
                        egestas consequat quis maecenas dolor lectus commodo.
                        Gravida massa nulla cur
                    </Text>
                </View>
            </View>

            <View style={tw` flex flex-row justify-center py-3`}>
                <TouchableOpacity
                    onPress={() => navigation.navigate('BOOKING_PAYMENT')}
                    style={tw`mt-5 w-[60%] bg-[#D9FD51] p-4 rounded-full `}
                >
                    <Text style={tw`text-center text-black text-md font-bold`}>
                        Book Class
                    </Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
};

export default memo(ClassesDetail);
