import React, { memo, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import Classes from './Classes';
import Appointments from './appointments';
import Bookings from './bookings';
import Courses from './courses';

const BookingTabs = ({ activeTab, setActiveTab, handleTabPress }) => {
    return (
        <>
            <View
                style={tw`flex flex-row justify-between px-5 pb-5 bg-#D0FF01]`}
            >
                <TouchableOpacity onPress={() => handleTabPress('Classes')}>
                    <Text
                        style={activeTab === 'Classes' ? tw`font-bold` : null}
                    >
                        Classes
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => handleTabPress('Appointment')}>
                    <Text
                        style={
                            activeTab === 'Appointment' ? tw`font-bold` : null
                        }
                    >
                        Appointment
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => handleTabPress('Bookings')}>
                    <Text
                        style={activeTab === 'Bookings' ? tw`font-bold` : null}
                    >
                        Bookings
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={() => handleTabPress('Courses')}>
                    <Text
                        style={activeTab === 'Courses' ? tw`font-bold` : null}
                    >
                        Courses
                    </Text>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default memo(BookingTabs);
