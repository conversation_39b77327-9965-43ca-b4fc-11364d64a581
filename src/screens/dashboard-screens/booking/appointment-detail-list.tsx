// import moment from 'moment';
import { useNavigation } from '@react-navigation/native';
import React, { memo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

const BookingData = [
    {
        id: 1,
        time: '7:00 am',
        duration: '60 min',
        instructor: '<PERSON><PERSON><PERSON>',
        classType: 'Morning Group Yoga',
        time_Standard: 'IST',
    },
    {
        id: 2,
        time: '8:30 am',
        duration: '45 min',
        instructor: '<PERSON>',
        classType: 'Pilates',
        time_Standard: 'IST',
    },
    {
        id: 3,
        time: '10:00 am',
        duration: '30 min',
        instructor: '<PERSON><PERSON><PERSON>',
        classType: 'Strength Training',
        time_Standard: 'IST',
    },
    {
        id: 4,
        time: '11:30 am',
        duration: '60 min',
        instructor: '<PERSON><PERSON>',
        classType: 'Cardio Blast',
        time_Standard: 'IST',
    },
    {
        id: 5,
        time: '1:00 pm',
        duration: '90 min',
        instructor: '<PERSON><PERSON>',
        classType: 'Advanced Yoga',
        time_Standard: 'IST',
    },
];

const AppointmentDetalList = () => {
    const navigation = useNavigation();
    return (
        <View style={tw``}>
            <Text style={tw`text-base px-4 text-[#455560] font-bold uppercase`}>
                {`Saturday, August 6`}
            </Text>

            {BookingData.map((item, index) => {
                return (
                    <TouchableOpacity
                        onPress={() => navigation.navigate('CLASSES_DETAIL')}
                    >
                        <View
                            key={index}
                            style={tw`flex flex-row gap-4 items-center border-b border-[#7d7d7d] py-3`}
                        >
                            <View
                                style={tw`flex flex-col gap-1.5 pl-4 w-[30%]`}
                            >
                                <Text
                                    style={tw`text-sm text-[#455560] font-bold uppercase`}
                                >
                                    {item.time}
                                </Text>
                                <Text
                                    style={tw`text-sm text-[#455560] uppercase`}
                                >
                                    {item.time_Standard}
                                </Text>
                                <Text
                                    style={tw`text-sm text-[#455560] uppercase`}
                                >
                                    {item.duration}
                                </Text>
                            </View>
                            <View style={tw`flex flex-col w-[70%] gap-1.5`}>
                                <Text
                                    style={tw`text-sm text-[#455560] font-bold uppercase`}
                                >
                                    {item.classType}
                                </Text>
                                <Text style={tw`text-sm text-[#455560] `}>
                                    {item.instructor}
                                </Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                );
            })}
        </View>
    );
};

export default memo(AppointmentDetalList);
