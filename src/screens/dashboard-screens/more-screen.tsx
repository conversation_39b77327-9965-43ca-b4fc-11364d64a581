import React from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import ProfileHeader from '~/components/common/profile-header';

const data = [
    {
        id: 1,
        type: 'Reset 3.0',
    },
    {
        id: 2,
        type: 'THE LAB* GROUP CLASSES',
    },
];

const MoreScreen = () => {
    return (
        <>
            <ProfileHeader />

            <View style={tw`bg-[#fafafa]`}>
                {data.map((item) => {
                    return (
                        <TouchableOpacity
                            key={item.id}
                            style={tw`flex flex-row gap-3 items-center px-5 py-3 border-b-2 border-[#ddd]`}
                        >
                            <Text
                                style={tw`text-[#455560] uppercase text-[15px] font-bold`}
                            >
                                {item.type}
                            </Text>
                        </TouchableOpacity>
                    );
                })}
            </View>
        </>
    );
};

export default MoreScreen;
