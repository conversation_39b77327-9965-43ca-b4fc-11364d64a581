import React, { useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

interface ProfileSettingProps {}

const ProfileSetting: React.FC<ProfileSettingProps> = () => {
    const [local_image, setLocalImage] = useState(null);
    const [uploadPopup, setUploadPopup] = useState(false);

    const props = {
        user_details: {
            profile_picture: `${Asset.ProfilePic}`, // Dummy profile picture URL
        },
    };
    return (
        <View>
            <BackButtonHeading Heading={'Profile Setting'} />
            <View style={tw`px-5`}>
                <View
                    style={tw`border-2  h-32  w-[100%] shadow-lg bg-black mt-5 rounded-5 flex flex-row justify-center items-center`}
                >
                    <Image
                        source={
                            local_image?.uri
                                ? { uri: local_image?.uri }
                                : props.user_details.profile_picture
                                ? {
                                      uri: props.user_details.profile_picture,
                                  }
                                : `${Asset.ProfilePic}`
                        }
                        style={tw`h-22 w-22 rounded-full border-2 border-white`}
                    />
                    <View style={tw` `}>
                        <TouchableOpacity
                            onPress={() => {
                                setUploadPopup(true);
                            }}
                            style={tw` flex flex-row w-[50] ml-5  bg-[#FFF] rounded-[50px] justify-center items-center`}
                        >
                            <Text
                                style={tw`text-[#000000] text-14 text-center font-400 py-3`}
                            >
                                Change Profile Photo
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={tw`flex flex-row justify-between mt-10 pr-2`}>
                    <Text style={tw`text-[#FFFFFF] font-700 text-18`}>
                        Personal Information
                    </Text>
                    <Image
                        // source={require('../../../assets/images/workout-creation-flow/editor.png')}
                        style={tw`h-6 w-6`}
                    />
                </View>
            </View>
        </View>
    );
};

export default ProfileSetting;

const styles = StyleSheet.create({});
