import React from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

import { useAppSelector } from '~/hooks/redux-hooks';

const ClientBasicAssessment = () => {
    const store = useAppSelector((state) => ({
        clientListDetails: state.client_store.clientDetails,
    }));

    const formatDate = (dateString) => {
        if (!dateString) return ''; // Handle null or undefined
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    };

    const measurement = [
        {
            icon: Asset.Chest,
            label: `Chest`,
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement.chest ??
                '--'
            } inch`,
        },
        {
            icon: Asset.Shoulder,
            label: 'Shoulder',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement
                    .shoulder ?? '--'
            } inch`,
        },
        {
            icon: Asset.Arm,
            label: 'Arm',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement.arm ??
                '--'
            } inch`,
        },
        {
            icon: Asset.Bicep,
            label: 'Bicep',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement
                    .biceps ?? '--'
            } inch`,
        },
        {
            icon: Asset.Forearm,
            label: 'Forearm',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement
                    .forearm ?? '--'
            } inch`,
        },
        {
            icon: Asset.Wrist,
            label: 'Wrist',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement.weist ??
                '--'
            } inch`,
        },
        {
            icon: Asset.Hip,
            label: 'Hip',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement.hip ??
                '--'
            } inch`,
        },
        {
            icon: Asset.Thigh,
            label: 'Thigh',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement
                    .theigh ?? '--'
            } inch`,
        },
        {
            icon: Asset.Calf,
            label: 'Calf',
            value: `${
                store?.clientListDetails?.basicAssessments?.measurement.calf ??
                '--'
            } inch`,
        },
    ];
    const heightWeight = [
        {
            icon: Asset.Weight,
            label: `What’s your weight?`,
            value: `${
                store?.clientListDetails?.basicAssessments?.weight ?? '--'
            } kg`,
        },
        {
            icon: Asset.Height,
            label: 'What’s your height?',
            value: `${
                store?.clientListDetails?.basicAssessments?.height ?? '--'
            } cm`,
        },
        {
            icon: Asset.Goal,
            label: 'Goal',
            value: store?.clientListDetails?.basicAssessments?.goal ?? '--',
        },
    ];
    console.log('first user info--------', store);
    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'Basic Assessment'} />

                <View style={tw`p-5 m-5 rounded-xl bg-white`}>
                    <Text
                        style={tw`text-18 font-semibold text-[#455560] uppercase border-b border-black/10 pb-2`}
                    >
                        Height & Weight{' '}
                    </Text>
                    <View style={tw`mt-5`}>
                        {heightWeight?.map((detail, index) => (
                            <View
                                key={index}
                                style={tw`flex flex-row items-start justify-between mt-4 `}
                            >
                                <View
                                    style={tw`flex flex-row items-center gap-2 w-[65%] `}
                                >
                                    <Image
                                        resizeMode="contain"
                                        style={tw`w-5 h-5`}
                                        source={detail.icon}
                                    />
                                    <Text
                                        style={tw`text-[#455560] text-[14px] font-bold `}
                                    >
                                        {detail.label}
                                    </Text>
                                </View>
                                <Text
                                    style={tw`w-[30%] text-[#45556066] text-[13px] `}
                                    numberOfLines={1} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                >
                                    {detail.value}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>

                <View style={tw`p-5 m-5 rounded-xl bg-white`}>
                    <Text
                        style={tw`text-18 font-semibold text-[#455560] uppercase border-b border-black/10 pb-2`}
                    >
                        Measurements{' '}
                    </Text>
                    <View style={tw`mt-5`}>
                        {measurement.map((detail, index) => (
                            <View
                                key={index}
                                style={tw`flex flex-row items-start justify-between mt-4 `}
                            >
                                <View
                                    style={tw`flex flex-row items-center gap-2 w-[70%] `}
                                >
                                    <Image
                                        resizeMode="contain"
                                        style={tw`w-5 h-5`}
                                        source={detail.icon}
                                    />
                                    <Text
                                        style={tw`text-[#455560] text-[14px] font-bold `}
                                    >
                                        {detail.label}
                                    </Text>
                                </View>
                                <Text
                                    style={tw`w-[25%] text-[#45556066] text-[13px] `}
                                    numberOfLines={1} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                >
                                    {detail.value}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default ClientBasicAssessment;

const styles = StyleSheet.create({});
