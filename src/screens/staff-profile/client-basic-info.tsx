import React from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

import { useAppSelector } from '~/hooks/redux-hooks';

const ClientBasicInfo = () => {
    const store = useAppSelector((state) => ({
        clientListDetails: state.client_store.clientDetails,
    }));

    const formatDate = (dateString) => {
        if (!dateString) return ''; // Handle null or undefined
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    };

    const clientDetails = [
        {
            icon: Asset.ClientIcon,
            label: 'First Name',
            value: store?.clientListDetails?.firstName,
        },
        {
            icon: Asset.ClientIcon,
            label: 'Last Name',
            value: store?.clientListDetails?.lastName,
        },
        {
            icon: Asset.GenderIcon,
            label: 'Gender',
            value: store?.clientListDetails?.gender,
        },
        {
            icon: Asset.DOBIcon,
            label: 'DOB',
            value: formatDate(store?.clientListDetails?.dob),
        },
        {
            icon: Asset.MailIcon,
            label: 'Email',
            value: store?.clientListDetails?.email,
        },
        {
            icon: Asset.PhoneIcon,
            label: 'Contact Number',
            value: store?.clientListDetails?.mobile,
        },
        {
            icon: Asset.ActivityIcon,
            label: 'Activity Level',
            value: store?.clientListDetails?.activityLevel,
        },
        {
            icon: Asset.ClientIcon,
            label: 'Emergency Contact Person',
            value: store?.clientListDetails?.emergencyContactPerson,
        },
        {
            icon: Asset.PhoneIcon,
            label: 'Emergency Contact Number',
            value: store?.clientListDetails?.emergencyContactPhone,
        },
        {
            icon: Asset.AddressIcon,
            label: 'Address',
            value: `${store?.clientListDetails?.address?.addressLine1}, ${store?.clientListDetails?.address?.addressLine2}, ${store?.clientListDetails?.address?.city}, ${store?.clientListDetails?.address?.country}, ${store?.clientListDetails?.address?.postalCode}`,
        },
    ];
    console.log('first user info--------', store.clientListDetails);
    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'General Information'} />

                <View style={tw`p-5 m-5 bg-white rounded-xl`}>
                    <Text
                        style={tw`text-18 text-[#455560] font-semibold uppercase border-b border-black/10 pb-2`}
                    >
                        Basic Information
                    </Text>
                    <View style={tw`mt-5`}>
                        {clientDetails.map((detail, index) => (
                            <View
                                key={index}
                                style={tw`flex flex-row items-start justify-between mt-4 `}
                            >
                                <View
                                    style={tw`flex flex-row items-center gap-2 w-[45%] `}
                                >
                                    <Image
                                        resizeMode="contain"
                                        style={tw`w-5 h-5`}
                                        source={detail.icon}
                                    />
                                    <Text
                                        style={tw`text-[#455560] text-[14px] font-bold `}
                                    >
                                        {detail.label}
                                    </Text>
                                </View>
                                <Text
                                    style={tw`w-[48%] text-[#45556066] text-[13px] `}
                                    numberOfLines={2} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                >
                                    {detail.value}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default ClientBasicInfo;

const styles = StyleSheet.create({});
