import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useState } from 'react';
import {
    Alert,
    Image,
    Linking,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { DeleteAccount, Logout } from '~/redux/actions/auth-actions';
import { GetStaffDetails } from '~/redux/actions/client-action';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';
import TrashIcon from '~/assets/svg/trash_icon.svg';

import { GET_STAFF_DETAILS } from '~/constants/api-constant';
import { UserRole } from '~/constants/enums';
import {
    ACTIVITY_LEVEL,
    BASIC_INFORMATION_SCREEN,
    CLIENT_CHANGE_PASSWORD,
    CLIENT_LISTING,
    FACILITY_DETAILS,
    LOGIN_SCREEN,
    MEASUREMENT_SCREEN,
    QR_CODE_SCREEN,
    SELECT_ORGANIZATION,
    STAFF_DETAIL,
    STAFF_LISTING,
    WEI<PERSON>HT_SCREEN,
} from '~/constants/navigation-constant';

import ProfileHeader from '~/components/common/profile-header';

import Alertify from '~/scripts/toast';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

const StaffProfile = ({ navigation }: any) => {
    const dispatch = useAppDispatch();
    const [loading, setLoading] = useState(true);
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        firstName: state.auth_store.firstName,
        lastName: state.auth_store.lastName,
        profileImage: state.profile_store.personalDetails.photo,
        staffDetails: state.client_store.staffDetails,
        userId: state.auth_store.userId,
        isPasswordSet: state.auth_store.isPasswordSet,
    }));
    const clientId = useAppSelector((state) => state.auth_store.userId);

    console.log('first clientId--------', clientId);
    console.log('staff Id -------', store.isPasswordSet);

    // const handleStaffDetails = (clientId: any) => {
    //     navigation.navigate(GET_STAFF_DETAILS);
    //     dispatch(GetStaffDetails({ clientId: clientId }));
    // };

    const data = [
        {
            id: 1,
            type: 'Clients',
            screenName: CLIENT_LISTING,
            icon: Asset.ClientIcon,
        },
        ...(store.role !== UserRole.ORGANIZATION
            ? [
                  {
                      id: 2,
                      type: 'Profile',
                      screenName: STAFF_DETAIL,
                      icon: Asset.ProfileIcon,
                  },
              ]
            : []),
        // {
        //     id: 2,
        //     type: 'Profile',
        //     screenName: STAFF_DETAIL,
        //     icon: Asset.ProfileIcon,
        // },
        // {
        //     id: 2,
        //     type: 'attendance ',
        //     // screenName: CLIENT_LISTING,
        //     icon: Asset.AttendanceIcon,
        // },
        // {
        //     id: 3,
        //     type: 'Reviews',
        //     // screenName: CLIENT_LISTING,
        //     icon: Asset.ReviewsIcon,
        // },
        // {
        //     id: 4,
        //     type: 'Contact',
        //     // screenName: CLIENT_LISTING,
        //     icon: Asset.ContactIcon,
        // },
        // {
        //     id: 5,
        //     type: 'Notifications',
        //     // screenName: CLIENT_LISTING,
        //     icon: Asset.NotificationIcon,
        // },
        {
            id: 4,
            type: 'My Staff',
            icon: Asset.InfoOutlinedIcon,
            screenName: STAFF_LISTING,
        },
        {
            id: 6,
            type: store.isPasswordSet ? 'Change Password' : 'Set Password',
            screenName: CLIENT_CHANGE_PASSWORD,
            icon: Asset.Edit,
        },
        {
            id: 7,
            type: 'Facility Info',
            screenName: FACILITY_DETAILS,
            icon: Asset.Setting,
        },
        {
            id: 8,
            type: 'Privacy policy',
            screenName: 'Privacy Policy',
            icon: Asset.PrivacyIcon,
            url: 'https://hopwellness.ai/privacy-policy',
        },

        {
            id: 9,
            type: 'Log out',
            screenName: LOGIN_SCREEN,
            icon: Asset.LogOut,
        },
        {
            id: 10,
            type: 'Qr Code',
            screenName: QR_CODE_SCREEN,
            icon: Asset.LogOut,
        },
        {
            id: 14,
            type: 'Delete My Account',
            icon: 'svg',
            screenName: 'Delete Account',
        },
    ];

    useFocusEffect(
        useCallback(() => {
            if (!clientId) return;

            setLoading(true);
            dispatch(GetStaffDetails({ clientId })).then(() => {
                setLoading(false);
            });
        }, [])
    );
    console.log('Staff details', store);

    async function handleNavigation(item: any) {
        if (item.type === 'Log out') {
            Alert.alert(
                'Confirm Logout',
                'Are you sure you want to logout?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel',
                    },
                    {
                        text: 'Logout',
                        style: 'destructive',
                        onPress: async () => {
                            try {
                                await dispatch(Logout());
                                Alertify.success('Logout successful');
                                navigation.navigate(SELECT_ORGANIZATION);
                            } catch (error) {
                                Alertify.error(
                                    'Logout failed. Please try again.'
                                );
                            }
                        },
                    },
                ],
                { cancelable: true }
            );
        } else if (item.url) {
            Linking.openURL(item.url);
        } else if (item.id === 14) {
            const reqData = {
                role: store.role,
                userId: store.userId,
            };
            Alert.alert(
                'Delete Account',
                'Are you sure you want to request for delete your account?',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Confirm',
                        style: 'destructive',
                        onPress: () => {
                            dispatch(DeleteAccount(reqData));
                        },
                    },
                ],
                { cancelable: true }
            );
        } else {
            navigation.navigate(item.screenName);
            console.log('first---------show ', item);
        }
    }

    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <ProfileHeader />
                <View style={tw`mb-5 px-5`}>
                    {data
                        .filter((item) => {
                            if (
                                store.role === UserRole.Trainer &&
                                item.type === 'My Staff'
                            )
                                return false;
                            return true;
                        })
                        .map((item) => {
                            const isResetIcon = item.type === 'Reset 3.0';
                            const isDeleteAccount = item.id === 14;
                            return (
                                <TouchableOpacity
                                    key={item.id}
                                    style={tw`flex flex-row gap-5 items-center px-1 py-4 border-b border-black/10`}
                                    onPress={() => handleNavigation(item)}
                                >
                                    {isDeleteAccount ? (
                                        <TrashIcon
                                            style={tw`text-red-600`}
                                            width={20}
                                            height={20}
                                        />
                                    ) : (
                                        <Image
                                            style={
                                                isResetIcon
                                                    ? tw`w-5 h-5`
                                                    : tw`w-6 h-6`
                                            }
                                            resizeMode="contain"
                                            source={item.icon}
                                        />
                                    )}

                                    <Text
                                        style={tw`${
                                            isDeleteAccount
                                                ? 'text-red-600'
                                                : 'text-[#455560]'
                                        } uppercase text-base`}
                                    >
                                        {item.type}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                </View>
            </ScrollView>
        </View>
    );
};

export default StaffProfile;

const styles = StyleSheet.create({});
