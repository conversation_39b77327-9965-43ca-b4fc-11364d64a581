import { useFocusEffect, useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Animated,
    Button,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    GetClientDetails,
    GetClientListing,
    GetClientListingByRole,
} from '~/redux/actions/client-action';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { CLIENT_DETAILS } from '~/constants/navigation-constant';

import TextInput from '~/components/atoms/text-input';
import BackButtonHeading from '~/components/common/back-button-heading';
import SearchBar from '~/components/common/search-bar';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';

export const ClientSkeleton = ({ count }: any) => {
    const opacity = useRef(new Animated.Value(0.6)).current;

    useEffect(() => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 500,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0.6,
                    duration: 500,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    }, []);

    return (
        <View>
            {[...Array(count)]?.map((_, index) => (
                <View
                    key={index}
                    style={tw`flex flex-row w-full mt-5 border-b border-gray-300 pb-3`}
                >
                    {/* Profile Image Skeleton */}
                    <View style={tw`w-[20%]`}>
                        <Animated.View
                            style={[
                                tw`bg-gray-300 rounded-full w-16 h-16`,
                                { opacity },
                            ]}
                        />
                    </View>

                    {/* Client Name and Age Skeleton */}
                    <View style={tw`w-[40%] px-2`}>
                        <Animated.View
                            style={[
                                tw`w-32 h-4 bg-gray-300 rounded`,
                                { opacity },
                            ]}
                        />
                        <Animated.View
                            style={[
                                tw`w-24 h-4 bg-gray-300 rounded mt-2`,
                                { opacity },
                            ]}
                        />
                    </View>

                    {/* Status Skeleton */}
                    <View style={tw`w-[30%] flex items-start`}>
                        <Animated.View
                            style={[
                                tw`w-16 h-4 bg-gray-300 rounded mt-2`,
                                { opacity },
                            ]}
                        />
                    </View>

                    {/* Arrow Icon Skeleton */}
                    <View style={tw`w-[10%] flex justify-center items-center`}>
                        <Animated.View
                            style={[
                                tw`w-5 h-5 bg-gray-300 rounded-full`,
                                { opacity },
                            ]}
                        />
                    </View>
                </View>
            ))}
        </View>
    );
};

export function getRandomColor(seed: string) {
    const colors = [
        '#F7DFF6',
        '#A1B4FF',
        '#B4C1C7',
        '#FFD09E',
        '#DDA0DD',
        '#A4E9FF',
        '#F5DEB3',
    ];
    const index = seed.charCodeAt(0) % colors.length;
    return colors[index];
}

const itemsPerPage = 10;
const ClientListing = () => {
    const [currentPage, setCurrentPage] = useState(1);
    const [loading, setLoading] = useState(true);
    const dispatch = useAppDispatch();
    const [clientSearch, setClientSearch] = useState('');
    const debouncedRequest = useDebounce((callback: any) => callback(), 200);

    const store = useAppSelector((state) => ({
        clientListData: state.client_store.clientListByStaff,
        clientListCount: state.client_store.clientListCountByStaff,
    }));

    console.log('client list---------', store.clientListData);

    // Get the data for the current page
    const indexOfLastUser = currentPage * itemsPerPage;
    const indexOfFirstUser = indexOfLastUser - itemsPerPage;

    // Calculate total number of pages
    const totalPages = Math.ceil(store?.clientListCount / itemsPerPage);

    // Handle next and previous page
    const nextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const prevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const navigation = useNavigation();

    //  useFocusEffect(
    //         useCallback(() => {
    //             const payload = {
    //                 page: 1,
    //                 pageSize: 50,
    //                 facilityId: store.selectedFacility,
    //                 isActive: true,
    //             };
    //             dispatch(GetClientListingByRole(payload));
    //         }, [])
    //     );

    useFocusEffect(
        useCallback(() => {
            setLoading(true);
            if (clientSearch) {
                const payload = {
                    page: currentPage,
                    pageSize: 10,
                    search: clientSearch,
                };
                debouncedRequest(() => {
                    dispatch(GetClientListingByRole(payload)).then(() => {
                        setLoading(false);
                    });
                });
            } else {
                const payload = {
                    page: currentPage,
                    pageSize: 10,
                };
                dispatch(GetClientListingByRole(payload)).then(() => {
                    setLoading(false);
                });
            }
        }, [clientSearch, currentPage])
    );

    const handleClientDetails = (clientId: string) => {
        (navigation as any).navigate(CLIENT_DETAILS);
        dispatch(GetClientDetails({ clientId: clientId }));
    };

    const handleSearch = (searchText: string) => {
        setClientSearch(searchText);
        setCurrentPage(1);
    };

    const calculateAge = (dob: any) => {
        const birthDate = new Date(dob);
        const ageDiffMs = Date.now() - birthDate.getTime();
        const ageDate = new Date(ageDiffMs);
        return Math.abs(ageDate.getUTCFullYear() - 1970);
    };

    // function renderProfileImage(img: any) {
    //     if (img) {
    //         return { uri: img };
    //     }
    //     return Asset.ProfileIconNew;
    // }

    function renderProfileImage(img: any) {
        return img ? { uri: img } : null;
    }

    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'Clients Listing'} />
                <View style={tw`px-2 py-4`}>
                    <View style={tw`bg-white p-2 rounded-md shadow-md`}>
                        {/* <SearchBar /> */}

                        <View
                            style={tw`bg-[#F7F7F7] rounded-xl my-3 shadow-md py-1 px-2 flex flex-row justify-start items-center`}
                        >
                            <View style={tw`w-[10%]`}>
                                <Image
                                    resizeMode="contain"
                                    style={tw`w-6 h-6`}
                                    source={Asset.SearchIcon}
                                />
                            </View>
                            <View style={tw`w-[75%]`}>
                                <TextInput
                                    value={clientSearch}
                                    onChangeText={handleSearch}
                                    placeholder={'Search'}
                                    style={tw` px-2   text-[#3b3b3b]  text-14 font-400`}
                                />
                            </View>
                            {/* <TouchableOpacity
                                style={tw`w-[15%] flex justify-center items-center`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={tw`w-7 h-7`}
                                    source={Asset.FilterIcon}
                                />
                            </TouchableOpacity> */}
                        </View>

                        {loading ? (
                            // <View
                            //     style={tw`flex-1 justify-center items-center`}
                            // >
                            //     <ActivityIndicator
                            //         size="large"
                            //         color="#8143D1"
                            //     />
                            // </View>
                            <ClientSkeleton count={store?.clientListCount} />
                        ) : (
                            <View style={tw`px-4 pt-4`}>
                                <Text
                                    style={tw`text-[#455560] text-14 font-semibold`}
                                >
                                    Total Clients : {store?.clientListCount}
                                </Text>
                                <View>
                                    {store?.clientListData?.map(
                                        (person: any) => (
                                            <TouchableOpacity
                                                onPress={() => {
                                                    handleClientDetails(
                                                        person._id
                                                    );
                                                }}
                                                key={person.id}
                                                style={tw`flex flex-row w-full mt-4 border-b border-black/10 pb-2`}
                                            >
                                                <View style={tw`w-[20%]`}>
                                                    {renderProfileImage(
                                                        person.photo
                                                    ) ? (
                                                        <Image
                                                            style={tw`h-12 w-12 rounded-full`}
                                                            source={renderProfileImage(
                                                                person.photo
                                                            )}
                                                            resizeMode="cover"
                                                        />
                                                    ) : (
                                                        <View
                                                            style={[
                                                                tw`h-12 w-12 rounded-full justify-center items-center`,
                                                                {
                                                                    backgroundColor:
                                                                        getRandomColor(
                                                                            person.firstName ||
                                                                                'A'
                                                                        ),
                                                                },
                                                            ]}
                                                        >
                                                            <Text
                                                                style={tw`text-white text-lg font-bold`}
                                                            >
                                                                {person.firstName
                                                                    ?.charAt(0)
                                                                    .toUpperCase() ||
                                                                    'U'}
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>

                                                <View style={tw`w-[40%] px-2`}>
                                                    <View>
                                                        <Text
                                                            style={tw`text-[16px] font-semibold text-[#455560]`}
                                                        >
                                                            {person.firstName}
                                                            {` `}
                                                            {person.lastName}
                                                        </Text>
                                                        {person?.dob && (
                                                            <Text
                                                                style={tw`text-[14px] text-[#455560]`}
                                                            >
                                                                Age:{' '}
                                                                {calculateAge(
                                                                    person.dob
                                                                )}{' '}
                                                                Years
                                                            </Text>
                                                        )}
                                                    </View>
                                                </View>
                                                <View
                                                    style={tw`w-[30%] flex items-start`}
                                                >
                                                    <View
                                                        style={tw`flex flex-row items-center justify-around w-full`}
                                                    >
                                                        <Text
                                                            style={tw`text-[16px] font-semibold text-[#455560]`}
                                                        >
                                                            Status
                                                        </Text>
                                                        <View
                                                            style={[
                                                                tw`rounded-full w-2 h-2`,
                                                                {
                                                                    backgroundColor:
                                                                        person.isActive
                                                                            ? '#3FB249' // Green if active
                                                                            : '#FF0000', // Red if inactive
                                                                },
                                                            ]}
                                                        />
                                                    </View>
                                                </View>
                                                <View
                                                    style={tw`w-[10%] flex justify-start translate-y-2 items-start`}
                                                >
                                                    <View>
                                                        <Image
                                                            style={tw`w-5 h-5`}
                                                            resizeMode="contain"
                                                            source={
                                                                Asset.RightArrowIcon
                                                            }
                                                        />
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        )
                                    )}
                                </View>

                                {/* Show Pagination only if clientListCount is more than 10 */}
                                {store?.clientListCount > 10 && (
                                    <View
                                        style={tw`flex flex-row justify-between items-center my-5`}
                                    >
                                        <TouchableOpacity
                                            onPress={prevPage}
                                            disabled={currentPage === 1}
                                            style={tw`flex flex-row justify-center items-center`}
                                        >
                                            <Image
                                                style={tw`w-7 h-7`}
                                                source={Asset.PreviousIcon}
                                            />
                                            <Text
                                                style={tw`font-semibold leading-none text-black`}
                                            >
                                                Previous
                                            </Text>
                                        </TouchableOpacity>
                                        <Text style={tw`text-black`}>
                                            Page {currentPage} of {totalPages}
                                        </Text>
                                        <TouchableOpacity
                                            onPress={nextPage}
                                            disabled={
                                                currentPage === totalPages
                                            }
                                            style={tw`flex flex-row justify-center items-center`}
                                        >
                                            <Text
                                                style={tw`font-semibold text-black leading-none`}
                                            >
                                                Next
                                            </Text>
                                            <Image
                                                style={tw`w-7 h-7`}
                                                resizeMode="cover"
                                                source={Asset.NextIcon}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default ClientListing;

const styles = StyleSheet.create({});
