import { <PERSON>ik, FormikHelpers } from 'formik';
import React from 'react';
import {
    Alert,
    Image,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    Logout,
    RegisteredUserChangePassword,
    SetPassword,
    StaffResetPassword,
} from '~/redux/actions/auth-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { Button } from '~/components/atoms';
import TextInput from '~/components/atoms/text-input';
import BackButtonHeading from '~/components/common/back-button-heading';

import Alertify from '~/scripts/toast';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { navigationRef } from '~/hooks/useLocation';

// Define the form values type
interface FormValues {
    oldPassword: string;
    password: string;
    confirmPassword: string;
}

const ClientChangesPassword: React.FC = () => {
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        isPasswordSet: state.auth_store.isPasswordSet,
        userId: state.auth_store.userId,
    }));

    console.log('Store------------', store);

    const validatePassword = (password: string) => {
        const validation = {
            lengthValid: password.length >= 8 && password.length <= 30,
            upperCaseValid: /[A-Z]/.test(password),
            lowerCaseValid: /[a-z]/.test(password),
            numberOrSpecialValid: /[0-9!@#$%^&*(),.?":{}|<>]/.test(password),
        };
        return validation;
    };

    const onBack = () => {
        navigationRef.goBack();
    };

    // Handle form submission
    const handleSubmit = (
        values: FormValues,
        { setSubmitting, resetForm }: FormikHelpers<FormValues>
    ) => {
        if (store.isPasswordSet && !values.oldPassword) {
            Alertify.error('Old password is required');
            setSubmitting(false);
            return;
        }

        if (!values.password) {
            Alertify.error('New password is required');
            setSubmitting(false);
            return;
        }

        if (!values.confirmPassword) {
            Alertify.error('Confirm password is required');
            setSubmitting(false);
            return;
        }

        if (values.password !== values.confirmPassword) {
            Alertify.error('Passwords do not match');
            setSubmitting(false); // Stop submission state
            return;
        }

        // Dispatch the action to change password
        const payload = {
            newPassword: values.password,
            confirmPassword: values.confirmPassword,
        };

        const action = store.isPasswordSet
            ? RegisteredUserChangePassword({
                  ...payload,
                  oldPassword: values.oldPassword,
              })
            : SetPassword({ ...payload });

        dispatch(action).then((res) => {
            console.log('Res-------------', res);
            if (
                res?.payload?.statusCode === 400 ||
                res?.payload?.statusCode === 401
            ) {
                Alertify.error(res?.payload.message[0]);
            } else {
                // dispatch(Logout());
                Alertify.success(res?.payload?.res?.data?.message);

                resetForm();
            }
            setSubmitting(false); // Stop submission state
        });
    };

    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <View style={tw`px-4`}>
                    <View
                        style={tw`flex justify-center items-center mt-5 mb-10`}
                    >
                        <Image
                            style={tw`w-16 h-16`}
                            source={Asset.PasswordImg}
                            resizeMode="cover"
                        />
                        <Text
                            style={tw`text-xl uppercase text-[#455560] font-medium mt-2`}
                        >
                            {store.isPasswordSet
                                ? 'Change Your Password'
                                : 'Set Your Password'}
                        </Text>
                        <Text style={tw`text-14 text-[#455560] text-center`}>
                            In order to keep your account safe you need to
                            create a strong password.
                        </Text>
                    </View>

                    <Formik
                        initialValues={{
                            oldPassword: '',
                            password: '',
                            confirmPassword: '',
                        }}
                        onSubmit={handleSubmit}
                    >
                        {({
                            handleSubmit,
                            handleChange,
                            values,
                            handleBlur,
                            isSubmitting,
                        }) => {
                            const validation = validatePassword(
                                values.password
                            );

                            return (
                                <>
                                    <View style={tw`flex flex-col gap-3`}>
                                        {/* Old Password Input */}
                                        {store.isPasswordSet && (
                                            <TextInput
                                                stacked
                                                type="password"
                                                label="Old Password"
                                                onChangeText={handleChange(
                                                    'oldPassword'
                                                )}
                                                onBlur={handleBlur(
                                                    'oldPassword'
                                                )}
                                                value={values.oldPassword}
                                            />
                                        )}
                                        {/* New Password Input */}
                                        <TextInput
                                            stacked
                                            type="password"
                                            label="New Password"
                                            onChangeText={handleChange(
                                                'password'
                                            )}
                                            onBlur={handleBlur('password')}
                                            value={values.password}
                                        />
                                        {/* Confirm Password Input */}
                                        <TextInput
                                            stacked
                                            type="password"
                                            label="Confirm Password"
                                            onChangeText={handleChange(
                                                'confirmPassword'
                                            )}
                                            onBlur={handleBlur(
                                                'confirmPassword'
                                            )}
                                            value={values.confirmPassword}
                                        />
                                    </View>
                                    <View>
                                        <Text
                                            style={tw`text-[#1A3353] text-14 mt-5`}
                                        >
                                            YOUR PASSWORD MUST CONTAIN
                                        </Text>
                                        <View
                                            style={tw`mb-1 mt-3 flex flex-row gap-4 items-center`}
                                        >
                                            <Image
                                                resizeMode="contain"
                                                style={tw`w-5 h-5 border border-gray-200 rounded-full`}
                                                source={
                                                    validation.lengthValid
                                                        ? Asset.CheckCircleIcon
                                                        : Asset.UncheckCircleIcon
                                                }
                                            />
                                            <Text
                                                style={tw`text-[#45556080] text-14`}
                                            >
                                                Between 8 and 30 characters
                                            </Text>
                                        </View>
                                        <View
                                            style={tw`my-1 flex flex-row gap-4 items-center`}
                                        >
                                            <Image
                                                resizeMode="contain"
                                                style={tw`w-5 h-5 border border-gray-200 rounded-full`}
                                                source={
                                                    validation.upperCaseValid
                                                        ? Asset.CheckCircleIcon
                                                        : Asset.UncheckCircleIcon
                                                }
                                            />
                                            <Text
                                                style={tw`text-[#45556080] text-14`}
                                            >
                                                At least one upper case letter
                                            </Text>
                                        </View>
                                        <View
                                            style={tw`my-1 flex flex-row gap-4 items-center`}
                                        >
                                            <Image
                                                resizeMode="contain"
                                                style={tw`w-5 h-5 border border-gray-200 rounded-full`}
                                                source={
                                                    validation.lowerCaseValid
                                                        ? Asset.CheckCircleIcon
                                                        : Asset.UncheckCircleIcon
                                                }
                                            />
                                            <Text
                                                style={tw`text-[#45556080] text-14`}
                                            >
                                                At least one lower case letter
                                            </Text>
                                        </View>
                                        <View
                                            style={tw`my-1 flex flex-row gap-4 items-center`}
                                        >
                                            <Image
                                                resizeMode="contain"
                                                style={tw`w-5 h-5 border border-gray-200 rounded-full`}
                                                source={
                                                    validation.numberOrSpecialValid
                                                        ? Asset.CheckCircleIcon
                                                        : Asset.UncheckCircleIcon
                                                }
                                            />
                                            <Text
                                                style={tw`text-[#45556080] text-14`}
                                            >
                                                At least one number or special
                                                character
                                            </Text>
                                        </View>
                                    </View>
                                    <View
                                        style={tw`flex flex-row justify-around items-center`}
                                    >
                                        <TouchableOpacity
                                            style={tw`w-[40%] flex justify-center px-4 py-3 gap-2 items-center rounded-full mt-10 bg-white border`}
                                            onPress={onBack}
                                        >
                                            <Text
                                                style={tw`text-[#000] text-14`}
                                            >
                                                Cancel
                                            </Text>
                                        </TouchableOpacity>
                                        <Button
                                            style={tw`w-[40%] rounded-full mt-10`}
                                            onPress={handleSubmit}
                                            // nextImage
                                            // disabled={isSubmitting} // Disable the button when submitting
                                        >
                                            Submit
                                        </Button>
                                    </View>
                                </>
                            );
                        }}
                    </Formik>
                </View>
            </ScrollView>
        </View>
    );
};

export default ClientChangesPassword;
