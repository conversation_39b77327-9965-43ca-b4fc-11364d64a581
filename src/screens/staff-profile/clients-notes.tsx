import React, { memo } from 'react';
import { Text, View } from 'react-native';

import tw from '~/styles/tailwind';

import { TextInput } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';

const ClientsNotes = () => {
    return (
        <View style={tw``}>
            <BackButtonHeading Heading={'Notes'} />
            <View style={tw`mt-8 px-5`}>
                <Text style={tw`text-MainTextColor font-medium text-14 pb-2`}>
                    Notes
                </Text>
                <TextInput
                    style={tw`w-[100%]  border-b border-[#45556066] rounded-md `}
                    multiline
                    maxLength={300}
                    placeholder="Write your note here"
                />
            </View>
        </View>
    );
};

export default memo(ClientsNotes);
