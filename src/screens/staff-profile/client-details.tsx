import { useNavigation } from '@react-navigation/native';
import {
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import {
    CLIENTS_NOTES,
    CLIENT_BASIC_ASSESSMENT,
    CLIENT_BASIC_INFO,
} from '~/constants/navigation-constant';

import BackButtonHeading from '~/components/common/back-button-heading';
import { formatDate } from '~/components/common/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

import { getRandomColor } from './client-listing';

function renderProfileImage(img: any) {
    if (img) {
        return { uri: img };
    }
    return Asset.ProfileIconNew;
}

const items = [
    {
        id: 1,
        icon: Asset.GeneralIcon,
        label: 'General Information',
        screen_nav: CLIENT_BASIC_INFO,
    },
    {
        id: 2,
        icon: Asset.InfoIcon,
        label: 'Basic Assessment',
        screen_nav: CLIENT_BASIC_ASSESSMENT,
    },
    {
        id: 3,
        icon: Asset.NotesIcon,
        label: 'Notes',
        screen_nav: CLIENTS_NOTES,
    },
    // {
    //     id: 4,
    //     icon: Asset.PolicyIcon,
    //     label: 'Policies',
    //     screen_nav: CLIENT_BASIC_INFO,
    // },
    // {
    //     id: 5,
    //     icon: Asset.GeneralIcon,
    //     label: 'HISTORY',
    //     screen_nav: CLIENT_BASIC_INFO,
    // },
];

const ClientDetails = ({}) => {
    // const { name, age, status, statusColor, personImg, clientId } =
    //     route.params;
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        clientListDetails: state.client_store.clientDetails,
        userType: state.client_store.clientList,
    }));

    console.log('first--- clientDetails', store);

    const navigation = useNavigation();

    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'Client Profile'} />

                <View style={tw`p-5 mt-3 `}>
                    <View
                        style={tw` flex flex-row items-center  justify-between`}
                    >
                        <View style={tw` flex flex-row items-center gap-2 `}>
                            <View
                                style={tw`w-18 h-18 border border-gray-300 rounded-full overflow-hidden`}
                            >
                                {store.clientListDetails?.photo ? (
                                    <Image
                                        resizeMode="contain"
                                        style={tw`w-full h-full  rounded-full`}
                                        // source={{
                                        //     uri: store.clientListDetails?.photo,
                                        // }}
                                        source={renderProfileImage(
                                            store.clientListDetails?.photo
                                        )}
                                    />
                                ) : (
                                    <View
                                        style={[
                                            tw`h-18 w-18 rounded-full justify-center items-center`,
                                            {
                                                backgroundColor: getRandomColor(
                                                    store.clientListDetails
                                                        ?.firstName || 'A'
                                                ),
                                            },
                                        ]}
                                    >
                                        <Text
                                            style={tw`text-white text-lg font-bold`}
                                        >
                                            {store.clientListDetails?.firstName
                                                ?.charAt(0)
                                                .toUpperCase() || 'U'}
                                        </Text>
                                    </View>
                                )}
                            </View>
                            <View style={tw` flex flex-col gap-1 w-[55%] `}>
                                <Text
                                    style={tw`text-sm font-bold text-[#455560]`}
                                >
                                    {store.clientListDetails?.firstName}{' '}
                                    {store.clientListDetails?.lastName}
                                </Text>
                                <Text
                                    numberOfLines={1} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                    style={tw`text-sm font-bold text-[#455560] `}
                                >
                                    Client Id &nbsp;
                                    <Text
                                        style={tw`font-normal text-xs  text-[#72849A]`}
                                    >
                                        {store.clientListDetails?.clientId}
                                    </Text>
                                </Text>
                            </View>
                        </View>
                        <View
                            style={tw`bg-white shadow-md flex flex-col  py-1 px-2.5 rounded-lg`}
                        >
                            <Text
                                style={tw`text-sm font-medium text-[#455560] `}
                            >
                                User Type
                            </Text>

                            {/* <Text
                                style={tw`text-sm font-normal text-[#455560] capitalize`}
                            >
                                Active
                            </Text> */}
                            <View
                                style={tw`flex flex-row items-center gap-2 w-full`}
                            >
                                <View
                                    style={[
                                        tw`rounded-full w-2 h-2`,
                                        {
                                            backgroundColor: store
                                                .clientListDetails?.isActive
                                                ? '#3FB249' // Green if active
                                                : '#FF0000', // Red if inactive
                                        },
                                    ]}
                                />
                                <Text
                                    style={tw`text-14 font-normal text-black`}
                                >
                                    {store.clientListDetails?.isActive
                                        ? 'Active'
                                        : 'Inactive'}
                                </Text>
                            </View>
                        </View>
                    </View>
                    <View style={tw`mt-3 flex flex-row gap-28 `}>
                        <View>
                            <Text
                                style={tw`text-sm  text-[#455560] font-medium`}
                            >
                                Date Joined
                            </Text>
                            <Text
                                style={tw`text-xs  text-[#455560]  font-normal`}
                            >
                                {formatDate(store.clientListDetails?.createdAt)}
                            </Text>
                        </View>
                        <View>
                            <Text
                                style={tw`text-sm  text-[#455560] font-medium`}
                            >
                                BOOKINGS/VISIT
                            </Text>
                            <Text
                                style={tw`text-xs   text-[#455560] font-normal`}
                            >
                                {store.clientListDetails?.totalBookedSessions}
                            </Text>
                        </View>
                    </View>
                </View>
                <View style={tw` flex flex-col mt-3`}>
                    <View style={tw`flex flex-col gap-3 px-5 `}>
                        {items?.map((item: any) => (
                            <TouchableOpacity
                                onPress={() =>
                                    (navigation as any).navigate(
                                        item.screen_nav
                                    )
                                }
                                key={item.id}
                                style={tw``}
                            >
                                <Text
                                    style={tw`uppercase  text-[#455560] text-16 border-b pb-3 px-2 border-black/10 border-opacity-10 font-normal `}
                                >
                                    {item.label}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default ClientDetails;
