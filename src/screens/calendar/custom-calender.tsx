import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import {
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { getCategoryColor } from './calendar-screen';

const ROW_HEIGHT = 120;

const TIME_SLOTS = Array.from(
    { length: 24 },
    (_, i) => `${i.toString().padStart(2, '0')}:00`
);

const getMinutesSinceMidnight = (time: string | Date) => {
    // Parse the time without timezone conversion to avoid offset issues
    const date = dayjs(time);
    return date.hour() * 60 + date.minute();
};

const CustomTrainerCalendarList = ({
    selectedDate,
    trainers = [],
    events = [],
    selectedTrainer,
    onTrainerPress,
    onEventPress,
    workingHours,
}: any) => {
    const selectedDateStr = dayjs(selectedDate).format('YYYY-MM-DD');
    const scrollRefs = useRef<{ [key: string]: ScrollView | null }>({});
    const verticalScrollRef = useRef<ScrollView>(null);

    const [currentMinutes, setCurrentMinutes] = useState(() => {
        const now = dayjs();
        return now.hour() * 60 + now.minute();
    });

    useEffect(() => {
        const interval = setInterval(() => {
            const now = dayjs();
            setCurrentMinutes(now.hour() * 60 + now.minute());
        }, 60000);
        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        const now = dayjs();
        const isToday = dayjs(selectedDate).isSame(now, 'day');

        let targetHour = 7; // default opening hour

        if (isToday) {
            const minutesSinceMidnight = now.hour() * 60 + now.minute();
            const adjustedMinutes = Math.max(minutesSinceMidnight - 60, 0);
            const offset = (adjustedMinutes / 60) * ROW_HEIGHT;
            verticalScrollRef.current?.scrollTo({ y: offset, animated: false });
        }
        // else {
        //     // fallback: scroll to opening hour based on working hours
        //     const dayKey = dayjs(selectedDate)
        //         .format('ddd')
        //         .toLowerCase()
        //         .slice(0, 3);

        //     const dayHours = workingHours?.[dayKey];
        //     if (dayHours && dayHours.length > 0) {
        //         const from = dayHours[0].from;
        //         const hour = parseInt(from.split(':')[0], 10);
        //         if (!isNaN(hour)) {
        //             targetHour = hour;
        //         }
        //     }

        //     const offset = targetHour * 90;
        //     verticalScrollRef.current?.scrollTo({ y: offset, animated: false });
        // }
    }, [selectedDate, workingHours]);

    const headerScrollRef = useRef<ScrollView>(null);
    const bodyScrollRef = useRef<ScrollView>(null);

    const syncScroll = (event: any) => {
        const offsetX = event.nativeEvent.contentOffset.x;
        headerScrollRef.current?.scrollTo({ x: offsetX, animated: false });
    };

    const screenWidth = Dimensions.get('window').width;
    const columnCount = trainers.length > 0 ? Math.min(trainers.length, 5) : 1;
    const trainerColumnWidth = screenWidth / columnCount;

    useEffect(() => {
        const yOffset = 7 * 90;
        Object.values(scrollRefs.current).forEach((ref) => {
            ref?.scrollTo({ y: yOffset, animated: false });
        });
    }, []);

    const getTrainerColumnWidth = () => {
        const count = trainers.length;

        if (count === 1) return screenWidth - 60;
        if (count === 2) return (screenWidth - 60) / 2;
        return 130;
    };

    console.log('events-------', events);

    const assignEventColumns = (events: any[]) => {
        const sortedEvents = [...events].sort(
            (a, b) =>
                getMinutesSinceMidnight(a.start) -
                getMinutesSinceMidnight(b.start)
        );

        const result = [];
        const columns: any[] = [];

        for (const event of sortedEvents) {
            const start = getMinutesSinceMidnight(event.start);
            const end = getMinutesSinceMidnight(event.end);

            let placed = false;

            for (let i = 0; i < columns.length; i++) {
                const col = columns[i];
                if (start >= getMinutesSinceMidnight(col[col.length - 1].end)) {
                    col.push(event);
                    result.push({
                        ...event,
                        columnIndex: i,
                        totalColumns: columns.length,
                    });
                    placed = true;
                    break;
                }
            }

            if (!placed) {
                columns.push([event]);
                result.push({
                    ...event,
                    columnIndex: columns.length - 1,
                    totalColumns: columns.length,
                });
            }
        }

        return result.map((e) => ({
            ...e,
            totalColumns: columns.length,
        }));
    };

    return (
        <View style={[tw` `, { flexDirection: 'row' }]}>
            {/* Time Column */}
            {/* <View
                style={[
                    styles.timeColumn,
                    {
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        zIndex: 10,
                    },
                ]}
            >
                {TIME_SLOTS.map((slot) => (
                    <View key={slot} style={styles.timeCell}>
                        <Text style={styles.timeText}>{slot}</Text>
                    </View>
                ))}
            </View> */}

            {/* Trainers' Columns */}
            <ScrollView
                ref={headerScrollRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.headerContainer}
                contentContainerStyle={{ flexDirection: 'row' }}
            >
                {trainers.map((trainer: any) => (
                    <View
                        key={trainer.userId}
                        style={[
                            styles.trainerHeaderCell,
                            { width: trainerColumnWidth },
                        ]}
                    >
                        <TouchableOpacity
                            onPress={() => onTrainerPress?.(trainer)}
                        >
                            <View style={styles.trainerAvatar}>
                                <Text style={styles.trainerAvatarText}>
                                    {(trainer.firstName
                                        ?.charAt(0)
                                        .toUpperCase() || '') +
                                        (trainer.lastName
                                            ?.charAt(0)
                                            .toUpperCase() || '')}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                ))}
            </ScrollView>

            {/* Scrollable Timeline */}
            <ScrollView
                ref={verticalScrollRef}
                style={{ flex: 1 }}
                contentContainerStyle={{ flexGrow: 1 }}
            >
                {dayjs(selectedDate).isSame(dayjs(), 'day') && (
                    <View
                        style={{
                            position: 'absolute',
                            top: (currentMinutes / 60) * ROW_HEIGHT - 20,
                            left: 0,
                            right: 0,
                            alignItems: 'center',
                            zIndex: 100,
                            pointerEvents: 'none',
                        }}
                    >
                        <Text style={{ color: 'black', fontWeight: 'bold' }}>
                            {dayjs().format('HH:mm')}
                        </Text>
                    </View>
                )}
                <ScrollView
                    ref={bodyScrollRef}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    onScroll={(e) =>
                        headerScrollRef.current?.scrollTo({
                            x: e.nativeEvent.contentOffset.x,
                            animated: false,
                        })
                    }
                    scrollEventThrottle={16}
                    contentContainerStyle={{ flexDirection: 'row' }}
                >
                    {/* SINGLE RED LINE SPANNING ALL COLUMNS */}
                    {dayjs(selectedDate).isSame(dayjs(), 'day') && (
                        <View
                            style={{
                                position: 'absolute',
                                top: (currentMinutes / 60) * ROW_HEIGHT,
                                left: 0,
                                height: 1,
                                backgroundColor: 'red',
                                width:
                                    (trainers.length || 1) * trainerColumnWidth,

                                zIndex: 50,
                            }}
                        />
                    )}

                    {trainers.map((trainer: any) => {
                        const trainerEvents = assignEventColumns(
                            events.filter(
                                (e: any) =>
                                    e.trainerId === trainer.userId &&
                                    dayjs(e.start).format('YYYY-MM-DD') ===
                                        selectedDateStr
                            )
                        );

                        return (
                            <View
                                key={trainer.userId}
                                style={{
                                    width: trainerColumnWidth,
                                    borderRightWidth: 1,
                                    borderColor: '#E0E0E0',
                                }}
                            >
                                <View
                                    style={{
                                        height: 24 * ROW_HEIGHT,
                                        position: 'relative',
                                    }}
                                >
                                    {/* Events */}
                                    {trainerEvents.map((event: any) => {
                                        const startMin =
                                            getMinutesSinceMidnight(
                                                event.start
                                            );
                                        const endMin = getMinutesSinceMidnight(
                                            event.end
                                        );
                                        const duration = endMin - startMin;

                                        const top =
                                            (startMin / 60) * ROW_HEIGHT;
                                        const height = Math.max(
                                            (duration / 60) * ROW_HEIGHT,
                                            15
                                        );
                                        const eventColor = getCategoryColor(
                                            event.classType
                                        );
                                        const eventWidth =
                                            event.totalColumns === 1
                                                ? trainerColumnWidth - 10
                                                : (trainerColumnWidth - 10) /
                                                  event.totalColumns;
                                        const left =
                                            event.totalColumns === 1
                                                ? 5
                                                : event.columnIndex *
                                                      eventWidth +
                                                  5;

                                        return (
                                            <TouchableOpacity
                                                key={event.id}
                                                onPress={() =>
                                                    onEventPress?.(event)
                                                }
                                                style={[
                                                    styles.eventBlock,
                                                    {
                                                        top,
                                                        height,
                                                        left,
                                                        width: eventWidth - 5,
                                                        backgroundColor:
                                                            eventColor,
                                                        borderColor: eventColor,
                                                    },
                                                ]}
                                            >
                                                <Text
                                                    style={styles.eventText}
                                                    numberOfLines={1}
                                                >
                                                    {event.title}
                                                </Text>
                                                {event.clientName && (
                                                    <Text
                                                        numberOfLines={1}
                                                        style={styles.timeRange}
                                                    >
                                                        {event.clientName}
                                                    </Text>
                                                )}
                                                <Text
                                                    numberOfLines={1}
                                                    style={styles.timeRange}
                                                >
                                                    {event.from} - {event.to}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}

                                    {/* Grid lines */}
                                    {TIME_SLOTS.map((slot, index) => (
                                        <View
                                            key={`grid-${slot}`}
                                            style={[
                                                styles.gridLine,
                                                { top: index * ROW_HEIGHT },
                                            ]}
                                        />
                                    ))}

                                    {/* Empty slots */}
                                    {[...Array(24)].map((_, index) => {
                                        const hour = `${index
                                            .toString()
                                            .padStart(2, '0')}:00`;
                                        return (
                                            <TouchableOpacity
                                                key={hour}
                                                style={[
                                                    styles.emptyCell,
                                                    { top: index * ROW_HEIGHT },
                                                ]}
                                                onPress={() =>
                                                    onEventPress?.({
                                                        isEmpty: true,
                                                        slot: hour,
                                                        trainer,
                                                        selectedDate,
                                                    })
                                                }
                                            />
                                        );
                                    })}
                                </View>
                            </View>
                        );
                    })}
                </ScrollView>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    headerContainer: {
        backgroundColor: '#f1f1f1',
        zIndex: 10,
        elevation: 3,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
    },
    trainerHeaderCell: {
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
        borderRightWidth: 1,
        borderColor: '#EEE',
    },
    trainerAvatar: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#0fd99c',
        justifyContent: 'center',
        alignItems: 'center',
    },
    trainerAvatarText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    eventBlock: {
        position: 'absolute',
        borderRadius: 6,
        paddingVertical: 2,
        paddingHorizontal: 6,
        borderWidth: 0.5,
        zIndex: 5,
    },
    eventText: {
        fontSize: 13,
        fontWeight: '600',
        color: '#000',
    },
    timeRange: {
        fontSize: 11,
        color: '#444',
    },
    emptyCell: {
        position: 'absolute',
        left: 0,
        right: 0,
        height: 90,
        backgroundColor: 'transparent',
    },
    gridLine: {
        position: 'absolute',
        left: 0,
        right: 0,
        height: 1,
        backgroundColor: '#DDD',
    },
});

export default CustomTrainerCalendarList;
