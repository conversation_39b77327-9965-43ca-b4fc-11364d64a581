import { useNavigation } from '@react-navigation/native';
import React, { memo, useCallback, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import { CALENDAR_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';
import TextInput from '~/components/atoms/text-input';

type Day = {
    id: number;
    label: string;
    key: string;
};

const AmenityWorkingHour = ({ details }: any) => {
    const [selectedDay, setSelectedDay] = useState<string>('mon');
    const navigation = useNavigation();

    const days: Day[] = [
        { id: 1, label: 'Mon', key: 'mon' },
        { id: 2, label: 'Tue', key: 'tue' },
        { id: 3, label: 'Wed', key: 'wed' },
        { id: 4, label: 'Thu', key: 'thu' },
        { id: 5, label: 'Fri', key: 'fri' },
        { id: 6, label: 'Sat', key: 'sat' },
        { id: 7, label: 'Sun', key: 'sun' },
    ];

    const availableData =
        details?.availability?.find((item: any) => item.type === 'available')
            ?.workingHours[selectedDay] || [];
    return (
        <>
            <View
                style={tw`flex flex-col  px-5 mx-5 border-b pb-6  border-MainTextColor border-opacity-20 `}
            >
                <Text style={tw`font-semibold text-lg text-MainTextColor`}>
                    Working hours
                </Text>

                <View style={tw`flex flex-row pt-3 gap-0.5`}>
                    {days.map((item) => (
                        <TouchableOpacity
                            key={item.id}
                            style={tw`mr-2`}
                            onPress={() => setSelectedDay(item.key)}
                        >
                            <View
                                style={tw`border border-[#E6EBF1] rounded-full h-[40px] flex justify-center flex-row items-center w-[40px] ${
                                    selectedDay === item.key ? 'bg-primary' : ''
                                }`}
                            >
                                <Text
                                    style={tw`text-[#1A3353] text-xs ${
                                        selectedDay === item.key
                                            ? 'text-white'
                                            : ''
                                    }`}
                                >
                                    {item.label}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>

                <View style={tw``}>
                    {availableData?.length > 0 ? (
                        availableData?.map((slot: any, index: number) => (
                            <View
                                style={tw`flex flex-row gap-2 items-center pt-5 pb-3 justify-between`}
                            >
                                <TouchableOpacity
                                    style={tw`w-[50%] flex gap-1 flex-col `}
                                >
                                    <Text
                                        style={tw`text-[#1A3353] text-sm font-medium`}
                                    >
                                        Opening Time
                                    </Text>
                                    <TextInput
                                        disabled
                                        value={slot.from}
                                        // placeholder={'Opening Time'}
                                        style={tw` px-2 border  border-[#E6EBF1] rounded-md text-14 font-400`}
                                    />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={tw`w-[50%] gap-1 flex flex-col `}
                                >
                                    <Text
                                        style={tw`text-[#1A3353] text-sm font-medium`}
                                    >
                                        Closing Time
                                    </Text>
                                    <TextInput
                                        disabled
                                        value={slot.to}
                                        // placeholder={'Closing Time'}
                                        style={tw` px-2 border  border-[#E6EBF1] rounded-md text-14 font-400`}
                                    />
                                </TouchableOpacity>
                            </View>
                        ))
                    ) : (
                        <Text style={tw`text-[#E74C3C] text-center text-sm`}>
                            No working hours available for {selectedDay}.
                        </Text>
                    )}
                </View>
            </View>
            <View
                style={tw`flex flex-col px-9 border-b pb-6  border-MainTextColor border-opacity-20 `}
            >
                <Text style={tw`font-semibold text-lg text-MainTextColor`}>
                    Amenities
                </Text>
                <View>
                    <FlatList
                        data={details?.amenityDetails || []}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item }) => (
                            <Text
                                style={tw`text-base leading-5 text-[#455560]`}
                            >
                                • {item.name}
                            </Text>
                        )}
                    />
                </View>
            </View>
            {/* <View style={tw`flex justify-center mt-9 mb-3 `}>
                <Button
                    onPress={() => navigation.navigate(CALENDAR_SCREEN)}
                    style={[tw`w-[50%] mx-auto rounded-lg  font-bold`]}
                >
                    Welcome
                </Button>
            </View> */}
        </>
    );
};

export default memo(AmenityWorkingHour);
