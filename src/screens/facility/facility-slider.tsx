import React, { memo } from 'react';
import { Dimensions, Image, StyleSheet, View } from 'react-native';
import SwiperFlatList from 'react-native-swiper-flatlist';

import tw from '~/styles/tailwind';

const fallbackImage = require('../../assets/images/icons/gym_banner.png');

const width = Dimensions.get('window').width;

const FacilitySlider = ({ gallery, facilityDetails }: any) => {
    console.log('Facility details-----------', facilityDetails);
    const imageData =
        gallery && gallery.length > 0
            ? gallery?.map((url: any, index: number) => ({
                  id: index.toString(),
                  image: { uri: url },
              }))
            : [
                  {
                      id: 'fallback',
                      image: facilityDetails?.profilePicture && {
                          uri: facilityDetails?.profilePicture,
                      },
                  },
              ];

    return (
        <View style={tw``}>
            <SwiperFlatList
                autoplayDelay={2}
                autoplayLoop
                index={0}
                showPagination
                paginationActiveColor={'#8143D1'}
                paginationStyleItem={styles.paginationItem}
                data={imageData}
                keyExtractor={(item) => item.id.toString()}
                renderItem={({ item }) => (
                    <View style={styles.child}>
                        <Image
                            style={tw`w-[90%] mx-auto  h-[300px]  rounded-[50px]`}
                            source={item.image}
                            resizeMode="cover"
                        />
                    </View>
                )}
            />
        </View>
    );
};

export default memo(FacilitySlider);

const styles = StyleSheet.create({
    // container: { backgroundColor: 'transparent', display },
    child: { width },
    paginationItem: {
        width: 10,
        height: 10,
        marginHorizontal: 5,
        marginTop: 10,
    },
});
