import React, { memo } from 'react';
import { Dimensions, Image, Text, View } from 'react-native';
import RenderHTML from 'react-native-render-html';

import tw from '~/styles/tailwind';

import Contact_SVG from '~/assets/svg/contact_Icon.svg';
import Location_SVG from '~/assets/svg/location_thin.svg';

const Detail = ({ details }: any) => {
    const { width } = Dimensions.get('window');

    const email = details?.email || 'N/A';
    const phone = details?.mobile || 'N/A';
    const contactName = details?.contactName || 'N/A';
    const address = details?.address
        ? `${details?.address?.addressLine1}, ${details?.cityName?.[0]}, ${details.stateName?.[0]}, ${details?.address?.postalCode}`
        : 'Address not available';

    // Dynamically populate facility data
    const facilityData = [
        {
            id: 1,
            title: 'Address',
            data: address,
            icon: <Location_SVG width={30} height={30} />,
        },
        {
            id: 2,
            title: 'Contact Details',
            data: `Email: ${email}\nPhone: ${phone}`,
            icon: <Contact_SVG width={30} height={30} />,
        },
        // {
        //     id: 3,
        //     title: 'Contact Person',
        //     data: contactName,
        //     icon: <Contact_SVG width={30} height={30} />,
        // },
    ];

    return (
        <View style={tw`flex flex-col `}>
            {facilityData.map((item, index) => {
                const isLastItem = index === facilityData.length - 1;

                return (
                    <View
                        key={item.id}
                        style={tw`flex flex-row  pt-4 pb-3 mx-5 border-MainTextColor border-opacity-20 ${
                            isLastItem ? 'border-b border-t' : 'border-t'
                        }`}
                    >
                        <View style={tw`w-[20%] flex flex-row pl-5 pt-2 `}>
                            {item.icon}
                        </View>
                        <View style={tw`w-[70%]`}>
                            <View style={tw`flex flex-col `}>
                                <Text
                                    style={tw`font-semibold text-lg text-MainTextColor`}
                                >
                                    {item.title}
                                </Text>
                                <Text style={tw`text-base text-MainTextColor`}>
                                    {item.data
                                        .split('\n')
                                        .map((line: any, index: number) => (
                                            <Text key={index}>
                                                {line}
                                                {'\n'}
                                            </Text>
                                        ))}
                                </Text>
                            </View>
                        </View>
                    </View>
                );
            })}

            <View
                style={tw`flex flex-col pl-5 mx-5 pr-6 border-b  border-MainTextColor border-opacity-20 py-6`}
            >
                <Text style={tw`font-semibold text-lg text-MainTextColor`}>
                    About Gym
                </Text>
                {/* <Text style={tw`text-base text-Ma`}>
                    Lorem ipsum dolor sit amet consectetur. Volutpat risus sit
                    quam sed condimentum mattis. Vel elementum orci tristique eu
                    a sed. Et orci commodo aliquam diam adipiscing eget. Vitae
                    fringilla sit ultricies turpis dignissim volutpat massa
                    gravida vehicula.
                </Text> */}
                <RenderHTML
                    contentWidth={width}
                    source={{
                        html:
                            details?.description ||
                            '<p class="description">No description available.</p>',
                    }}
                    tagsStyles={{
                        h1: tw`text-base mt-1 font-normal text-[#455560]`,
                        h2: tw`text-base mt-1 font-normal text-[#455560]`,
                        h3: tw`text-base mt-1 font-normal text-[#455560]`,
                        h4: tw`text-base mt-1 font-normal text-[#455560]`,
                        p: tw`text-base mt-1 font-normal text-[#455560]`,
                        // h1: tw`text-base mt-1 font-normal text-[#455560]`,
                    }}
                    classesStyles={{
                        description: tw`italic text-gray-500`,
                    }}
                />
            </View>
        </View>
    );
};

export default memo(Detail);
