import React, { memo } from 'react';
import {
    Modal,
    StyleSheet,
    Text,
    TouchableWithoutFeedback,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

const AmenityModal = ({ onClose, visible, details }: any) => {
    return (
        <Modal
            transparent={true}
            animationType="slide"
            visible={visible}
            onRequestClose={onClose}
        >
            <TouchableWithoutFeedback onPress={onClose}>
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <Text style={tw`text-black text-lg font-medium pb-5`}>
                            Amenities
                        </Text>
                        <View style={tw`flex flex-col gap-1.5`}>
                            {details?.amenityDetails?.map(
                                (item: any, index: number) => {
                                    return (
                                        <View
                                            key={item.id}
                                            style={tw`flex flex-col gap-1 items-center w-[33.3%]`}
                                        >
                                            {item?.icon}
                                            <Text
                                                style={tw`text-center text-xs text-[#455560]`}
                                            >
                                                {item.name}
                                            </Text>
                                        </View>
                                    );
                                }
                            )}
                        </View>
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
};

export default memo(AmenityModal);

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
        width: '95%',
        padding: 20,
        backgroundColor: 'white',
        borderRadius: 10,
        alignItems: 'center',
    },
});
