import React, { memo, useState } from 'react';
import {
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import TextInput from '~/components/atoms/text-input';

type WorkingHourModalProps = {
    onClose: () => void;
    visible: boolean;
    details?: any;
};

type Day = {
    id: number;
    label: string;
    key: string;
};

const WorkingHourModal: React.FC<WorkingHourModalProps> = ({
    onClose,
    visible,
    details,
}) => {
    const [selectedDay, setSelectedDay] = useState<string>('mon'); // State to track selected day

    const days: Day[] = [
        { id: 1, label: 'Mon', key: 'mon' },
        { id: 2, label: 'Tues', key: 'tue' },
        { id: 3, label: 'Wed', key: 'wed' },
        { id: 4, label: 'Thu', key: 'thu' },
        { id: 5, label: 'Fri', key: 'fri' },
        { id: 6, label: 'Sat', key: 'sat' },
        { id: 7, label: 'Sun', key: 'sun' },
    ];

    const availableData =
        details?.availability?.find((item: any) => item.type === 'available')
            ?.workingHours[selectedDay] || [];

    return (
        <Modal
            transparent={true}
            animationType="slide"
            visible={visible}
            onRequestClose={onClose}
        >
            <TouchableWithoutFeedback onPress={onClose}>
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <Text
                            style={tw`text-[#1A3353] text-center text-lg font-medium pb-5`}
                        >
                            Working Hours
                        </Text>
                        <View style={tw`flex flex-row gap-0.5`}>
                            {days.map((item) => (
                                <TouchableOpacity
                                    key={item.id}
                                    style={tw`mr-2`}
                                    onPress={() => setSelectedDay(item.key)}
                                >
                                    <View
                                        style={tw`border border-[#E6EBF1] rounded-full h-[40px] flex justify-center flex-row items-center w-[40px] ${
                                            selectedDay === item.key
                                                ? 'bg-primary'
                                                : ''
                                        }`}
                                    >
                                        <Text
                                            style={tw`text-[#1A3353] text-xs ${
                                                selectedDay === item.key
                                                    ? 'text-white'
                                                    : ''
                                            }`}
                                        >
                                            {item.label}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            ))}
                        </View>

                        <View style={tw`pt-5`}>
                            {availableData?.length > 0 ? (
                                availableData?.map(
                                    (slot: any, index: number) => (
                                        <View
                                            style={tw`flex flex-row gap-2 items-center pt-5 pb-3 justify-between`}
                                        >
                                            <TouchableOpacity
                                                style={tw`w-[50%] flex flex-col `}
                                            >
                                                <Text
                                                    style={tw`text-[#1A3353] text-sm font-medium`}
                                                >
                                                    Opening Time
                                                </Text>
                                                <TextInput
                                                    disabled
                                                    value={slot.from}
                                                    // placeholder={'Opening Time'}
                                                    style={tw` px-2 border  border-[#E6EBF1] rounded-md text-14 font-400`}
                                                />
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={tw`w-[50%] flex flex-col `}
                                            >
                                                <Text
                                                    style={tw`text-[#1A3353] text-sm font-medium`}
                                                >
                                                    Closing Time
                                                </Text>
                                                <TextInput
                                                    disabled
                                                    value={slot.to}
                                                    // placeholder={'Closing Time'}
                                                    style={tw` px-2 border  border-[#E6EBF1] rounded-md text-14 font-400`}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                    )
                                )
                            ) : (
                                <Text
                                    style={tw`text-[#E74C3C] text-center text-sm`}
                                >
                                    No working hours available for {selectedDay}
                                    .
                                </Text>
                            )}
                        </View>
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </Modal>
    );
};

export default memo(WorkingHourModal);

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
        width: '95%',
        padding: 20,
        backgroundColor: 'white',
        borderRadius: 10,
        // alignItems: 'center',
    },
});
