import React, { useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';
import { BarChart } from 'react-native-gifted-charts';

import tw from '~/styles/tailwind';

const SalesGraph = () => {
    const [selectedFilter, setSelectedFilter] = useState<
        'daily' | 'monthly' | 'yearly'
    >('daily');
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [salesData, setSalesData] = useState<
        { label: string; value: number }[]
    >([]);
    const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth()); // 0-11

    useEffect(() => {
        setLoading(true);

        const timeout = setTimeout(() => {
            let newData: { label: string; value: number }[] = [];

            if (selectedFilter === 'daily') {
                const newData = [
                    ...Array.from({ length: 24 }, (_, i) => {
                        const hour = i % 12 === 0 ? 12 : i % 12;
                        const suffix = i < 12 ? 'AM' : 'PM';
                        const label = `${hour}${suffix}`;

                        // Use date string seed to guarantee uniqueness per day
                        const daySeed = parseInt(
                            selectedDate
                                .toISOString()
                                .split('T')[0]
                                .replace(/-/g, '')
                        );

                        const value =
                            2000 + ((selectedDate.getDate() + i * 13) % 3000);

                        return {
                            label,
                            value: Math.round(value),
                        };
                    }),
                ];

                setSalesData(newData);
                setLoading(false);
                return;
            }

            if (selectedFilter === 'monthly') {
                const seed = selectedYear * 100 + selectedMonth;

                newData = [
                    { label: 'W1', value: ((seed * 19) % 300000) + 1500 },
                    { label: 'W2', value: ((seed * 23) % 300000) + 1600 },
                    { label: 'W3', value: ((seed * 29) % 300000) + 1700 },
                    { label: 'W4', value: ((seed * 31) % 300000) + 1800 },
                ];
            }

            if (selectedFilter === 'yearly') {
                const base = selectedYear;

                newData = Array.from({ length: 12 }, (_, i) => {
                    const monthName = new Date(0, i).toLocaleString('default', {
                        month: 'short',
                    });
                    return {
                        label: monthName,
                        value: 2000000 + ((base * (i + 1) * 13) % 1000000), 
                    };
                });
            }

            // ✅ This is now safe to call once
            setSalesData(newData);
            setLoading(false);
        }, 1000); // simulate API delay

        return () => clearTimeout(timeout);
    }, [selectedFilter, selectedDate, selectedYear, selectedMonth]);

    const yearDropdownData = useMemo(() => {
        const currentYear = new Date().getFullYear();
        return Array.from({ length: 10 }, (_, i) => {
            const year = currentYear - i;
            return { label: `${year}`, value: year };
        });
    }, []);

    // const salesData = useMemo(() => {
    //     if (selectedFilter === 'daily') {
    //         return Array.from({ length: 24 }, (_, i) => {
    //             const hour = i % 12 === 0 ? 12 : i % 12;
    //             const suffix = i < 12 ? 'AM' : 'PM';

    //             // 👇 Using getTime to force uniqueness on each date
    //             const seed = selectedDate.getTime() + i;

    //             const value = ((seed * 37) % 250) + 50;

    //             return {
    //                 label: `${hour}${suffix}`,
    //                 value: Math.round(value),
    //             };
    //         });
    //     }

    //     // Other filters...
    //     if (selectedFilter === 'monthly') {
    //         return [
    //             { value: 1500, label: 'W1' },
    //             { value: 2000, label: 'W2' },
    //             { value: 1700, label: 'W3' },
    //             { value: 2200, label: 'W4' },
    //         ];
    //     }

    //     if (selectedFilter === 'yearly') {
    //         return [
    //             {
    //                 label: 'Jan',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Feb',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Mar',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Apr',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'May',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Jun',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Jul',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Aug',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Sep',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Oct',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Nov',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //             {
    //                 label: 'Dec',
    //                 value: Math.floor(Math.random() * 8000 + 10000),
    //             },
    //         ];
    //     }

    //     return [];
    // }, [selectedFilter, selectedDate.getTime()]); // ✅ This is the real fix

    const average = useMemo(() => {
        const total = salesData.reduce((sum, d) => sum + d.value, 0);
        return Math.round(total / salesData.length || 0);
    }, [salesData]);

    useEffect(() => {
        console.log('selectedDate changed:', selectedDate);
    }, [selectedDate]);

    return (
        <View
            style={tw`px-5 pb-6 border-b border-MainTextColor border-opacity-20`}
        >
            {/* <Text style={tw`font-semibold text-lg text-MainTextColor pb-3`}>
                Sales
            </Text> */}

            {/* Filter Tabs */}
            <View style={tw`flex-row justify-between mb-5`}>
                {['daily', 'monthly', 'yearly'].map((filter) => (
                    <TouchableOpacity
                        key={filter}
                        onPress={() => setSelectedFilter(filter as any)}
                        style={[
                            tw`px-4 py-2 flex items-center w-[30%] rounded-full`,
                            {
                                backgroundColor:
                                    selectedFilter === filter
                                        ? '#0fd99c'
                                        : '#E5E7EB',
                            },
                        ]}
                    >
                        <Text
                            style={tw`${
                                selectedFilter === filter
                                    ? 'text-white'
                                    : 'text-[#455560]'
                            } font-medium`}
                        >
                            {filter.charAt(0).toUpperCase() + filter.slice(1)}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Year Dropdown for Yearly Filter */}
            {(selectedFilter === 'monthly' || selectedFilter === 'yearly') && (
                <>
                    <View style={tw`mb-2`}>
                        <Text
                            style={tw`mb-2 text-base text-MainTextColor font-medium`}
                        >
                            Select Year{` `}
                            {selectedFilter === 'monthly' ? '& Month' : ''}
                        </Text>
                        <Dropdown
                            style={tw`py-2 px-4 border border-gray-300 rounded bg-white`}
                            placeholderStyle={tw`text-gray-500`}
                            selectedTextStyle={tw`text-black font-medium`}
                            data={yearDropdownData}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Year"
                            value={selectedYear}
                            onChange={(item) => setSelectedYear(item.value)}
                        />
                    </View>

                    {selectedFilter === 'monthly' ? (
                        <View style={tw`flex-1 mb-4`}>
                            <Dropdown
                                style={tw`py-2 px-4 border border-gray-300 rounded bg-white`}
                                placeholderStyle={tw`text-gray-500`}
                                selectedTextStyle={tw`text-black font-medium`}
                                data={Array.from({ length: 12 }, (_, i) => ({
                                    label: new Date(0, i).toLocaleString(
                                        'default',
                                        { month: 'long' }
                                    ),
                                    value: i,
                                }))}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Month"
                                value={selectedMonth}
                                onChange={(item) =>
                                    setSelectedMonth(item.value)
                                }
                            />
                        </View>
                    ) : (
                        <></>
                    )}
                </>
            )}
            {selectedFilter === 'daily' && (
                <View style={tw`mb-4`}>
                    <Text
                        style={tw`mb-2 text-base text-MainTextColor font-medium`}
                    >
                        Select Date
                    </Text>

                    <TouchableOpacity
                        onPress={() => setIsDatePickerOpen(true)}
                        style={tw`py-2 px-4 justify-center border border-gray-300 rounded bg-white`}
                    >
                        <Text style={tw`text-black`}>
                            {selectedDate.toDateString()}
                        </Text>
                    </TouchableOpacity>

                    <DatePicker
                        modal
                        open={isDatePickerOpen}
                        date={selectedDate}
                        mode="date"
                        onConfirm={(date) => {
                            setIsDatePickerOpen(false);

                            // Only update if date has changed
                            if (date.getTime() !== selectedDate.getTime()) {
                                setSelectedDate(new Date(date)); // force new object
                            }
                        }}
                        onCancel={() => setIsDatePickerOpen(false)}
                        maximumDate={new Date()}
                    />
                </View>
            )}

            {/* Bar Chart */}
            {/* {loading ? (
                <View style={tw`h-56 justify-center items-center`}>
                    <ActivityIndicator color={'#8143D1'} size={'large'} />
                </View>
            ) : ( */}
            <>
                <BarChart
                    key={`${selectedFilter}-${selectedDate.toDateString()}`}
                    barWidth={selectedFilter === 'yearly' ? 22 : 25}
                    noOfSections={selectedFilter === 'daily' ? 6 : 5}
                    barBorderRadius={4}
                    frontColor="#0fd99c"
                    data={salesData}
                    yAxisThickness={0}
                    xAxisThickness={0}
                    xAxisLabelTextStyle={{ color: '#455560' }}
                    yAxisTextStyle={{ color: '#455560' }}
                />
                <View style={tw`mt-5`}>
                    <Text style={tw`text-center text-gray-700`}>
                        Average Sale: ₹{average.toLocaleString('en-IN')}
                    </Text>
                </View>
            </>
            {/* )} */}

            {/* Average Display */}
        </View>
    );
};

export default SalesGraph;
