import {
    useFocusEffect,
    useNavigation,
    useRoute,
} from '@react-navigation/native';
import React, { memo, useCallback, useEffect, useState } from 'react';
import {
    Image,
    LayoutAnimation,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { StaffGetStaffDetails } from '~/redux/actions/staff-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';
import {
    capitalizeFirstLetter,
    formatDate,
} from '~/components/common/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

import StaffBasicInfo from './staff-basic-info';
import StaffSkillExperience from './staff-skill-experience';

function renderProfileImage(img: any, gender: string) {
    if (img) {
        return { uri: img };
    }
    return gender?.toLowerCase() === 'male'
        ? Asset.MaleGenderProfile
        : Asset.FemaleGenderProfile;
}

const StaffSkillExperienceTab = () => {
    const route = useRoute();
    const { staffDetail }: any = route.params || {};
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        personalInfo: state.staff_store.staffDetails.personalInfo,
        additionalInfo: state.staff_store.staffDetails.additionalInfo,
        facilityInfo: state.staff_store.staffDetails.facilityInfo,
    }));

    console.log('Store------------', staffDetail);

    // useEffect(() => {
    //     if (staffDetail) {
    //         dispatch(StaffGetStaffDetails({ userId: staffDetail }));
    //     }
    // }, [dispatch, staffDetail]);
    useFocusEffect(
        useCallback(() => {
            if (staffDetail) {
                dispatch(StaffGetStaffDetails({ userId: staffDetail }));
            }
        }, [])
    );

    return (
        <View style={tw`bg-[#FAFAFA]  flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'skill and experience'} />

                <View style={tw`px-5 py-7`}>
                    <View
                        style={tw`flex flex-row items-center py-5 rounded-2xl pl-7 gap-8 bg-primary`}
                    >
                        <View
                            style={tw`w-18 h-18 border border-gray-300 rounded-full overflow-hidden`}
                        >
                            <Image
                                resizeMode="cover"
                                style={tw`w-full h-full rounded-full`}
                                source={renderProfileImage(
                                    store.personalInfo?.profilePicture,
                                    store.personalInfo?.gender
                                )}
                            />
                        </View>
                        <View style={tw`flex flex-col gap-1 text-white`}>
                            <Text style={tw`text-16 font-bold  text-white`}>
                                {capitalizeFirstLetter(
                                    store.personalInfo?.firstName
                                )}{' '}
                                {capitalizeFirstLetter(
                                    store.personalInfo?.lastName
                                )}
                            </Text>
                            <Text
                                numberOfLines={1}
                                ellipsizeMode="tail"
                                style={tw`text-16 font-bold text-white `}
                            >
                                Staff ID : &nbsp;
                                <Text
                                    style={tw`font-normal text-14 text-white `}
                                >
                                    {store.additionalInfo?.staffId}
                                </Text>
                            </Text>
                        </View>
                    </View>
                </View>

                <StaffSkillExperience
                    {...{
                        ...store.personalInfo,
                        ...store.additionalInfo,
                        ...store.facilityInfo,
                    }}
                />
            </ScrollView>
        </View>
    );
};

export default memo(StaffSkillExperienceTab);
