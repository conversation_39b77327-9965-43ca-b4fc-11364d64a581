import { memo, useEffect, useState } from 'react';
import {
    FlatList,
    Image,
    Modal,
    StyleSheet,
    Switch,
    Text,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import { GetStaffListByRole } from '~/redux/actions/staff-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';
import FilterSVG from '~/assets/svg/filter-svg.svg';

import { STAFF_DETAIL } from '~/constants/navigation-constant';

import TextInput from '~/components/atoms/text-input';
import BackButtonHeading from '~/components/common/back-button-heading';
import { getFormattedRole } from '~/components/common/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { navigationRef } from '~/hooks/useLocation';

const StaffListing = () => {
    const [staffDetail, setStaffDetails] = useState({});
    const [isDropdownVisible, setDropdownVisible] = useState(false);
    const [search, setSearch] = useState('');
    const debouncedRequest = useDebounce((callback: any) => callback(), 300);

    const dispatch = useAppDispatch();

    const handleDropdownToggle = (item: any) => {
        console.log('Item------', item);
        setStaffDetails(item?.userId);
        setDropdownVisible(!isDropdownVisible);
    };

    const store = useAppSelector((state) => ({
        staffLists: state.staff_store.staffLists,
    }));

    useEffect(() => {
        if (search) {
            debouncedRequest(() => {
                dispatch(GetStaffListByRole({ search: search }));
            });
        } else {
            dispatch(GetStaffListByRole({}));
        }
    }, [search]);

    const renderItem = ({ item }: any) => (
        <View
            key={item._id}
            style={tw`flex flex-row items-center justify-between py-2 px-4 border-b border-black/10`}
        >
            <TouchableOpacity
                onPress={() => {
                    (navigationRef as any).navigate(STAFF_DETAIL, {
                        staffDetail: item.userId,
                    });
                }}
            >
                <View style={tw`flex flex-row items-center gap-2`}>
                    <View style={tw`w-[20%]`}>
                        {renderProfileImage(item.profilePicture) ? (
                            <Image
                                style={tw`h-12 w-12 rounded-full`}
                                source={renderProfileImage(item.profilePicture)}
                                resizeMode="cover"
                            />
                        ) : (
                            <View
                                style={[
                                    tw`h-12 w-12 rounded-full justify-center items-center`,
                                    {
                                        backgroundColor: getRandomColor(
                                            item.firstName || 'A'
                                        ),
                                    },
                                ]}
                            >
                                <Text style={tw`text-white text-lg font-bold`}>
                                    {item.firstName?.charAt(0).toUpperCase() ||
                                        'U'}
                                </Text>
                            </View>
                        )}
                    </View>
                    <View style={tw`flex flex-col w-[200px]  mr-4`}>
                        <Text
                            style={tw`text-[15px] font-semibold text-[#455560]`}
                        >
                            {item.firstName} {item.lastName}
                        </Text>
                        <Text style={tw`text-14 text-[#455560]`}>
                            {getFormattedRole(item.role)}
                        </Text>
                    </View>
                    <View
                        style={[
                            tw`w-3 h-3 rounded-full`,
                            item.isActive ? tw`bg-green-700` : tw`bg-red-700`,
                        ]}
                    ></View>
                </View>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => handleDropdownToggle(item)}>
                <Text style={tw`text-black text-3xl`}>⁝</Text>
            </TouchableOpacity>
        </View>
    );

    function renderProfileImage(img: any) {
        return img ? { uri: img } : null;
    }

    function getRandomColor(seed: string) {
        const colors = [
            '#F7DFF6',
            '#A1B4FF',
            '#B4C1C7',
            '#FFD09E',
            '#DDA0DD',
            '#A4E9FF',
            '#F5DEB3',
        ];
        const index = seed.charCodeAt(0) % colors.length;
        return colors[index];
    }

    return (
        <View style={tw``}>
            {/* ================ header =========================== */}
            {/* <View style={tw`bg-#D0FF01] w-[100%] h-24 px-4 shadow-md`}>
                <View
                    style={tw`flex justify-between items-center flex-row h-full`}
                >
                    <BackButtonHeading Heading={`My Staff`} />
                    <TouchableOpacity>
                        <View style={tw`bg-black  p-2.5  rounded-full`}>
                            <SearchSVG style={tw`text-lg`} />
                        </View>
                    </TouchableOpacity>
                </View>
            </View> */}
            <BackButtonHeading Heading={`My Staff`} />
            {/* -====================search========================= */}
            <View>
                <View
                    style={tw`flex gap-5 flex-row py-4 justify-center items-center `}
                >
                    <View style={tw`w-[80%]`}>
                        <TextInput
                            placeholder={`Search for staff `}
                            value={search}
                            onChangeText={(text) => setSearch(text)}
                            style={tw`pl-3   h-12 border rounded-full  border-gray-200  `}
                        />
                    </View>
                    <FilterSVG />
                </View>
            </View>
            {/* ================Staff listing=========================== */}
            <View style={tw`flex flex-col gap-3 pb-5`}>
                <FlatList
                    data={store.staffLists}
                    renderItem={renderItem}
                    keyExtractor={(item: any) => item._id}
                    style={tw`flex flex-col  gap-6 pb-5`}
                />
                <Modal
                    transparent={true}
                    visible={isDropdownVisible}
                    animationType="fade"
                    onRequestClose={() => setDropdownVisible(false)}
                >
                    <TouchableWithoutFeedback
                        onPress={() => setDropdownVisible(false)}
                    >
                        <View
                            style={tw`flex-1 justify-center items-center bg-black bg-opacity-50`}
                        >
                            <View style={tw`bg-white p-4 w-[80%] rounded-lg`}>
                                <TouchableOpacity
                                    onPress={() => {
                                        (navigationRef as any).navigate(
                                            STAFF_DETAIL,
                                            {
                                                staffDetail,
                                            }
                                        );
                                    }}
                                >
                                    <Text style={tw`text-lg p-2`}>View</Text>
                                </TouchableOpacity>
                                {/* <TouchableOpacity
                                // onPress={() => {
                                //     handleOptionSelectEdit;
                                // }}
                                >
                                    <Text style={tw`text-lg p-2`}>Edit</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                // onPress={() => {
                                //     handleOptionSelectDelete;
                                // }}
                                >
                                    <Text style={tw`text-lg p-2`}>Delete</Text>
                                </TouchableOpacity> */}
                            </View>
                        </View>
                    </TouchableWithoutFeedback>
                </Modal>
            </View>
        </View>
    );
};

export default memo(StaffListing);

const styles = StyleSheet.create({
    switch: {
        transform: [{ scaleX: 1 }, { scaleY: 1 }],
    },
});
