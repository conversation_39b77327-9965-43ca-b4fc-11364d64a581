import {
    useFocusEffect,
    useNavigation,
    useRoute,
} from '@react-navigation/native';
import React, { memo, useCallback, useEffect, useState } from 'react';
import {
    Image,
    LayoutAnimation,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { StaffGetStaffDetails } from '~/redux/actions/staff-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import {
    STAFF_PERSONAL_INFORMATION,
    STAFF_SKILL_EXPERIENCE_TAB,
} from '~/constants/navigation-constant';

import BackButtonHeading from '~/components/common/back-button-heading';
import {
    capitalizeFirstLetter,
    formatDate,
} from '~/components/common/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

import StaffBasicInfo from './staff-basic-info';
import StaffSkillExperience from './staff-skill-experience';

const scheduleData = [
    {
        tabName: 'Personal Information',
        content: StaffBasicInfo,
    },
    {
        tabName: 'Skills & Experience',
        content: StaffSkillExperience,
    },
];

function renderProfileImage(img: any, gender: string) {
    if (img) {
        return { uri: img };
    }
    return gender?.toLowerCase() === 'male'
        ? Asset.MaleGenderProfile
        : Asset.FemaleGenderProfile;
}

const StaffDetail = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { staffDetail }: any = route.params || {};
    const [activeSection, setActiveSection] = useState(null);
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        personalInfo: state.staff_store.staffDetails.personalInfo,
        additionalInfo: state.staff_store.staffDetails.additionalInfo,
        facilityInfo: state.staff_store.staffDetails.facilityInfo,
    }));

    console.log('Store------------', staffDetail);

    // useEffect(() => {
    //     if (staffDetail) {
    //         dispatch(StaffGetStaffDetails({ userId: staffDetail }));
    //     }
    // }, [dispatch, staffDetail]);

    useFocusEffect(
        useCallback(() => {
            if (staffDetail) {
                dispatch(StaffGetStaffDetails({ userId: staffDetail }));
            }
        }, [])
    );

    console.log({ activeSection });

    const [showDetails, setShowDetails] = useState(null);

    const handleShowHide = (index: any) => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setShowDetails(showDetails === index ? null : index);
    };

    const [activeTab, setActiveTab] = useState('profile');
    const handleTabPress = (tab: any) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };

    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'personal information'} />

                <View style={tw`px-5 py-7`}>
                    <View
                        style={tw`flex flex-row items-center py-5 rounded-2xl pl-7 gap-8 bg-primary`}
                    >
                        <View
                            style={tw`w-18 h-18 border border-gray-300 rounded-full overflow-hidden`}
                        >
                            <Image
                                resizeMode="cover"
                                style={tw`w-full h-full rounded-full`}
                                source={renderProfileImage(
                                    store.personalInfo?.profilePicture,
                                    store.personalInfo?.gender
                                )}
                            />
                        </View>
                        <View style={tw`flex flex-col gap-1 text-white`}>
                            <Text style={tw`text-16 font-bold  text-white`}>
                                {capitalizeFirstLetter(
                                    store.personalInfo?.firstName
                                )}{' '}
                                {capitalizeFirstLetter(
                                    store.personalInfo?.lastName
                                )}
                            </Text>
                            <Text
                                numberOfLines={1}
                                ellipsizeMode="tail"
                                style={tw`text-16 font-bold text-white `}
                            >
                                Staff ID : &nbsp;
                                <Text
                                    style={tw`font-normal text-14 text-white `}
                                >
                                    {store.additionalInfo?.staffId}
                                </Text>
                            </Text>
                        </View>

                        {/* <View
                            style={tw`bg-white shadow-md flex flex-col py-1 px-1.5 rounded-sm`}
                        >
                            <Text
                                style={tw`text-sm font-medium text-[#455560]`}
                            >
                                User Type
                            </Text>

                            <View
                                style={tw`flex flex-row items-center gap-2 w-full`}
                            >
                                <View
                                    style={[
                                        tw`rounded-full w-2 h-2`,
                                        {
                                            backgroundColor: store.personalInfo
                                                ?.isActive
                                                ? '#3FB249'
                                                : '#FF0000',
                                        },
                                    ]}
                                />
                                <Text
                                    style={tw`text-14 font-normal text-black`}
                                >
                                    {store.personalInfo?.isActive
                                        ? 'Active'
                                        : 'Inactive'}
                                </Text>
                            </View>
                        </View> */}
                    </View>

                    {/* <View style={tw` justify-between pt-5 px-5 flex flex-row`}>
                        <View>
                            <Text
                                style={tw`text-sm text-[#455560] font-medium`}
                            >
                                Date Joined
                            </Text>
                            <Text
                                style={tw`text-xs text-[#455560] font-normal`}
                            >
                                {formatDate(
                                    store.additionalInfo?.employmentDate
                                )}
                            </Text>
                        </View>
                        <View>
                            <Text
                                style={tw`text-sm text-[#455560] font-medium`}
                            >
                                Bookings/Visit
                            </Text>
                            <Text
                                style={tw`text-xs text-[#455560] font-normal`}
                            >
                                06
                            </Text>
                        </View>
                    </View> */}
                </View>

                <View style={tw`hidden`}>
                    {scheduleData.map((data, index) => (
                        <View
                            key={index}
                            style={tw`bg-[#FAFAFA] p-4 rounded-lg `}
                        >
                            <TouchableOpacity
                                style={tw``}
                                onPress={() => handleShowHide(index)}
                            >
                                <View
                                    style={tw`flex flex-row justify-between items-center border-b pb-2 border-[#D8D8D8]`}
                                >
                                    <Text
                                        style={tw`text-14 text-[#455560] font-bold px-2`}
                                    >
                                        {data.tabName}
                                    </Text>
                                    <View style={tw``}>
                                        {showDetails === index ? (
                                            <Image
                                                style={tw` w-6 h-6`}
                                                source={Asset.DownArrow}
                                                resizeMode="cover"
                                            />
                                        ) : (
                                            <Image
                                                style={tw` w-6 h-6`}
                                                source={Asset.UpArrows}
                                            />
                                        )}
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {showDetails === index && (
                                <>
                                    <View style={tw`bg-[#F5F5F5]  rounded-lg`}>
                                        <data.content
                                            {...{
                                                ...store.personalInfo,
                                                ...store.additionalInfo,
                                                ...store.facilityInfo,
                                            }}
                                        />
                                    </View>
                                </>
                            )}
                        </View>
                    ))}
                </View>
                {/* -------------------------profile tabs-------------------------- */}

                <View
                    style={tw`flex flex-row items-center border-b border-[#2C2C2E4D] border-opacity-20`}
                >
                    <TouchableOpacity
                        style={tw`w-[50%] px-8  ${
                            activeTab ? 'border-b border-primary' : ''
                        } `}
                        onPress={() => handleTabPress('profile')}
                    >
                        <Text
                            style={tw` pb-1  ${
                                activeTab
                                    ? 'text-[#000000] font-bold'
                                    : 'text-[#A9A9A9]'
                            } font-base uppercase`}
                        >
                            Profile
                        </Text>
                    </TouchableOpacity>
                </View>

                {activeTab === 'profile' && (
                    <View style={tw`flex flex-col gap-5 py-6`}>
                        <TouchableOpacity
                            onPress={() =>
                                (navigation as any).navigate(
                                    STAFF_PERSONAL_INFORMATION
                                )
                            }
                        >
                            <Text
                                style={tw`text-MainTextColor text-16 border-b px-8 font-semibold border-[#2C2C2E4D] border-opacity-5 pb-5`}
                            >
                                Personal Information
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() =>
                                (navigation as any).navigate(
                                    STAFF_SKILL_EXPERIENCE_TAB
                                )
                            }
                        >
                            <Text
                                style={tw`text-MainTextColor text-16 border-b px-8 font-semibold border-[#2C2C2E4D] border-opacity-5 pb-5`}
                            >
                                Skills and Experience
                            </Text>
                        </TouchableOpacity>
                    </View>
                )}
            </ScrollView>
        </View>
    );
};

export default memo(StaffDetail);
