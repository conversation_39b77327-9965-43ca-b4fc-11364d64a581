import { useRoute } from '@react-navigation/native';
import dayjs from 'dayjs';
import React, { memo, useState } from 'react';
import {
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { Staff_Roles } from '~/constants/enums';

import { Button, TextInput } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';
import {
    capitalizeFirstLetter,
    formatDate,
} from '~/components/common/function';

const StaffBasicInfo = ({ ...props }) => {
    const route = useRoute();
    const [gender, setGender] = useState(null);
    const [isEditingBasicInfo, setIsEditingBasicInfo] = useState(false);

    const [dobDate, setDobDate] = useState(new Date());
    const [openDOBPicker, setOpenDOBPicker] = useState(false);
    const { type, scheduleId, isDetails }: any = route.params || {};

    const isViewMode: any = type === 'view';

    console.log('StaffBasicInfo', { props });

    const [formData, setFormData] = useState({
        firstName: props.firstName || '',
        lastName: props.lastName || '',
        gender: props.gender?.toLowerCase() || '',
        dateOfBirth: props.dateOfBirth
            ? new Date(props.dateOfBirth)
            : new Date(),
        employmentDate: props.employmentDate
            ? new Date(props.employmentDate)
            : new Date(),
        email: props.email || '',
        mobile: props.mobile || '',
        address: {
            street: props.address?.street || '',
            city: props.cityNames?.[0] || '',
            state: props.state || '',
            country: props.country || '',
        },
    });

    const updateField = (key: string, value: any) => {
        if (key.startsWith('address.')) {
            const addressKey = key.split('.')[1];
            setFormData((prev) => ({
                ...prev,
                address: {
                    ...prev.address,
                    [addressKey]: value,
                },
            }));
        } else {
            setFormData((prev) => ({ ...prev, [key]: value }));
        }
    };

    const StaffInfoDetail = [
        {
            icon: Asset.ClientIcon,
            label: 'First Name',
            value: capitalizeFirstLetter(props.firstName) || '-',
        },
        {
            icon: Asset.ClientIcon,
            label: 'Last Name',
            value: capitalizeFirstLetter(props?.lastName) || '-',
        },
        {
            icon: Asset.GenderIcon,
            label: 'Gender',
            value: capitalizeFirstLetter(props?.gender) || '-',
        },
        {
            icon: Asset.DOBIcon,
            label: 'DOB',
            value: props?.dateOfBirth ? formatDate(props?.dateOfBirth) : '-',
        },
        {
            icon: Asset.Calender,
            label: 'Date of Joining',
            value: props?.employmentDate
                ? formatDate(props.employmentDate)
                : '-',
        },

        {
            icon: Asset.MailIcon,
            label: 'Email',
            value: props?.email || '-',
        },
        {
            icon: Asset.ClientIcon,
            label: 'Role',
            value: Staff_Roles[props?.roleType] || props?.roleType || '-',
        },
        {
            icon: Asset.LocationIcon,
            label: 'Facility',
            value: props?.facility || '-',
        },
        {
            icon: Asset.PhoneIcon,
            label: 'Contact Number',
            value: props?.mobile || '-',
        },

        // {
        //     icon: Asset.ActivityIcon,
        //     label: 'Activity Level',
        //     // value: store?.clientListDetails?.activityLevel,
        // },
        // {
        //     icon: Asset.ClientIcon,
        //     label: 'Emergency Contact Person',
        //     // value: store?.clientListDetails?.emergencyContactPerson,
        // },
        // {
        //     icon: Asset.PhoneIcon,
        //     label: 'Emergency Contact Number',
        //     // value: store?.clientListDetails?.emergencyContactPhone,
        // },
    ];

    const StaffAddressDetail = [
        {
            label: 'Address',
            value: `${props?.cityNames[0]}` || '-',
            // value: `${props?.address?.street}, ${props?.address?.addressLine2}, ${props?.address?.city}, ${props?.address?.country}, ${props?.address?.postalCode}`,
        },
        {
            label: 'City',
            value: props?.cityNames || '-',
            // value: `${props?.address?.street}, ${props?.address?.addressLine2}, ${props?.address?.city}, ${props?.address?.country}, ${props?.address?.postalCode}`,
        },
        {
            label: 'State',
            value: props?.state || '-',
            // value: `${props?.address?.street}, ${props?.address?.addressLine2}, ${props?.address?.city}, ${props?.address?.country}, ${props?.address?.postalCode}`,
        },
        {
            label: 'Country',
            value: props?.country || '-',
            // value: `${props?.address?.street}, ${props?.address?.addressLine2}, ${props?.address?.city}, ${props?.address?.country}, ${props?.address?.postalCode}`,
        },
    ];

    const DatePickerComponent = ({
        label,
        date,
        onDateChange,
        open,
        setOpen,
    }: {
        label: any;
        date: Date;
        onDateChange: (date: Date) => void;
        open: boolean;
        setOpen: (open: boolean) => void;
    }) => (
        <View style={tw`w-[45%]`}>
            <Text style={tw`text-black  pb-2`}>{label}</Text>
            <TouchableOpacity
                style={tw`w-[100%] h-[32px] flex justify-center px-1`}
                onPress={() => setOpen(true)}
            >
                <Text style={tw`text-[#455560] text-[14px]`}>
                    {dayjs(date).format('MMM D, YYYY')}
                </Text>
                <DatePicker
                    modal
                    open={open}
                    date={date}
                    onConfirm={(date) => {
                        setOpen(false);
                        onDateChange(date);
                    }}
                    onCancel={() => setOpen(false)}
                    mode="date"
                />
            </TouchableOpacity>
        </View>
    );

    return (
        <View style={tw`  flex pb-7 flex-col gap-4`}>
            <View
                style={tw` px-5  ${
                    !isEditingBasicInfo
                        ? 'justify-between flex items-center flex-row'
                        : ''
                } `}
            >
                <Text
                    style={tw`text-[#455560] text-lg text-left font-semibold `}
                >
                    Basic Information
                </Text>
                {/* {!isEditingBasicInfo ? (
                    <TouchableOpacity
                        onPress={() => setIsEditingBasicInfo((prev) => !prev)}
                    >
                        <Image
                            resizeMode="contain"
                            style={tw`w-5 h-5`}
                            source={Asset.Edit}
                        />
                    </TouchableOpacity>
                ) : (
                    ''
                )} */}
            </View>
            {!isEditingBasicInfo ? (
                <>
                    <View style={tw`flex flex-col gap-6`}>
                        {StaffInfoDetail.map((detail, index) => (
                            <View
                                key={index}
                                style={tw`flex flex-row  items-start justify-between  `}
                            >
                                <View
                                    style={tw`flex flex-row pl-7 items-center gap-2 w-[45%] `}
                                >
                                    <Image
                                        resizeMode="contain"
                                        style={tw`w-5 h-5`}
                                        source={detail.icon}
                                    />
                                    <Text
                                        style={tw`text-[#455560] text-sm  font-semibold `}
                                    >
                                        {detail.label}
                                    </Text>
                                </View>
                                <Text
                                    style={tw`w-[48%] text-[#45556066] text-sm `}
                                    numberOfLines={2} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                >
                                    {detail.value}
                                </Text>
                            </View>
                        ))}
                    </View>

                    <View
                        style={tw`flex flex-row px-5  pt-9 justify-between items-center`}
                    >
                        <Text
                            style={tw`text-[#455560] text-lg  font-semibold `}
                        >
                            Address
                        </Text>
                    </View>
                    <View style={tw`flex flex-col gap-6`}>
                        {StaffAddressDetail.map((detail, index) => (
                            <View
                                key={index}
                                style={tw`flex flex-row  items-start justify-between  `}
                            >
                                <View style={tw` pl-7  w-[45%] `}>
                                    <Text
                                        style={tw`text-[#455560] text-sm  font-semibold `}
                                    >
                                        {detail.label}
                                    </Text>
                                </View>
                                <Text
                                    style={tw`w-[48%] text-[#45556066] text-sm `}
                                    numberOfLines={2} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                >
                                    {detail.value}
                                </Text>
                            </View>
                        ))}
                    </View>
                </>
            ) : (
                <View style={tw`px-5 flex flex-col gap-6`}>
                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            First Name
                        </Text>
                        <TextInput
                            value={formData.firstName}
                            onChangeText={(val) =>
                                updateField('firstName', val)
                            }
                            placeholder="Enter First Name"
                            style={tw``}
                        />
                    </View>

                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Last Name
                        </Text>
                        <TextInput
                            value={formData.lastName}
                            onChangeText={(val) => updateField('lastName', val)}
                            placeholder="Enter Last Name"
                            style={tw``}
                        />
                    </View>

                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Date of Joining
                        </Text>
                        <TextInput
                            value={formData.employmentDate}
                            // onChangeText={(val) =>
                            //     updateField('employmentDate', val)
                            // }

                            placeholder="Enter Date of Joining"
                            style={tw``}
                        />
                    </View>

                    <View style={tw``}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Gender
                        </Text>
                        <View
                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                        >
                            <Dropdown
                                labelField="label"
                                valueField="value"
                                placeholder="Select Gender"
                                data={[
                                    { label: 'Male', value: 'male' },
                                    { label: 'Female', value: 'female' },
                                ]}
                                value={formData.gender}
                                onChange={(val) => updateField('gender', val)}
                            />
                        </View>
                    </View>

                    <View
                        style={tw`w-[100%] flex flex-row justify-between items-center border-b border-[#45556066] pb-1.5`}
                    >
                        <DatePickerComponent
                            label={
                                <Text
                                    style={tw`text-[#455560] font-medium  text-[13px]`}
                                >
                                    DOB
                                </Text>
                            }
                            date={dobDate}
                            onDateChange={setDobDate}
                            open={openDOBPicker}
                            setOpen={setOpenDOBPicker}
                        />
                        <Image source={Asset.Calender} style={[tw`w-6 h-6`]} />
                    </View>

                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Email
                        </Text>
                        <TextInput
                            placeholder="Enter Email"
                            style={tw``}
                            value={formData.email}
                            onChangeText={(val) => updateField('email', val)}
                            disabled
                        />
                    </View>
                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Mobile
                        </Text>
                        <TextInput
                            placeholder="Enter Mobile"
                            style={tw``}
                            value={formData.mobile}
                            onChangeText={(val) => updateField('mobile', val)}
                            disabled
                        />
                    </View>

                    <Text
                        style={tw`text-[#455560] text-lg mt-6 text-left font-semibold `}
                    >
                        Address
                    </Text>

                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Street Address
                        </Text>
                        <TextInput
                            value={formData.address.street}
                            onChangeText={(val) => updateField('street', val)}
                            placeholder="Enter Address"
                            style={tw``}
                        />
                    </View>
                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            City
                        </Text>
                        <TextInput
                            value={formData.address?.city}
                            onChangeText={(val) =>
                                updateField('firstName', val)
                            }
                            placeholder="Enter City"
                            style={tw``}
                        />
                    </View>
                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            State
                        </Text>
                        <TextInput
                            value={formData.address?.state}
                            onChangeText={(val) =>
                                updateField('firstName', val)
                            }
                            placeholder="Enter State"
                            style={tw``}
                        />
                    </View>
                    <View style={tw` border-b border-gray-400`}>
                        <Text
                            style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                        >
                            Country
                        </Text>
                        <TextInput
                            value={formData.address.country}
                            onChangeText={(val) =>
                                updateField('firstName', val)
                            }
                            placeholder="Enter Country"
                            style={tw``}
                        />
                    </View>

                    <Button style={tw`w-[50%] rounded-lg mx-auto mt-5`}>
                        Update Profile
                    </Button>
                </View>
            )}
        </View>
    );
};

export default memo(StaffBasicInfo);
