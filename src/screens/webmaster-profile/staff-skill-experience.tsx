import { memo } from 'react';
import {
    Dimensions,
    Image,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import RenderHTML from 'react-native-render-html';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

const StaffSkillExperience = ({ ...props }) => {
    const { width } = Dimensions.get('window');
    const StaffDetail = [
        {
            icon: Asset.ClientIcon,
            label: 'Certification Name',
            value: props?.certification,
        },
        {
            icon: Asset.ClientIcon,
            label: 'Experience',
            value: props?.experience,
        },
    ];
    const StaffDescription = [
        {
            icon: Asset.AddressIcon,
            label: 'About',
            value: props?.description,
        },
    ];
    console.log('StaffSkillExperience', { props });
    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <View style={tw` px-5 pb-6 `}>
                <Text
                    style={tw`text-[#455560] text-lg text-left font-semibold `}
                >
                    Certification
                </Text>

                {/* <TouchableOpacity>
                    <Image
                        resizeMode="contain"
                        style={tw`w-5 h-5`}
                        source={Asset.Edit}
                    />
                </TouchableOpacity> */}
            </View>
            <View style={tw``}>
                <View style={tw`flex flex-col px-5 gap-4 py-1`}>
                    {StaffDetail.map((detail, index) => (
                        <View
                            key={index}
                            style={tw`flex flex-row items-start justify-between  `}
                        >
                            <View
                                style={tw`flex flex-row items-center gap-2 w-[45%] `}
                            >
                                <Text
                                    style={tw`text-[#455560] text-[14px]  font-semibold `}
                                >
                                    {detail.label}
                                </Text>
                            </View>
                            <Text
                                style={tw`w-[48%] text-[#45556066] text-[13px] `}
                                numberOfLines={2} // You can set this to control the number of visible lines
                                ellipsizeMode="tail"
                            >
                                {detail.value}
                            </Text>
                        </View>
                    ))}
                </View>
            </View>
            <View style={tw` py-4  px-5`}>
                <View style={tw``}>
                    {StaffDescription.map((detail, index) => (
                        <View
                            key={index}
                            style={tw`flex flex-col  gap-2 items-start justify-between  `}
                        >
                            <View
                                style={tw`flex flex-row items-center gap-2  `}
                            >
                                <Text
                                    style={tw`text-[#455560] text-[14px] font-semibold `}
                                >
                                    {detail.label}
                                </Text>
                            </View>
                            <RenderHTML
                                contentWidth={width}
                                source={{
                                    html:
                                        detail?.value ||
                                        '<p class="description">No description available.</p>',
                                }}
                                tagsStyles={{
                                    h1: tw`text-base mt-1 font-normal text-[#455560]`,
                                    h2: tw`text-base mt-1 font-normal text-[#455560]`,
                                    h3: tw`text-base mt-1 font-normal text-[#455560]`,
                                    h4: tw`text-base mt-1 font-normal text-[#455560]`,
                                    p: tw`text-base mt-1 font-normal text-[#455560]`,
                                    strong: tw`text-base mt-1 font-normal text-[#455560]`,
                                    // h1: tw`text-base mt-1 font-normal text-[#455560]`,
                                }}
                                classesStyles={{
                                    description: tw`italic text-gray-500`,
                                }}
                            />
                        </View>
                    ))}
                </View>
            </View>
        </View>
    );
};

export default memo(StaffSkillExperience);
