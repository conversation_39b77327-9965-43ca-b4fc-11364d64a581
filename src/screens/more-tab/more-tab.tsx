import React, { memo } from 'react';
import {
    Alert,
    Image,
    Linking,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { DeleteAccount, Logout } from '~/redux/actions/auth-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';
import TrashIcon from '~/assets/svg/trash_icon.svg';

import { UserRole } from '~/constants/enums';
import {
    LOGIN_SCREEN,
    SELECT_ORGANIZATION,
    STAFF_LISTING,
} from '~/constants/navigation-constant';

import ProfileHeader from '~/components/common/profile-header';

import Alertify from '~/scripts/toast';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { navigationRef } from '~/hooks/useLocation';

const data = [
    { id: 1, type: 'Reset 3.0', icon: Asset.ResetIcon, screenName: 'Reset' },
    // {
    //     id: 2,
    //     type: 'THE LAB* GROUP CLASSES',
    //     icon: Asset.GroupIcon,
    //     screenName: 'GroupClasses',
    // },
    // {
    //     id: 3,
    //     type: 'BUY PT SESSIONS',
    //     icon: Asset.TagIcon,
    //     screenName: 'BuyPTSesssions',
    // },
    {
        id: 4,
        type: 'My Staff',
        icon: Asset.InfoOutlinedIcon,
        screenName: STAFF_LISTING,
    },
    // {
    //     id: 5,
    //     type: 'KNOW YOUR PT PACKAGES',
    //     icon: Asset.InfoOutlinedIcon,
    //     screenName: 'PTPackages',
    // },
    // { id: 6, type: 'Reviews', icon: Asset.ReviewsIcon, screenName: 'Reviews' },
    // { id: 7, type: 'Contact', icon: Asset.ContactIcon, screenName: 'Contact' },
    // {
    //     id: 8,
    //     type: 'Notifications',
    //     icon: Asset.NotificationIcon,
    //     screenName: 'Notifications',
    // },
    // {
    //     id: 9,
    //     type: 'Change Password',
    //     icon: Asset.Edit,
    //     screenName: 'ChangePassword',
    // },
    {
        id: 10,
        type: 'View Sessions',
        icon: Asset.EyeIcon,
        screenName: 'ViewSessions',
    },
    { id: 11, type: 'Settings', icon: Asset.Setting, screenName: 'Settings' },
    {
        id: 12,
        type: 'Privacy Policy',
        icon: Asset.PrivacyIcon,
        screenName: 'Privacy Policy',
        url: 'https://hopwellness.ai/privacy-policy',
    },
    { id: 13, type: 'Log out', icon: Asset.LogOut, screenName: 'Log Out' },
    {
        id: 14,
        type: 'Delete Account',
        icon: 'svg',
        screenName: 'Delete Account',
    },
];

const MoreTab = ({ navigation }: any) => {
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        userId: state.auth_store.userId,
    }));

    console.log('Store--------', store.role);

    function handleNavigation(item: any) {
        console.log('Item------', item);
        if (item.type === 'Log out') {
            Alert.alert(
                'Confirm Logout',
                'Are you sure you want to logout?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel',
                    },
                    {
                        text: 'Logout',
                        style: 'destructive',
                        onPress: async () => {
                            try {
                                await dispatch(Logout());
                                Alertify.success('Logout successful');
                                navigation.navigate(SELECT_ORGANIZATION);
                            } catch (error) {
                                Alertify.error(
                                    'Logout failed. Please try again.'
                                );
                            }
                        },
                    },
                ],
                { cancelable: true }
            );
        } else if (item.type === 'Privacy Policy') {
            console.log('dvdfbvdfjnvdfjnv');
            Linking.openURL(item.url);
        } else if (item.type === 'Delete Account') {
            const reqData = {
                role: store.role,
                userId: store.userId,
            };
            Alert.alert(
                'Delete Account',
                'Are you sure you want to request for delete your account?',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Confirm',
                        style: 'destructive',
                        onPress: () => {
                            dispatch(DeleteAccount(reqData));
                        },
                    },
                ],
                { cancelable: true }
            );
        } else {
            navigation.navigate(item.screenName);
        }
    }

    return (
        <View style={tw`bg-[#FAFAFA] flex-1`}>
            <ScrollView>
                <ProfileHeader />

                <View style={tw`mb-5`}>
                    {data
                        .filter((item) => {
                            if (
                                store.role === UserRole.Trainer &&
                                item.type === 'My Staff'
                            )
                                return false;
                            return true;
                        })
                        .map((item) => {
                            const isResetIcon = item.type === 'Reset 3.0';
                            const isDeleteAccount =
                                item.type === 'Delete Account';
                            return (
                                <TouchableOpacity
                                    onPress={() => handleNavigation(item)}
                                    key={item.id}
                                    style={tw`flex flex-row gap-5 items-center px-5 py-4 border-b border-[#4555604D]`}
                                >
                                    {isDeleteAccount ? (
                                        <TrashIcon width={24} height={24} />
                                    ) : (
                                        <Image
                                            style={
                                                isResetIcon
                                                    ? tw`w-5 h-5`
                                                    : tw`w-6 h-6`
                                            }
                                            resizeMode="contain"
                                            source={item.icon}
                                        />
                                    )}

                                    <Text
                                        style={tw`${
                                            isDeleteAccount
                                                ? 'text-red-600'
                                                : 'text-[#455560]'
                                        } uppercase text-base`}
                                    >
                                        {item.type}
                                    </Text>
                                </TouchableOpacity>
                            );
                        })}
                </View>
            </ScrollView>
        </View>
    );
};

export default memo(MoreTab);
