import { RouteProp, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import { Text, View } from 'react-native';
import { ForgotPasswordRequestOtp } from '~/redux/actions/auth-actions';
import { isMobileEmail } from '~/utils/utils';

import tw from '~/styles/tailwind';

import { VERIFICATION_SCREEN } from '~/constants/navigation-constant';

import { RootStackParamList } from '~/navigation/navigation-types';

import { Button, TextInput } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';

import { useAppDispatch } from '~/hooks/redux-hooks';

type ForgotPasswordScreenRouteProp = RouteProp<
    RootStackParamList,
    'FORGOT_PASSWORD'
>;

type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<
    RootStackParamList,
    'FORGOT_PASSWORD'
>;

interface ForgotPasswordScreenProps {
    navigation: ForgotPasswordScreenNavigationProp;
}

const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = ({
    navigation,
}) => {
    const dispatch = useAppDispatch();
    const route = useRoute<ForgotPasswordScreenRouteProp>();
    const { contact } = route.params;

    function handleSubmit(values: any) {
        const type = isMobileEmail(values.contact);
        if (!type) return;
        dispatch(
            ForgotPasswordRequestOtp({
                type,
                [type]: values.contact,
            })
        )
            .unwrap()
            .then((data) => {
                navigation.navigate(VERIFICATION_SCREEN, {
                    forgotPasswordRequest: true,
                    type,
                    contact: values.contact,
                    otp: data.res.data.otp,
                });
            });
    }

    return (
        <>
            <View>
                <BackButtonHeading />
                <View style={tw`px-[5%] mt-5`}>
                    <Text style={tw`text-24 text-[#455560] font-700 font-bold`}>
                        Forgot Password?
                    </Text>
                    <Text style={tw`mt-2 text-14 text-[#455560] mb-5`}>
                        Enter your information below
                    </Text>
                    <Formik onSubmit={handleSubmit} initialValues={{ contact }}>
                        {({
                            errors,
                            handleChange,
                            handleSubmit,
                            handleBlur,
                            values,
                            touched,
                        }) => (
                            <>
                                <TextInput
                                    stacked
                                    label="Email/Mobile"
                                    placeholder="Email/Mobile"
                                    onChangeText={handleChange('contact')}
                                    value={values.contact}
                                    onBlur={handleBlur('contact')}
                                />
                                {errors.contact && touched.contact && (
                                    <Text>{errors.contact}</Text>
                                )}
                                <View
                                    style={tw`flex justify-center items-center`}
                                >
                                    <Button
                                        nextImage
                                        style={tw`w-[50%] rounded-full mt-10`}
                                        onPress={handleSubmit}
                                    >
                                        Send
                                    </Button>
                                </View>
                            </>
                        )}
                    </Formik>
                </View>
            </View>
        </>
    );
};

export default ForgotPasswordScreen;
