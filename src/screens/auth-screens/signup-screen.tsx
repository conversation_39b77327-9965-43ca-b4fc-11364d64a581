import { Route<PERSON>rop, useRoute } from '@react-navigation/native';
import { Formik } from 'formik';
import { useState } from 'react';
import {
    ImageBackground,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
// import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import * as Yup from 'yup';
import { RegisterUser } from '~/redux/actions/auth-actions';
import { GetUserDetails } from '~/redux/actions/profile-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { RootStackParamList } from '~/navigation/navigation-types';

import { Button, TextInput } from '~/components/atoms';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

const SignupSchema = Yup.object().shape({
    firstName: Yup.string().required('First Name is required'),
    lastName: Yup.string().required('Last Name is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    mobile: Yup.string()
        .matches(/^\d{10}$/, 'Phone number is not valid')
        .required('Phone number is required'),
    password: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password'), null], 'Passwords must match')
        .required('Confirm Password is required'),
});

type SignupScreenRouteProp = RouteProp<RootStackParamList, 'SIGNUP_SCREEN'>;

interface FormValues {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    password: string;
    confirmPassword: string;
}

const SignupScreen = () => {
    const route = useRoute<SignupScreenRouteProp>();
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        optVerificationCode: state.auth_store.otpVerificationCode,
    }));
    const {
        type = 'email',
        contact,
        otp,
        forgotPasswordRequest,
    } = route.params || {};

    function registerUser(values: FormValues) {
        dispatch(
            RegisterUser({
                type,
                [type]: contact,
                ...values,
                mobile: values.mobile,
                confirmPassword: values.confirmPassword,
                otpVerificationCode: store.optVerificationCode,
            })
        )
            .unwrap()
            .then((data) => {
                console.log(data);
                dispatch(GetUserDetails({}));
            });
    }

    return (
        <View style={styles.container}>
            <ScrollView>
                <View style={tw`h-auto justify-center items-center`}>
                    <ImageBackground
                        resizeMode="cover"
                        style={tw`h-100 w-full`}
                        source={Asset.Sign_upBg}
                    >
                        <View style={tw`absolute px-[5%] bottom-[25%]`}>
                            <Text style={[tw`text-black`, styles.headerTitle]}>
                                HELLO ROOKIES,
                            </Text>
                            <Text
                                style={[
                                    tw`text-black w-[60%] text-12 uppercase`,
                                ]}
                            >
                                Enter your informations below or login with a
                                other account
                            </Text>
                        </View>
                        <View style={tw``}></View>
                    </ImageBackground>
                </View>
                <View style={tw`h-auto pt-[5%] px-[5%]`}>
                    <Formik
                        initialValues={{
                            firstName: '',
                            lastName: '',
                            email: type === 'email' ? contact : '',
                            mobile: type === 'mobile' ? contact : '',
                            password: '',
                            confirmPassword: '',
                        }}
                        validationSchema={SignupSchema}
                        onSubmit={registerUser}
                    >
                        {({
                            handleChange,
                            handleBlur,
                            handleSubmit,
                            values,
                            errors,
                            touched,
                        }) => (
                            <>
                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>First Name</Text>
                                    <TextInput
                                        style={tw`border-b border-black pb-2`}
                                        placeholder="First Name"
                                        onChangeText={handleChange('firstName')}
                                        onBlur={handleBlur('firstName')}
                                        value={values.firstName}
                                    />
                                    {errors.firstName && touched.firstName && (
                                        <Text style={styles.error}>
                                            {errors.firstName}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Last Name</Text>
                                    <TextInput
                                        style={tw`border-b border-black pb-2`}
                                        placeholder="Last Name"
                                        onChangeText={handleChange('lastName')}
                                        onBlur={handleBlur('lastName')}
                                        value={values.lastName}
                                    />
                                    {errors.lastName && touched.lastName && (
                                        <Text style={styles.error}>
                                            {errors.lastName}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Email</Text>
                                    <TextInput
                                        style={tw`border-b border-black pb-2`}
                                        placeholder="Email"
                                        onChangeText={handleChange('email')}
                                        onBlur={handleBlur('email')}
                                        value={values.email}
                                        keyboardType="email-address"
                                        editable={type !== 'email'}
                                    />
                                    {errors.email && touched.email && (
                                        <Text style={styles.error}>
                                            {errors.email}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Phone</Text>
                                    <TextInput
                                        style={tw`border-b border-black pb-2`}
                                        placeholder="Phone"
                                        onChangeText={handleChange('mobile')}
                                        onBlur={handleBlur('mobile')}
                                        value={values.mobile}
                                        keyboardType="phone-pad"
                                        editable={type !== 'mobile'}
                                    />
                                    {errors.mobile && touched.mobile && (
                                        <Text style={styles.error}>
                                            {errors.mobile}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>Password</Text>
                                    <View style={{}}>
                                        <TextInput
                                            style={tw`border-b border-black pb-2 w-full`}
                                            placeholder="Password"
                                            onChangeText={handleChange(
                                                'password'
                                            )}
                                            onBlur={handleBlur('password')}
                                            value={values.password}
                                            type="password"
                                        />
                                    </View>
                                    {errors.password && touched.password && (
                                        <Text style={styles.error}>
                                            {errors.password}
                                        </Text>
                                    )}
                                </View>

                                <View style={styles.inputContainer}>
                                    <Text style={styles.label}>
                                        Password again
                                    </Text>
                                    <View style={{}}>
                                        <TextInput
                                            type="password"
                                            style={tw`border-b border-black pb-2 w-full`}
                                            placeholder="Confirm Password"
                                            onChangeText={handleChange(
                                                'confirmPassword'
                                            )}
                                            onBlur={handleBlur(
                                                'confirmPassword'
                                            )}
                                            value={values.confirmPassword}
                                        />
                                    </View>
                                    {errors.confirmPassword &&
                                        touched.confirmPassword && (
                                            <Text style={styles.error}>
                                                {errors.confirmPassword}
                                            </Text>
                                        )}
                                </View>
                                <View
                                    style={tw`flex justify-center items-end mt-10 mb-[8%]`}
                                >
                                    <Button
                                        nextImage
                                        style={[
                                            tw`w-[70%] rounded-full font-bold`,
                                            styles.buttonText,
                                        ]}
                                        onPress={handleSubmit}
                                    >
                                        Complete Sign-up
                                    </Button>
                                </View>
                            </>
                        )}
                    </Formik>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        // padding: 20,
        backgroundColor: '#f5f5f5',
    },
    headerTitle: {
        fontSize: 30,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    headerSubtitle: {
        fontSize: 14,
        color: '#777',
        marginBottom: 30,
    },
    inputContainer: {
        marginBottom: 20,
    },
    label: {
        fontSize: 14,
        marginBottom: 5,
    },
    input: {
        height: 40,
        paddingHorizontal: 10,
    },
    passwordContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: 'gray',
        // borderWidth: 1,
        paddingHorizontal: 10,
        borderRadius: 5,
    },
    error: {
        color: 'red',
        marginTop: 5,
    },
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        color: 'white',
        justifyContent: 'center',
        backgroundColor: '#000',
        padding: 15,
        // borderRadius: 5,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        marginRight: 10,
    },
});

export default SignupScreen;
