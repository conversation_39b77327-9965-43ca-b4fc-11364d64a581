import React, { memo } from 'react';
import { Text, View } from 'react-native';
import { List } from 'react-native-paper';

import tw from '~/styles/tailwind';

const TestScreen = () => {
    return (
        <View style={tw``}>
            <List.AccordionGroup>
                <List.Accordion
                    title="Accordion 1"
                    id="1"
                    right={(props) => <List.Icon {...props} icon="folder" />}
                >
                    <List.Item title="Item 1" />
                </List.Accordion>
                <List.Accordion title="Accordion 2" id="2">
                    <List.Item title="Item 2" />
                </List.Accordion>
                <View>
                    <List.Accordion title="Accordion 3" id="3">
                        <List.Item title="Item 3" />
                    </List.Accordion>
                </View>
            </List.AccordionGroup>
        </View>
    );
};

export default memo(TestScreen);
