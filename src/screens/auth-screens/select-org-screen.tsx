// SelectOrganizationScreen.tsx
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import React, { useCallback, useState } from 'react';
import {
    FlatList,
    Image,
    KeyboardAvoidingView,
    Modal,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { organizationList } from '~/redux/actions/auth-actions';
import {
    ClearSelectedOrganization,
    setSelectedOrganization,
} from '~/redux/slices/auth-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { LOGIN_SCREEN } from '~/constants/navigation-constant';

import { Button } from '~/components/atoms';

import Alertify from '~/scripts/toast';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';

const SelectOrganizationScreen = () => {
    const [isModalVisible, setModalVisible] = useState(false);
    const [selectedOrganizationData, setSelectedOrganizationData] = useState<
        string | null
    >(null);
    const [searchValue, setSearchValue] = useState('');
    const dispatch = useAppDispatch();
    const navigation = useNavigation();
    const debouncedRequest = useDebounce((callback: any) => callback(), 200);

    const store = useAppSelector((state) => ({
        organizationList: state.auth_store.organizationList,
    }));

    console.log('Stoere----vdvdv-----', store.organizationList);

    const handleSelectOrganization = () => {
        dispatch(ClearSelectedOrganization());
        setModalVisible(true);
    };

    useFocusEffect(
        useCallback(() => {
            const payload = { search: searchValue };
            if (searchValue) {
                debouncedRequest(() => {
                    dispatch(organizationList({ payload }));
                });
            }
        }, [searchValue])
    );

    const handleOrgSelect = (org: {
        _id: string;
        organizationName: string;
    }) => {
        setSelectedOrganizationData(org.organizationName);
        dispatch(
            setSelectedOrganization({
                organizationId: org._id,
                organizationName: org.organizationName,
            })
        );
        setModalVisible(false);
    };

    const handleNext = () => {
        if (selectedOrganizationData) {
            (navigation as any).navigate(LOGIN_SCREEN);
        } else {
            Alertify.error('Please select an organization first.');
        }
    };

    return (
        <View style={tw`flex-1 justify-start items-center`}>
            <View style={tw`w-full bg-primary flex py-6 justify-center`}>
                <Text
                    style={tw`text-[22px] font-semibold text-white bg-primary w-full text-center`}
                >
                    Select your Organization
                </Text>
            </View>

            <Image
                source={Asset.SelectOrgHop}
                style={tw` w-52 h-28 mb-8 mt-5`}
                resizeMode="contain"
            />

            <Text
                style={tw`text-14 font-semibold text-gray-700 mb-2 px-6 self-start`}
            >
                Select your Organization
            </Text>

            <TouchableOpacity
                style={tw`w-[94%] border-b border-gray-300 py-2 mb-3 px-3`}
                onPress={handleSelectOrganization}
            >
                <Text style={tw`text-base text-[#455560]`}>
                    {selectedOrganizationData || 'Select your Organization'}
                </Text>
            </TouchableOpacity>

            {/* <TouchableOpacity
                style={tw`bg-primary px-6 py-3 rounded-md `}
                onPress={handleNext}
            >
                <Text style={tw`text-white text-center text-base`}>Next</Text>
            </TouchableOpacity> */}
            <View style={tw`flex justify-center pt-5 items-center`}>
                <Button
                    style={tw`mt-3 w-[45%] rounded-lg text-center`}
                    onPress={handleNext}
                    // loading={loader}
                >
                    Next
                </Button>
            </View>

            <Modal
                style={tw` h-full`}
                visible={isModalVisible}
                animationType="slide"
                transparent={true}
            >
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                    style={tw` h-full flex justify-end  bg-transparent `}
                >
                    <View style={tw`h-[85%] bg-white flex justify-end `}>
                        <View
                            style={tw`bg-white py-4 px-4 rounded-b-xl flex flex-row justify-between`}
                        >
                            <View></View>
                            <Text
                                style={tw`text-[#455560] text-14 text-center font-semibold mt-1`}
                            >
                                Find your Business
                            </Text>

                            <TouchableOpacity
                                onPress={() => setModalVisible(false)}
                            >
                                {/* <Text style={styles.closeButtonText}>×</Text> */}
                                <Image
                                    style={tw` w-4 h-4`}
                                    source={Asset.CloseIcon}
                                    resizeMode="contain"
                                />
                            </TouchableOpacity>
                        </View>

                        <View style={tw`px-4 `}>
                            <View
                                style={tw`flex-row items-center gap-3 bg-gray-100 rounded-md px-3 `}
                            >
                                <Image
                                    style={tw` h-6 w-5 `}
                                    source={Asset.BlackSearchIcon}
                                />
                                <TextInput
                                    placeholder="Search your organization"
                                    placeholderTextColor={'#455560'}
                                    value={searchValue}
                                    onChangeText={setSearchValue}
                                    style={tw`flex-1 text-gray-700 `}
                                    returnKeyType="go"
                                />
                            </View>
                        </View>

                        <FlatList
                            data={store.organizationList}
                            keyExtractor={(item) => item._id}
                            renderItem={({ item }) => (
                                <TouchableOpacity
                                    style={tw`px-4 py-3 border-b border-gray-200`}
                                    onPress={() => handleOrgSelect(item)}
                                >
                                    <Text style={tw`text-base text-gray-800`}>
                                        {item.organizationName}
                                    </Text>
                                </TouchableOpacity>
                            )}
                            style={tw`mt-4`}
                        />
                    </View>
                </KeyboardAvoidingView>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    closeButtonText: {
        fontSize: 24,
        color: '#455560',
    },
});

export default SelectOrganizationScreen;
