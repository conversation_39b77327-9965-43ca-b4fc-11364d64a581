import { RouteProp, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import { Text, View } from 'react-native';
import * as Yup from 'yup';
import { ResetPassword } from '~/redux/actions/auth-actions';
import { isMobileEmail } from '~/utils/utils';

import tw from '~/styles/tailwind';

import { DASHBOARD_STACK } from '~/constants/navigation-constant';

import { RootStackParamList } from '~/navigation/navigation-types';

import { Button, TextInput } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';

import { useAppDispatch } from '~/hooks/redux-hooks';

type ResetPasswordScreenNavigationProps = NativeStackNavigationProp<
    RootStackParamList,
    'RESET_PASSWORD_SCREEN'
>;

type ResetPasswordScreenRouteProps = RouteProp<
    RootStackParamList,
    'RESET_PASSWORD_SCREEN'
>;

interface ResetPasswordScreenProps {
    navigation: ResetPasswordScreenNavigationProps;
}

const ResetPassWordSchema = Yup.object().shape({
    password: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password'), null], 'Passwords must match')
        .required('Confirm Password is required'),
});

const ResetPasswordScreen: React.FC<ResetPasswordScreenProps> = ({
    navigation,
}) => {
    const dispatch = useAppDispatch();
    const route = useRoute<ResetPasswordScreenRouteProps>();
    const { contact, otp, type, forgotPasswordRequest } = route.params;

    function handleSubmit(values: any) {
        const type = isMobileEmail(values.contact);
        if (!type) return;
        dispatch(
            ResetPassword({
                type,
                [type]: values.contact,
                password: values.password,
                confirmPassword: values.confirmPassword,
            })
        )
            .unwrap()
            .then((data) => {
                console.log(data);
                navigation.navigate(DASHBOARD_STACK);
            });
    }

    return (
        <>
            <View>
                <BackButtonHeading />
                <View style={tw`px-[5%] mt-5`}>
                    <Text
                        style={tw`uppercase text-24 text-[#455560] font-700 font-bold`}
                    >
                        Reset your Password
                    </Text>
                    <Text style={tw`mt-2 text-14 mb-5 text-[#455560]`}>
                        Password must be 8+ chars, with upper & lower case, a
                        number, special character and no space.
                    </Text>
                    <Formik
                        initialValues={{
                            contact: contact,
                            password: '',
                            confirmPassword: '',
                        }}
                        validationSchema={ResetPassWordSchema}
                        onSubmit={handleSubmit}
                    >
                        {({
                            handleSubmit,
                            handleChange,
                            values,
                            handleBlur,
                            errors,
                            touched,
                        }) => (
                            <>
                                <View style={tw`flex flex-col gap-3`}>
                                    <TextInput
                                        label="Email/Mobile"
                                        onBlur={handleBlur('contact')}
                                        onChangeText={handleChange('contact')}
                                        value={values.contact}
                                        keyboardType="email-address"
                                        editable={false}
                                        stacked
                                    />
                                    <TextInput
                                        stacked
                                        type="password"
                                        label="Password"
                                        value={values.password}
                                        onChangeText={handleChange('password')}
                                        onBlur={handleBlur('password')}
                                    />
                                    <TextInput
                                        stacked
                                        type="password"
                                        label="Confirm Password"
                                        value={values.confirmPassword}
                                        onChangeText={handleChange(
                                            'confirmPassword'
                                        )}
                                        onBlur={handleBlur('confirmPassword')}
                                    />
                                </View>
                                <View
                                    style={tw`flex justify-center items-center`}
                                >
                                    <Button
                                        style={tw`w-[60%] rounded-full mt-10`}
                                        onPress={handleSubmit}
                                        nextImage
                                    >
                                        Set Password
                                    </Button>
                                </View>
                            </>
                        )}
                    </Formik>
                </View>
            </View>
        </>
    );
};

export default ResetPasswordScreen;
