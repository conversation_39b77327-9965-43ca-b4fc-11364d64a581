import { useFocusEffect, useRoute } from '@react-navigation/native';
import React, { memo, useCallback, useEffect, useState } from 'react';
import {
    FlatList,
    Image,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    GetAllFacilitiesByOrganization,
    GetAllFacilitiesByOrganizationV2,
    GetAllFacilitiesByStaffId,
} from '~/redux/actions/facility-actions';
import { setFacilityID } from '~/redux/slices/auth-slice';
import { SetSelectedFacility } from '~/redux/slices/facility-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { UserRole } from '~/constants/enums';
import {
    CALENDAR_SCREEN,
    HOME_SCREEN,
    LOGIN_SCREEN,
} from '~/constants/navigation-constant';

import TextInput from '~/components/atoms/text-input';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { navigationRef, useLocation } from '~/hooks/useLocation';

const SelectLocationScreen = ({ navigation }: any) => {
    const route = useRoute();
    const { selectTrainerLocation }: any = route.params || {};

    console.log({ selectTrainerLocation });

    const [facilityLists, setFacilityLists] = useState([]);
    const [search, setSearch] = useState('');
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        facilityListByOrg: state.facility_store.facilityListByOrg,
        userId: state.auth_store.userId,
        role: state.auth_store.role,
    }));

    function handleSearch(text: string) {
        setSearch(text);
    }

    console.log('Facility list---------', store);

    // useEffect(() => {
    //     // dispatch(GetAllFacilitiesByOrganizationV2({}))
    //     //     .unwrap()
    //     //     .then((data) => {
    //     //         console.log(data.res.data);
    //     //         setFacilityLists(data.res.data);
    //     //     });
    //     console.log('Fetching facilities...');
    //     dispatch(GetAllFacilitiesByStaffId({ userId: store.userId }));
    // }, []);

    useFocusEffect(
        useCallback(() => {
            if (store.role === UserRole.ORGANIZATION) {
                dispatch(GetAllFacilitiesByOrganizationV2({}))
                    .unwrap()
                    .then((data: any) => {
                        console.log(data.res.data);
                        // setFacilityLists(data.res.data);
                    });
            } else {
                console.log('Fetching facilities from useFocusEffect...');
                dispatch(GetAllFacilitiesByStaffId({ userId: store.userId }));
            }
        }, [])
    );

    console.log('facilityLists----------', facilityLists);

    const normalizedFacilities =
        store.role === UserRole.ORGANIZATION
            ? store.facilityListByOrg?.map((facility: any) => ({
                  ...facility,
                  name: facility.facilityName,
              }))
            : store.facilityList;

    const filteredFacilities = normalizedFacilities?.filter((facility: any) =>
        facility?.name?.toLowerCase()?.includes(search?.toLowerCase())
    );

    const handleFacilityPress = (facility: any) => {
        dispatch(SetSelectedFacility(facility));
        // if (selectTrainerLocation) {
        //     navigationRef.goBack();
        // } else {
        //     navigation.navigate(LOGIN_SCREEN);
        // }
        dispatch(setFacilityID(facility._id));
        navigation.navigate(CALENDAR_SCREEN, { facilityId: facility._id });
        // navigation.navigate(HOME_SCREEN, { facilityId: facility._id });
    };

    return (
        <>
            <ScrollView>
                <View style={tw`bg-primary px-5 py-12`}>
                    <Text
                        style={tw`text-[24px] text-white font-bold mb-8 text-center`}
                    >
                        Select your location
                    </Text>
                    <View
                        style={tw`border border-[#7A7A7A] rounded-md bg-white p-1 flex flex-row  items-center`}
                    >
                        <Image
                            resizeMode="cover"
                            style={tw`w-8 h-8`}
                            source={Asset.LocationIcon}
                        />
                        <View style={tw`w-full`}>
                            <TextInput
                                value={search}
                                onChangeText={handleSearch}
                                placeholder="Current Location"
                            />
                        </View>
                    </View>
                </View>
                <View style={tw`flex-1 p-4 `}>
                    <FlatList
                        data={filteredFacilities}
                        keyExtractor={(item: any) => item._id}
                        renderItem={({ item }: any) => (
                            <View style={tw`border-b border-[#2C2C2E4D]`}>
                                <View style={tw` p-4   rounded`}>
                                    <Text style={tw`text-lg text-[#000000]`}>
                                        {item?.name}
                                    </Text>
                                    <Text style={tw`text-[#455560] text-sm`}>
                                        {item?.address?.addressLine1}
                                    </Text>
                                    {item?.address?.cityName &&
                                        item?.address?.stateName &&
                                        item?.address?.postalCode && (
                                            <Text
                                                style={tw`text-[#455560] text-sm`}
                                            >
                                                {item.address.cityName},{' '}
                                                {item.address.stateName} -{' '}
                                                {item.address.postalCode}
                                            </Text>
                                        )}
                                </View>
                                <TouchableOpacity
                                    style={tw` px-4 pb-4`}
                                    onPress={() => handleFacilityPress(item)}
                                >
                                    <View
                                        style={tw`rounded-md w-40 px-2 py-1.4 border-[#43434380] border bg-[#fff] border-opacity-50  `}
                                    >
                                        <Text
                                            style={tw`text-[14px] text-black text-center`}
                                        >
                                            Select this location
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        )}
                    />
                </View>
            </ScrollView>

            {/* <TouchableOpacity
                style={tw` px-4 py-2 flex justify-center items-center sticky w-full bg-transparent top-0`}
            >
                <View
                    style={tw`rounded-full w-[40%] flex flex-row items-center justify-center gap-2 shadow bg-[#D0FF01] p-2 `}
                >
                    <Image
                        style={tw`w-8 h-8`}
                        resizeMode="cover"
                        source={Asset.MapIcon}
                    />
                    <Text style={tw`text-[14px] text-black text-center`}>
                        View Map
                    </Text>
                </View>
            </TouchableOpacity> */}
        </>
    );
};

export default memo(SelectLocationScreen);
