import { RouteProp, useRoute } from '@react-navigation/native';
import { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Keyboard,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    RequestOtpLoginRegistration,
    VerifyOtp,
} from '~/redux/actions/auth-actions';
import {
    GetClientDetails,
    GetStaffDetails,
} from '~/redux/actions/client-action';
import { GetUserDetails } from '~/redux/actions/profile-actions';

import tw from '~/styles/tailwind';

import {
    DASHBOARD_STACK,
    ONBOARD_STACK,
    RESET_PASSWORD_SCREEN,
    SIGNUP_SCREEN,
} from '~/constants/navigation-constant';

import { RootStackParamList } from '~/navigation/navigation-types';

import { Button } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';

import Alertify from '~/scripts/toast';

import { useAppDispatch } from '~/hooks/redux-hooks';

type VerificationScreenRouteProp = RouteProp<
    RootStackParamList,
    'VERIFICATION_SCREEN'
>;

const VerificationScreen = ({ navigation }: any) => {
    const route = useRoute<VerificationScreenRouteProp>();
    const dispatch = useAppDispatch();
    const [timer, setTimer] = useState(30);
    const [DisplayLoader, setDisplayLoader] = useState(false);
    const inputs = useRef([]);
    const {
        type,
        contact,
        forgotPasswordRequest = false,
        otp: optApi,
    } = route.params;

    const isValidOtp = /^\d{6}$/.test(optApi);

    const initialOtp = isValidOtp
        ? String(optApi).split('')
        : ['', '', '', '', '', ''];
    const [otp, setOtp] = useState(['', '', '', '', '', '']);

    const handleResend = () => {
        setTimer(30);
        dispatch(RequestOtpLoginRegistration({ type, [type]: contact }))
            .unwrap()
            .then((data) => {
                const otp = data?.res?.data?.data?.otp;
                if (data?.res?.status === 200 || data?.res?.status === 201) {
                    Alertify.success('OTP Resent Successfully');
                }
            });
    };

    useEffect(() => {
        if (timer > 0) {
            const countdown = setTimeout(() => setTimer(timer - 1), 1000);
            return () => clearTimeout(countdown);
        }
    }, [timer]);

    const handleOtpChange = (index: any, text: any) => {
        const newOtp = [...otp];
        newOtp[index] = text;
        setOtp(newOtp);

        // Auto move to next input if typed
        if (text && index < 5) {
            inputs.current[index + 1]?.focus();
        }

        const otpValue = newOtp.join('');
        const isComplete = otpValue.length === 6 && /^\d{6}$/.test(otpValue);

        if (isComplete) {
            Keyboard.dismiss();
            handleSubmit(otpValue);
        }
    };

    const handleKeyPress = (index: any, key: any) => {
        if (key === 'Backspace') {
            const newOtp = [...otp];

            if (otp[index] === '') {
                if (index > 0) {
                    newOtp[index - 1] = '';
                    setOtp(newOtp);
                    inputs.current[index - 1]?.focus();
                }
            } else {
                newOtp[index] = '';
                setOtp(newOtp);
            }
        }
    };

    const handleSubmit = (otpFromInput?: string) => {
        const otpValue = otpFromInput || otp.join('');
        setDisplayLoader(true);
        Keyboard.dismiss();
        dispatch(
            VerifyOtp({
                type,
                [type]: contact,
                forgotPasswordRequest,
                otp: Number(otpValue),
            })
        )
            .unwrap()
            .then((data) => {
                console.log(data.res.data.userExist, forgotPasswordRequest);
                console.log('first data-----', data.res.data);
                if (data?.res?.data?.data?.userExist && forgotPasswordRequest) {
                    navigation.navigate(RESET_PASSWORD_SCREEN, {
                        type,
                        otp: otpValue,
                        contact,
                        forgotPasswordRequest,
                    });
                } else if (!data?.res?.data?.data?.userExist)
                    navigation.navigate(SIGNUP_SCREEN, {
                        type,
                        otp: otpValue,
                        contact,
                    });
                else {
                    // dispatch(GetUserDetails({}));
                    dispatch(
                        GetStaffDetails({
                            clientId: data?.res?.data?.data?.user?._id,
                        })
                    );
                    navigation.navigate(DASHBOARD_STACK);
                }
            })
            .finally(() => setDisplayLoader(false));
    };

    return (
        <View>
            <BackButtonHeading />
            <View style={tw`px-[5%] mt-5`}>
                <Text style={tw`text-24 text-[#455560] font-700 font-bold`}>
                    Verification OTP
                </Text>

                <Text style={tw`text-14 text-[#455560] mt-3`}>
                    Enter your OTP
                </Text>
                <Text style={tw`text-14 text-[#455560]`}>
                    Check your {type}. We've sent you the verification code at
                    your {type} : {contact}
                </Text>

                {/* <Text style={tw`text-[#455560]`}>Otp is {optApi}</Text> */}
                <View style={styles.fieldContainer}>
                    {otp?.map((digit, index) => (
                        <TextInput
                            key={index}
                            ref={(ref) => (inputs.current[index] = ref)}
                            style={[
                                tw`text-24`,
                                styles.otpInput,
                                digit && styles.otpInputFilled,
                            ]}
                            value={digit}
                            onChangeText={(text) =>
                                handleOtpChange(index, text)
                            }
                            keyboardType="numeric"
                            maxLength={1}
                            autoFocus={index === 0}
                            onKeyPress={({ nativeEvent }) =>
                                handleKeyPress(index, nativeEvent.key)
                            }
                        />
                    ))}
                </View>
                {timer > 0 ? (
                    <Text style={tw`text-center text-[#455560] mt-5`}>
                        Resend OTP in {timer} seconds
                    </Text>
                ) : (
                    <TouchableOpacity onPress={handleResend}>
                        <Text style={tw`text-center  mt-5 text-blue-500`}>
                            Resend OTP
                        </Text>
                    </TouchableOpacity>
                )}
                <View style={tw`flex justify-center items-center`}>
                    <Button
                        style={tw`w-60 rounded-full text-center  mt-10`}
                        onPress={DisplayLoader ? null : handleSubmit}
                        loading={DisplayLoader}
                    >
                        Verify
                    </Button>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    fieldContainer: {
        marginTop: 25,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    otpInput: {
        borderBottomWidth: 2,
        borderBottomColor: 'gray',
        marginHorizontal: 1,
        paddingHorizontal: 8,
        textAlign: 'center',
        width: 40,
        marginRight: 12,
    },
    otpInputFilled: {
        borderBottomColor: '#000000',
        color: '#000000',
    },
});

export default VerificationScreen;
