import { useFocusEffect, useRoute } from '@react-navigation/native';
import React, { memo, useCallback } from 'react';
import { Image, Text, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { OrderInvoiceDetails } from '~/redux/actions/order-action';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

import { capitalizeFirstLetter } from '~/scripts/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';


const OrderDetails = () => {
    const dispatch = useAppDispatch();
    const route = useRoute();
    const { orderId }: any = route.params || {};

    console.log('firOrderIDSst----------', orderId);

    const store = useAppSelector((state) => ({
        orderDetails: state.order_store.orderDetails,
    }));

    console.log('Order-------------', store.orderDetails);

    useFocusEffect(
        useCallback(() => {
            if (orderId) {
                dispatch(OrderInvoiceDetails({ orderId }));
            }
        }, [orderId])
    );

    return (
        <View style={tw`bg-[#FAFAFA]  flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'Order Details'} />
                <View style={tw`px-5 `}>
                    {/* ------------------upper card-------------------------- */}
                    <View
                        style={tw` flex flex-row items-center py-7 justify-between`}
                    >
                        <View style={tw` flex flex-row items-center gap-2 `}>
                            <View
                                style={tw`w-18 h-18 border border-gray-300 rounded-full overflow-hidden`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={tw`w-full h-full  rounded-full`}
                                    // source={{
                                    //     uri: store.clientListDetails?.photo,
                                    // }}
                                    source={Asset.ProfileIconNew}
                                />
                            </View>
                            <View style={tw` flex flex-col gap-1 w-[55%] `}>
                                <Text
                                    style={tw`text-md font-bold text-[#455560]`}
                                >
                                    {store.orderDetails?.clientDetails?.name}
                                </Text>
                                <Text
                                    numberOfLines={1} // You can set this to control the number of visible lines
                                    ellipsizeMode="tail"
                                    style={tw`text-sm font-bold text-[#455560] `}
                                >
                                    Order Id &nbsp;
                                    <Text
                                        style={tw`font-normal text-xs  text-[#72849A]`}
                                    >
                                        {store.orderDetails?.orderId}
                                    </Text>
                                </Text>
                            </View>
                        </View>
                        <View
                            style={tw`flex flex-col items-center justify-center pr-4`}
                        >
                            <Text
                                style={tw`text-sm font-medium text-[#455560] `}
                            >
                                ₹ {store.orderDetails?.grandTotal}
                            </Text>
                            <View
                                style={tw`${
                                    store.orderDetails?.paymentStatus ===
                                    'completed'
                                        ? 'bg-[#dcf3de]'
                                        : 'bg-[#ECF2FE]'
                                } py-[0.11rem] px-3 rounded-md `}
                            >
                                <Text
                                    style={tw`text-xs font-normal ${
                                        store.orderDetails?.paymentStatus ===
                                        'completed'
                                            ? 'text-[#11B51F]'
                                            : 'text-[#3E79F7]'
                                    } capitalize`}
                                >
                                    {capitalizeFirstLetter(
                                        store.orderDetails?.paymentStatus
                                    )}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* ------------------order detail table-------------------------- */}

                    <View style={tw`bg-white rounded-lg py-3 px-3 `}>
                        <View>
                            <Text
                                style={tw`text-16  font-bold mb-2 text-[#455560]`}
                            >
                                Item Details
                            </Text>

                            {[
                                ...(store.orderDetails?.purchaseItems || []),
                                ...(store.orderDetails?.productItem || []),
                            ]?.map((item: any, index: number) => (
                                <View
                                    key={index}
                                    style={tw`border-t border-[#455560] border-opacity-10   px-2 py-1 rounded-lg`}
                                >
                                    <Text
                                        style={tw`text-16 font-semibold  text-[#455560]`}
                                    >
                                        {item?.name}
                                    </Text>
                                    <View
                                        style={tw`border-b flex flex-row justify-between items-center border-[#455560] pb-4 border-opacity-10`}
                                    >
                                        <Text
                                            style={tw`text-sm  mt-2 text-[#72849A]`}
                                        >
                                            Quantity: {item?.quantity || 1}
                                        </Text>
                                        {item?.price && (
                                            <Text
                                                style={tw`text-sm  mt-1 text-[#72849A]`}
                                            >
                                                Price: ₹{item?.price}
                                            </Text>
                                        )}
                                        {item?.mrp &&
                                            item?.mrp !== item?.price && (
                                                <Text
                                                    style={tw`text-sm  mt-1 text-[#72849A]`}
                                                >
                                                    MRP: ₹{item?.mrp}
                                                </Text>
                                            )}
                                    </View>
                                </View>
                            ))}

                            <View
                                style={tw`mt-4 border border-[#455560] border-opacity-10 rounded-lg`}
                            >
                                <View
                                    style={tw`border-b border-[#455560] pb-4 flex flex-row justify-between border-opacity-10`}
                                >
                                    <Text
                                        style={tw`text-base mt-4 pl-6 font-normal text-[#455560]`}
                                    >
                                        Unit Price
                                    </Text>
                                    <Text
                                        style={tw`text-base mt-4 pr-3 font-semibold text-[#455560]`}
                                    >
                                        ₹ {store.orderDetails?.subTotal}
                                    </Text>
                                </View>
                                <View
                                    style={tw`border-b border-[#455560] pb-4 flex flex-row justify-between border-opacity-10`}
                                >
                                    <Text
                                        style={tw`text-base mt-4 pl-6 font-normal text-[#455560]`}
                                    >
                                        Discount
                                    </Text>
                                    <Text
                                        style={tw`text-base mt-4 pr-3 font-semibold text-[#455560]`}
                                    >
                                        ₹ -
                                        {store.orderDetails?.discount ||
                                            store.orderDetails?.cartDiscount}
                                    </Text>
                                </View>
                                <View
                                    style={tw`border-b border-[#455560] pb-4 flex flex-row justify-between border-opacity-10`}
                                >
                                    <Text
                                        style={tw`text-base mt-4 pl-6 font-normal text-[#455560]`}
                                    >
                                        GST
                                    </Text>
                                    <Text
                                        style={tw`text-base mt-4 pr-3 font-semibold text-[#455560]`}
                                    >
                                        ₹ {store.orderDetails?.totalGstValue}
                                    </Text>
                                </View>
                                <View
                                    style={tw`border-b border-[#455560] pb-4 flex flex-row justify-between border-opacity-10`}
                                >
                                    <Text
                                        style={tw`text-base mt-4 pl-6 font-semibold text-[#455560]`}
                                    >
                                        Total Payable
                                    </Text>
                                    <Text
                                        style={tw`text-base mt-4 pr-3 font-semibold text-[#455560]`}
                                    >
                                        ₹ {store.orderDetails?.grandTotal}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default memo(OrderDetails);
