// AUTH_API

export const REQUEST_OTP = '/mobile-auth/request-otp';
export const VERIFY_OTP = '/mobile-auth/verify-otp';
export const REGISTER_USER = '/auth/register';
export const USER_LOGIN = '/mobile-auth/login';
export const FORGOT_PASSWORD_REQUEST_OTP = '/mobile-auth/forget-password-request-otp';
export const STAFF_RESET_PASSWORD = '/admin/staff/reset-password';
export const RESET_PASSWORD = '/auth/reset-password';
export const REGISTER_NEW_USER = '/user/register';
export const REGISTERED_USER_CHANGE_PASSWORD = '/auth/change-password';
export const ORGANIZATION_LIST = '/mobile-auth/organization/list'
export const DELETE_USER_ACCOUNT = '/mobile-auth/delete-account';
export const SET_USER_PASSWORD = '/mobile-auth/set-password';

// GENERAL APIS

export const UPLOAD_IMAGE = '/general/upload-image';
export const GET_STATES = '/general/states';
export const GET_CITY = '/general/get/cityDetails';

// USERS API

export const GET_USER_DETAILS = '/clients/mobile';
export const UPDATE_USER_DETAILS = '/clients/mobile/update';

// FACILITY

export const GET_FACILITY_BY_ORGANIZATION = '/facility/listByOrg';
export const GET_FACILITY_BY_STAFF_ID = '/facility/listByStaffID';
export const FACILITY_LIST_BY_ORGANIZATION_V2 = '/facility/v2/listByOrg'
export const GET_FACILITY_DETAILS = '/facility/details';

// Client
export const GET_CLIENT_LISTING = '/staff/trainer/client/list';
export const GET_CLIENT_DETAILS = '/clients';
export const GET_STAFF_DETAILS = '/admin/staff/details';
export const GET_CLIENT_LIST_BY_STAFF = '/clients/list'

//AVAILABILITY
export const CREATE_TRAINER_AVAILABILITY = '/admin/staff/add/availability';
export const UPDATE_TRAINER_AVAILABILITY = '/admin/staff/update/availability';
export const GET_STAFF_AVAILABILITY_LIST = '/admin/staff/availability/list';
export const GET_STAFF_AVAILABILITY_DETAILS = '/admin/staff/getDetails';
export const DELETE_STAFF_TIME_SLOT = '/admin/staff/delete/availability';

//TRAINERS
export const GET_TRAINERS_LIST_BY_ORGANIZATION = '/admin/staff/trainers/list';
export const GET_STAFF_LISTS_BY_ORGANIZATIONS = '/admin/staff/list';
export const GET_STAFF_DETAILS_BY_ID = '/admin/staff/details';
export const GET_TRAINERS_LIST_BY_SUBTYPE = '/admin/staff/trainers/list/v1';


// payrate 
export const ALL_CLASS_TYPES = '/payRate/allClassType';
export const ALL_SERVICE_CATEGORIES = '/payRate/allServiceCategory';

// bookings

export const SERVICE_CATEGORY_LIST = '/organization/services/list';
export const PRICE_LIST_BY_USER_AND_TYPE = '/pricing/pricingByUserAndType';
export const SERVICE_CATEGORY_LIST_PACKAGE_ID = '/pricing/get-services';
export const ROOM_LIST_BY_SERVICE_CATEGORY = '/rooms/room-list/serviceCategoryId';
export const SERVICE_CATEGORY_LIST_BY_ORGANIZATION = '/organization/list/groupSubTypeByService';
export const PRICING_BY_USER_AND_SUBTYPE = 'pricing/pricingByUserAndSubType'


// Scheduling
export const CREATE_SCHEDULING = '/scheduling';
export const CREATE_BOOKING_SCHEDULING = '/scheduling/booking';
export const UPDATE_BOOKING_SCHEDULING = '/scheduling/booking/update';
export const CREATE_APPOINTMENT_SCHEDULING = '/scheduling/personal-appointment';
export const UPDATE_APPOINTMENT_SCHEDULING =
    '/scheduling/personal-appointment/update';
export const SCHEDULING_LIST = '/scheduling/get/list';
export const SCHEDULING_DETAILS = '/scheduling/get';
export const COURSE_PACKAGE_LIST = '/course/list'
export const CREATE_COURSE_SCHEDULE = '/course/create-schedule';
export const COURSE_SCHEDULING_DETAILS = '/course/scheduling-details';
export const UPDATE_COURSES_SCHEDULE = '/course/update-schedule';
export const COURSE_DETAILS = '/course/details';
export const UPDATE_SCHEDULING = '/scheduling/edit';
export const CANCEL_SCHEDULING = '/scheduling/cancel';
export const CHECK_IN_SCHEDULING = '/scheduling/checkIn';
export const DELETE_SCHEDULING = '/scheduling/delete';
export const SCHEDULING_STAFF_AVAILABILITY_LIST =
    '/scheduling/staff-availability';
export const CANCEL_SCHEDULEING_APPOINTMENT = '/scheduling/cancel';
export const ROOM_LIST_BY_SCHEDULING = '/rooms/list/scheduling';
export const CREATE_CLASSES_SCHEDULING = '/scheduling/classes/create';
export const UPDATE_CLASSES_SCHEDULING = '/scheduling/classes/update';
export const GET_CLASSES_SCHEDULING_DETAILS = '/scheduling/classes';
export const SERVICE_TYPE_BY_PACKAGE = '/organization/list/servicesFromPackage'


// Order lisitng 
export const PURCHASE_ORDER_LISTING = '/purchase/invoice/list';
export const ORDER_DETAILS_BY_ID = '/purchase/invoice';
