export type AuthType = 'email' | 'mobile' | 'google'; // Add 'google' if it's missing

export enum UserRole {
    Admin = 'Admin',
    Trainer = 'trainer',
    WEB_MASTER = 'webMaster',
    Guest = 'Guest',
    User = 'user',
    FRONT_DESK_ADMIN = 'frontDeskAdmin',
    SUPER_ADMIN = 'superAdmin',
    ORGANIZATION = 'organization',
}

export const ClassType = {
    PERSONAL_APPOINTMENT: 'personalAppointment',
    CLASSES: 'classes',
    BOOKING: 'bookings',
    COURSES: 'courses',
};

export const Staff_Roles = {
    trainer: 'TRAINER',
    frontDeskAdmin: 'FRONT DESK ADMIN',
    webMaster: `WEB MASTER`,
} as any;
