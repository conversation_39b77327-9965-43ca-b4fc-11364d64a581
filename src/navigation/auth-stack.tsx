import { createNativeStackNavigator } from '@react-navigation/native-stack';

import {
    FORGOT_PASSWORD,
    LOGIN_SCREEN,
    RESET_PASSWORD_SCREEN,
    SELECT_LOCATION_SCREEN,
    SELECT_ORGANIZATION,
    SIGNUP_SCREEN,
    VERIFICATION_SCREEN,
} from '~/constants/navigation-constant';

import ForgotPasswordScreen from '~/screens/auth-screens/forgot-password-screen';
import LoginScreen from '~/screens/auth-screens/login-screen';
import ResetPasswordScreen from '~/screens/auth-screens/reset-password';
import SelectLocationScreen from '~/screens/auth-screens/select-location-screen';
import SelectOrganizationScreen from '~/screens/auth-screens/select-org-screen';
import SignupScreen from '~/screens/auth-screens/signup-screen';
import VerificationScreen from '~/screens/auth-screens/verification-screen';

const AuthStack = createNativeStackNavigator();

const AuthNavigator = () => (
    <AuthStack.Navigator
        // initialRouteName={SELECT_LOCATION_SCREEN}
        initialRouteName={SELECT_ORGANIZATION}
        screenOptions={{
            headerShown: false,
        }}
    >
        <AuthStack.Screen
            name={SELECT_ORGANIZATION}
            component={SelectOrganizationScreen}
        />
        <AuthStack.Screen name={LOGIN_SCREEN} component={LoginScreen} />
        {/* <AuthStack.Screen
            name={SELECT_LOCATION_SCREEN}
            component={SelectLocationScreen}
        /> */}
        <AuthStack.Screen
            name={VERIFICATION_SCREEN}
            component={VerificationScreen}
        />
        <AuthStack.Screen name={SIGNUP_SCREEN} component={SignupScreen} />
        <AuthStack.Screen
            name={RESET_PASSWORD_SCREEN}
            component={ResetPasswordScreen}
        />
        <AuthStack.Screen
            name={FORGOT_PASSWORD}
            component={ForgotPasswordScreen}
        />
    </AuthStack.Navigator>
);

export default AuthNavigator;
