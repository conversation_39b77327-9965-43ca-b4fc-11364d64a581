import { AuthType } from '~/constants/enums';
import {
    AUTH_STACK,
    DASH<PERSON>ARD_STACK,
    FORGOT_PASSWORD,
    LOGIN_SCREEN,
    MEASUREMENT_SCREEN,
    ONBOARD_STACK,
    PROFILE_SCREEN,
    PRO<PERSON><PERSON>_SETTING,
    RESET_PASSWORD_SCREEN,
    SIGNUP_SCREEN,
    VERIFICATION_SCREEN,
} from '~/constants/navigation-constant';

// navigation-types.ts
export type RootStackParamList = {
    [LOGIN_SCREEN]: undefined;
    [VERIFICATION_SCREEN]: {
        contact: string;
        type: AuthType;
        otp: string;
        forgotPasswordRequest: boolean;
    };
    [SIGNU<PERSON>_SCREEN]: {
        contact: string;
        type?: AuthType;
        otp: string;
        forgotPasswordRequest: boolean;
    };
    [FORGOT_PASSWORD]: {
        contact: string;
    };
    [RESET_PASSWORD_SCREEN]: {
        contact: string;
        type?: AuthType;
        otp: string;
        forgotPasswordRequest: boolean;
    };
    [AUTH_STACK]: undefined;
    [DAS<PERSON><PERSON>ARD_STACK]: undefined;
    [ONBOARD_STACK]: undefined;
    [MEASUREMENT_SCREEN]: undefined;
    [PROFILE_SCREEN]: undefined;
    [PROFILE_SETTING]: undefined;
};
