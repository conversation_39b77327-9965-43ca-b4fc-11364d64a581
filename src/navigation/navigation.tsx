import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useEffect } from 'react';
import { enableScreens } from 'react-native-screens';

import {
    AUTH_STACK,
    BASIC_INFORMATION_SCREEN,
    DASHBOARD_STACK,
    ONBOARD_STACK,
    SIGNUP_SCREEN,
} from '~/constants/navigation-constant';

import AuthNavigator from '~/navigation/auth-stack';
import DashboardNavigator from '~/navigation/dashboard-stack';
import { RootStackParamList } from '~/navigation/navigation-types';
import OnboardNavigator from '~/navigation/onboard-stack';

import { useAppSelector } from '~/hooks/redux-hooks';
import {
    ScreenListener,
    SetScreenName,
    navigationRef,
} from '~/hooks/useLocation';

enableScreens();
const Stack = createNativeStackNavigator<RootStackParamList>();

const Navigation = () => {
    const store = useAppSelector((state) => ({
        isLogin: state.auth_store.isLogin,
        role: state.auth_store.role,
        showOnboardingScreen: state.auth_store.showOnboardingScreen,
    }));

    useEffect(() => {
        // set the screen name in the reducer on the first time of the page load
        SetScreenName();
        ScreenListener();
    }, []);

    const getInitialScreen = () => {
        if (store.isLogin)
            return (
                <Stack.Screen
                    name={DASHBOARD_STACK}
                    component={DashboardNavigator}
                />
            );
        else
            return <Stack.Screen name={AUTH_STACK} component={AuthNavigator} />;
        // return <Stack.Screen name={SIGNUP_SCREEN} component={SignupScreen} />;
    };

    return (
        <NavigationContainer ref={navigationRef}>
            <Stack.Navigator
                screenOptions={{
                    headerShown: false,
                }}
            >
                {getInitialScreen()}
            </Stack.Navigator>
        </NavigationContainer>
    );
};

export default Navigation;
