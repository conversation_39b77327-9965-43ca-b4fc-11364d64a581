import { createNativeStackNavigator } from '@react-navigation/native-stack';

import {
    ACTIVITY_LEVEL,
    BASIC_INFORMATION_SCREEN,
    GENDER_SCREEN,
    GOAL_SCREEN,
    HEIGHT_SCREEN,
    MEASUREMENT_SCREEN,
    WEIGHT_SCREEN,
} from '~/constants/navigation-constant';

import ActivityLevel from '~/screens/onboard-screens/activity-level';
import basicInformation from '~/screens/onboard-screens/basic-information';
import GenderScreen from '~/screens/onboard-screens/gender-screen';
import GoalScreen from '~/screens/onboard-screens/goal-screen';
import HeightScreen from '~/screens/onboard-screens/height-screen';
import MeasurementScreen from '~/screens/onboard-screens/measurement-screen';
import WeightScreen from '~/screens/onboard-screens/weight-screen';

const OnboardStack = createNativeStackNavigator();

const OnboardNavigator = () => {
    return (
        <OnboardStack.Navigator
            initialRouteName={GENDER_SCREEN}
            // initialRouteName={MEASUREMENT_SCREEN}
            screenOptions={{
                headerShown: false,
            }}
        >
            <OnboardStack.Screen
                name={GENDER_SCREEN}
                component={GenderScreen}
            />
            <OnboardStack.Screen
                name={WEIGHT_SCREEN}
                component={WeightScreen}
            />
            <OnboardStack.Screen
                name={HEIGHT_SCREEN}
                component={HeightScreen}
            />
            <OnboardStack.Screen name={GOAL_SCREEN} component={GoalScreen} />
            <OnboardStack.Screen
                name={ACTIVITY_LEVEL}
                component={ActivityLevel}
            />
            <OnboardStack.Screen
                name={MEASUREMENT_SCREEN}
                component={MeasurementScreen}
            />
            {/* <OnboardStack.Screen
                name={BASIC_INFORMATION_SCREEN}
                component={basicInformation}
            /> */}
        </OnboardStack.Navigator>
    );
};

export default OnboardNavigator;
