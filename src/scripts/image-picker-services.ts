// imagePickerHelper.ts
import ImagePicker, { Image, Options } from 'react-native-image-crop-picker';

export const openCamera = async (options: Options): Promise<Image> => {
    try {
        const image: Image = await ImagePicker.openCamera(options);
        return image;
    } catch (error) {
        console.error('ImagePicker Error: ', error);
        throw error;
    }
};

export const openGallery = async (options: Options): Promise<Image> => {
    try {
        const image: Image = await ImagePicker.openPicker(options);
        return image;
    } catch (error) {
        console.error('ImagePicker Error: ', error);
        throw error;
    }
};
