export const cleanUpSchedule = (schedule, availabilityType) => {
    const cleanedSchedule = {};
    let errorMessage = '';

    // Iterate over each day in the schedule
    Object.keys(schedule).forEach((day) => {
        cleanedSchedule[day] = schedule[day]
            .map(({ from, to, payRateIds, selectedServiceCategories }) => {
                // Error handling for missing fields
                if (!from || !to) {
                    errorMessage = `Time range (from/to) is missing for ${day}`;
                }
                if (availabilityType !== 'unavailable') {
                    if (
                        !selectedServiceCategories ||
                        selectedServiceCategories.length === 0
                    ) {
                        errorMessage = `Service Category is missing for ${day}`;
                    }
                }

                // Return only necessary fields
                return { from, to, payRateIds };
            })
            .filter(
                (shift) => shift.from && shift.to && shift.payRateIds.length > 0
            ); // Ensure valid shifts
    });

    // If there's an error, throw it
    if (errorMessage) {
        throw new Error(errorMessage);
    }

    return cleanedSchedule;
};

export const formatTime = (utcDateString) => {
    const date = new Date(utcDateString);

    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        // timeZone: 'Asia/Kolkata',
    });
};

export const capitalizeFirstLetter = (string) => {
    return string
        ?.split(' ')
        ?.map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(' ');
};

export const serviceTypes = {
    personalAppointment: 'appointment',
    classes: 'classes',
    bookings: 'booking',
    courses: 'course',
};

export const serviceTypesCaps = {
    personalAppointment: 'Appointment',
    classes: 'Classes',
    bookings: 'Booking',
    courses: 'Course',
};

export function getRoundedNext30Min(date = new Date()) {
    const result = new Date(date);
    const minutes = result.getMinutes();
    if (minutes < 30) {
        result.setMinutes(30, 0, 0);
    } else {
        result.setHours(result.getHours() + 1);
        result.setMinutes(0, 0, 0);
    }
    return result;
}
