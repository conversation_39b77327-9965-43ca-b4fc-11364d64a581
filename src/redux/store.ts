import AsyncStorage from '@react-native-async-storage/async-storage';
import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import authSlice from '~/redux/slices/auth-slice';
import availabilitySlice from '~/redux/slices/availability-slice';
import bookingSlice from '~/redux/slices/booking-slice';
import facilitySlice from '~/redux/slices/facility-slice';
import layoutSlice from '~/redux/slices/layout-slice';
import orderSlice from '~/redux/slices/order-slice';
import profileSlice from '~/redux/slices/profile-slice';
import schedulingSlice from '~/redux/slices/scheduling-slice';
import staffSlice from '~/redux/slices/staff-slice';

import { APP_MODE } from '~/env';

import clientSlice from './slices/client-slice';

const persistConfig = {
    key: 'root',
    storage: AsyncStorage,
    whitelist: [
        'auth_store',
        'layout_store',
        'profile_store',
        'facility_store',
    ],
};

const reducers = combineReducers({
    auth_store: authSlice,
    layout_store: layoutSlice,
    profile_store: profileSlice,
    client_store: clientSlice,
    facility_store: facilitySlice,
    scheduling_store: schedulingSlice,
    staff_store: staffSlice,
    availability_store: availabilitySlice,
    booking_store: bookingSlice,
    order_store: orderSlice,
});

const persistedReducer = persistReducer(persistConfig, reducers);

const store = configureStore({
    reducer: persistedReducer,
    devTools: APP_MODE === 'development',
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            thunk: true,
            serializableCheck: false,
            immutableCheck: false,
        }),
});

export const persistor = persistStore(store);

export default store;

export type RootState = ReturnType<typeof reducers>;
export type AppDispatch = typeof store.dispatch;
