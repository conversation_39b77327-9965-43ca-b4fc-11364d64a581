import { createSlice } from '@reduxjs/toolkit';
import { Logout } from '~/redux/actions/auth-actions';
import {
    CityList,
    GetState,
    GetUserDetails,
    UpdateUserDetails,
} from '~/redux/actions/profile-actions';

const initialState = {
    personalDetails: {
        firstName: '',
        facilityId: '',
        lastName: '',
        dob: '',
        gender: null,
        email: '',
        mobile: '',
        emergencyContactPerson: '',
        emergencyContactPhone: '',
        policies: [],
        photo: '',
        address: {
            addressLine1: '',
            addressLine2: '',
            postalCode: '',
            city: '',
            state: '',
            country: '',
        },
        activityLevel: '',
    },
    allStates: [],
    cityList: [],
};

const profileSlice = createSlice({
    name: 'profileSlice',
    initialState,
    reducers: {
        SetUserDetails: (state, { payload }) => {
            return {
                personalDetails: {
                    ...state.personalDetails,
                    [payload.key]: payload.value,
                },
            };
        },
        UpdateUserDetails: (state, { payload }) => {},
    },
    extraReducers: (builder) => {
        builder
            .addCase(GetUserDetails.fulfilled, (state, { payload }) => {
                state.personalDetails = {
                    ...state.personalDetails,
                    ...payload.res?.data.data,
                };
            })
            .addCase(GetState.fulfilled, (state, { payload }) => {
                state.allStates = payload.res?.data.data;
            })
            .addCase(Logout.fulfilled, (state, { payload }) => {
                return initialState;
            });
    },
});

export const { SetUserDetails } = profileSlice.actions;

export default profileSlice.reducer;
