import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    screenName: '',
    states: [],
    facilityLists: [],
};

const layoutSlice = createSlice({
    name: 'layoutSlice',
    initialState,
    reducers: {
        SetScreenNameRedux: (state, { payload }) => {
            state.screenName = payload.screenName;
        },
    },
    extraReducers: (builder) => {},
});

export const { SetScreenNameRedux } = layoutSlice.actions;

export default layoutSlice.reducer;
