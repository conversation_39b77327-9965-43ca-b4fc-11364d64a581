import { createSlice } from '@reduxjs/toolkit';

import {
    GetClientDetails,
    GetClientListing,
    GetClientListingByRole,
    GetStaffDetails,
} from '../actions/client-action';

interface clientProps {
    clientList: any;
    clientListCount: number;
    clientDetails: any;
    staffDetails: any;
    clientListByStaff: any;
    clientListCountByStaff: number;
}

const initialState: clientProps = {
    clientList: [],
    clientListCount: 0,
    clientListByStaff: [],
    clientListCountByStaff: 0,
    clientDetails: null,
    staffDetails: null,
};

const clientSlice = createSlice({
    name: 'layoutSlice',
    initialState,
    reducers: {
        // SetScreenNameRedux: (state, { payload }) => {
        //     state.screenName = payload.screenName;
        // },
    },
    extraReducers: (builder) => {
        builder
            .addCase(GetClientListing.fulfilled, (state, { payload }) => {
                // console.log('----------clientList', payload);
                state.clientList = payload?.data?.list;
                state.clientListCount = payload?.data?.count;
            })
            .addCase(GetClientListingByRole.fulfilled, (state, { payload }) => {
                console.log('----------clientList', payload);
                state.clientListByStaff = payload?.data?.data?.list;
                state.clientListCountByStaff = payload?.data?.data?.count;
            })
            .addCase(GetClientDetails.fulfilled, (state, { payload }) => {
                // console.log('----------clientDEtails', payload);
                state.clientDetails = payload?.data?.data;
            })
            .addCase(GetStaffDetails.fulfilled, (state, { payload }) => {
                // console.log('---------- StaffDEtails', payload);
                state.staffDetails = payload?.data?.data;
            });
    },
});

// export const { SetScreenNameRedux } = clientSlice.actions;

export default clientSlice.reducer;
