import { createSlice } from '@reduxjs/toolkit';
import {
    GetStaffListByRole,
    GetStaffListBySubType,
    StaffGetStaffDetails,
} from '~/redux/actions/staff-actions';

// types/staff.types.ts

export interface StaffDetails {
    personalInfo: any;
    additionalInfo: any;
    facilityInfo: any;
}

export interface StaffState {
    staffLists: any[];
    staffListCount: any;
    staffDetails: StaffDetails;
    staffListBySubType: any[];
}

// Initial state for the slice

const initialState: StaffState = {
    staffLists: [],
    staffListCount: null,
    staffDetails: {
        personalInfo: {},
        additionalInfo: {},
        facilityInfo: {},
    },
    staffListBySubType: [],
};

// Create the slice
const staffSlice = createSlice({
    name: 'staffSlice',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(GetStaffListByRole.fulfilled, (state, { payload }) => {
                state.staffLists = payload.res?.data.data;
                state.staffListCount = payload.res?.data?.pagination;
            })
            .addCase(StaffGetStaffDetails.fulfilled, (state, { payload }) => {
                console.log('Payload----------------', payload);
                state.staffDetails = payload.res?.data.data;
            })
            .addCase(GetStaffListBySubType.fulfilled, (state, { payload }) => {
                console.log('Payload----------------', payload);
                state.staffListBySubType = payload.res?.data?.data;
            });
    },
});

export const {} = staffSlice.actions;

// Export the reducer to be used in the store
export default staffSlice.reducer;
