import { createSlice } from '@reduxjs/toolkit';
import { GetTrainersListByOrganization } from '~/redux/actions/common-actions';
import {
    BookedCalendarData,
    GetStaffAvailabilityDetails,
    GetStaffAvailabilityList,
    ManipulateCalendarData,
} from '~/redux/actions/scheduling-actions';

interface GetStaffAvailabilityIniTialState {
    availabilitySlots: any;
    availabilityDetails: any;
    selectedTrainer: {
        firstName: undefined;
        lastName: undefined;
        userId: undefined;
    };
    trainersListByOrganization: any;
    calendarSchedulingList: Array<any>;
    bookingType: string;
}

const initialState: GetStaffAvailabilityIniTialState = {
    availabilitySlots: [],
    availabilityDetails: {},
    selectedTrainer: {
        firstName: undefined,
        lastName: undefined,
        userId: undefined,
    }, //
    trainersListByOrganization: [],
    calendarSchedulingList: [],
    bookingType: '',
};

const schedulingSlice = createSlice({
    name: 'schedulingSlice',
    initialState,
    reducers: {
        SelectTrainer: (state, { payload }) => {
            state.selectedTrainer = payload;
        },
        SetBookingType: (state, { payload }) => {
            state.bookingType = payload;
        },
        ClearBookingType: (state) => {
            state.bookingType = '';
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(
                GetStaffAvailabilityList.fulfilled,
                (state, { payload }) => {
                    state.availabilitySlots = payload.res?.data.data;
                }
            )
            .addCase(
                GetStaffAvailabilityDetails.fulfilled,
                (state, { payload }) => {
                    state.availabilityDetails = payload?.res?.data?.data;
                }
            )
            .addCase(
                GetTrainersListByOrganization.fulfilled,
                (state, { payload }) => {
                    state.trainersListByOrganization = payload?.res?.data.data;
                    if (!state.selectedTrainer.userId) {
                        state.selectedTrainer =
                            state.trainersListByOrganization?.[0];
                    }
                }
            )
            .addCase(BookedCalendarData.fulfilled, (state, { payload }) => {
                state.calendarSchedulingList = payload?.data?.data.map(
                    (item: any) => ManipulateCalendarData(item)
                );
            });
    },
});

export const { SelectTrainer, SetBookingType, ClearBookingType } =
    schedulingSlice.actions; // Export actions if defined
export default schedulingSlice.reducer;
