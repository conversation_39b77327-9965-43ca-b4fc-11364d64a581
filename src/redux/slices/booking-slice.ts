import { createSlice } from '@reduxjs/toolkit';
import {
    GetStaffListByRole,
    StaffGetStaffDetails,
} from '~/redux/actions/staff-actions';

import {
    PricingByUserAndSubType,
    PricingListingByUserAndType,
    ServiceCategoryList,
    ServiceCategoryListByPackageId,
    activeServiceCategoyListPricing,
    courseListForPackage,
    roomListingByScheduling,
    roomListingByServiceCategory,
    serviceCategorybyOrganization,
} from '../actions/booking-action';

// Initial state for the slice

interface InitialState {
    serviceCategoryData: any[];
    serviceCategoryDataCount: number;
    pricingListByUserByType: any;
    servicecategoryByPricingList?: any;
    roomListByServiceCategory?: any;
    servicecategoryByOrganization?: any;
    servicecategoryByOrganizationCount: number;
    pricingByUserAndSubType: any;
    roomListByScheduling: any;
    courseListForPackage: any;
    courseListForPackageCount: number;
    serviceCategoryByPackage: any;
    serviceCategoryByPackageCount: number;
}

const initialState: InitialState = {
    serviceCategoryData: [],
    serviceCategoryDataCount: 0,
    pricingListByUserByType: [],
    servicecategoryByPricingList: [],
    roomListByServiceCategory: [],
    servicecategoryByOrganization: [],
    servicecategoryByOrganizationCount: 0,
    pricingByUserAndSubType: [],
    roomListByScheduling: [],
    courseListForPackage: [],
    courseListForPackageCount: 0,
    serviceCategoryByPackage: [],
    serviceCategoryByPackageCount: 0,
};

// Create the slice
const bookingSlice = createSlice({
    name: 'bookingSlice',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(ServiceCategoryList.fulfilled, (state, { payload }) => {
                console.log('Payload--------------', payload);
                state.serviceCategoryData = payload?.data.data?.list;
                state.serviceCategoryDataCount = payload?.data.data?.count;
            })
            .addCase(
                PricingListingByUserAndType.fulfilled,
                (state, { payload }) => {
                    console.log(
                        'Pricing listing by user and type payload--------',
                        payload
                    );
                    state.pricingListByUserByType = payload?.data?.data;
                }
            )
            .addCase(
                ServiceCategoryListByPackageId.fulfilled,
                (state, { payload }) => {
                    // console.log(
                    //     'ServiceCategoryListByPackageId Pricing listing--------',
                    //     payload
                    // );
                    state.servicecategoryByPricingList = payload?.data?.data;
                }
            )
            .addCase(
                serviceCategorybyOrganization.fulfilled,
                (state, { payload }) => {
                    // console.log(
                    //     'ServiceCategoryListByorganization----vxdvxcv----',
                    //     payload
                    // );
                    state.servicecategoryByOrganization =
                        payload?.data?.data?.list;
                    state.servicecategoryByOrganizationCount =
                        payload?.data?.data?.list;
                }
            )
            .addCase(
                activeServiceCategoyListPricing.fulfilled,
                (state, { payload }) => {
                    console.log(
                        'ServiceCategoryListBypacahe----vxdvxcv----',
                        payload
                    );
                    state.serviceCategoryByPackage = payload?.data?.data?.list;
                }
            )
            .addCase(
                roomListingByServiceCategory.fulfilled,
                (state, { payload }) => {
                    state.roomListByServiceCategory = payload?.data || [];
                }
            )
            .addCase(
                PricingByUserAndSubType.fulfilled,
                (state, { payload }) => {
                    state.pricingByUserAndSubType = payload?.data?.data;
                }
            )
            .addCase(
                roomListingByScheduling.fulfilled,
                (state, { payload }) => {
                    state.roomListByScheduling = payload?.data?.data;
                }
            )
            .addCase(courseListForPackage.fulfilled, (state, { payload }) => {
                console.log('courseListForPackage ---------------', payload);
                state.courseListForPackage = payload?.data?.list;
                state.courseListForPackageCount = payload?.data?.count;
            });
    },
});

export const {} = bookingSlice.actions;

// Export the reducer to be used in the store
export default bookingSlice.reducer;
