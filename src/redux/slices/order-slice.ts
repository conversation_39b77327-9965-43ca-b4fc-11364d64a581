import { createSlice } from '@reduxjs/toolkit';

import { GetOrderListing, OrderInvoiceDetails } from '../actions/order-action';

interface orderProps {
    orderList: any;
    orderListCount: number;
    orderDetails: any;
}

const initialState: orderProps = {
    orderList: [],
    orderListCount: 0,
    orderDetails: null,
};

const orderSlice = createSlice({
    name: 'orderSlice',
    initialState,
    reducers: {
        // SetScreenNameRedux: (state, { payload }) => {
        //     state.screenName = payload.screenName;
        // },
    },
    extraReducers: (builder) => {
        builder.addCase(GetOrderListing.fulfilled, (state, { payload }) => {
            state.orderList = payload?.data?.data;
            state.orderListCount = payload?.data?.totalCount;
        });
        builder.addCase(OrderInvoiceDetails.fulfilled, (state, { payload }) => {
            console.log('----------paylaod order details------', payload);
            state.orderDetails = payload?.data?.data;
        });
    },
});

// export const { SetScreenNameRedux } = clientSlice.actions;

export default orderSlice.reducer;
