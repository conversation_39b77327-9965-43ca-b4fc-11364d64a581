import { createSlice } from '@reduxjs/toolkit';
import {
    GetStaffListByRole,
    StaffGetStaffDetails,
} from '~/redux/actions/staff-actions';

import {
    GetFacilitiesByStaffId,
    getAllClassTypeByStaffId,
    getAllServiceCategories,
} from '../actions/availability-action';

interface AvailabilityTypes {
    serviceType: [];
    faclilityListByStaffId: [];
    serviceCategoryList: [];
}

// Initial state for the slice
const initialState: AvailabilityTypes = {
    serviceType: [],
    faclilityListByStaffId: [],
    serviceCategoryList: [],
};

// Create the slice
const availabilitySlice = createSlice({
    name: 'availabilitySlice',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(
            getAllClassTypeByStaffId.fulfilled,
            (state, { payload }) => {
                console.log('Payload--------------', payload);
                state.serviceType = payload?.data?.data;
            }
        );
        builder.addCase(
            GetFacilitiesByStaffId.fulfilled,
            (state, { payload }) => {
                state.faclilityListByStaffId = payload.data?.data;
            }
        );
        builder.addCase(
            getAllServiceCategories.fulfilled,
            (state, { payload }) => {
                console.log('Payload-- dsdgdgdfg------------', payload);
                state.serviceCategoryList = payload.data || [];
            }
        );
    },
});

export const {} = availabilitySlice.actions;

// Export the reducer to be used in the store
export default availabilitySlice.reducer;
