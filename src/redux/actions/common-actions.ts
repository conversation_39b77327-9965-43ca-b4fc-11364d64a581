import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    GET_TRAINERS_LIST_BY_ORGANIZATION,
    UPLOAD_IMAGE,
} from '~/constants/api-constant';

// Adjust the import path as necessary
import { handleApiError, postApi } from '~/scripts/api-services';

export const UploadImage = createAsyncThunk(
    'general/UploadImage',
    async ({ image }, { rejectWithValue }) => {
        try {
            const formData = new FormData();
            const file = {
                uri: image.path,
                type: image.mime,
                name: image.path.split('/').pop(),
                lastModified: image.modificationDate
                    ? new Date(parseInt(image.modificationDate)).toISOString()
                    : new Date().toISOString(),
            };

            console.log(image);
            console.log(file);
            formData.append('image', file);

            const response = await postApi(UPLOAD_IMAGE, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            return { response }; // Assuming the response contains the data you need
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const GetTrainersListByOrganization = createAsyncThunk(
    'common/GetTrainersListByOrganization',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(GET_TRAINERS_LIST_BY_ORGANIZATION, {
                ...payload,
                // isActive: true,
            });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);
