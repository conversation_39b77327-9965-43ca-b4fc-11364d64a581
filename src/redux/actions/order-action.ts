import { createAsyncThunk } from '@reduxjs/toolkit';
import <PERSON>NFetchBlob from 'rn-fetch-blob';

import {
    DOWNLOAD_INVOICE_DETAILS,
    ORDER_DETAILS_BY_ID,
    PURCHASE_ORDER_LISTING,
} from '~/constants/api-constant';

import { getApi, handleApiError, postApi } from '~/scripts/api-services';
import Alertify from '~/scripts/toast';

import { ProjectUrl } from '~/env';

export const GetOrderListing = createAsyncThunk(
    'client/GetOrderListing',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(PURCHASE_ORDER_LISTING, {
                ...payload,
            });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*------------------- Order iNvoive Details ------------------ */

export const OrderInvoiceDetails: any = createAsyncThunk(
    'OrderInvoiceDetails',
    async ({ orderId }: any) => {
        try {
            const response = await getApi(`${ORDER_DETAILS_BY_ID}/${orderId}`);
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Download Invoice ------------------ */

export const DownloadInvoice = createAsyncThunk(
    'DownloadInvoice',
    async ({ orderId }: { orderId: string }) => {
        try {
            // Build full URL safely (remove extra slashes)
            const base = ProjectUrl.replace(/\/$/, '');
            const endpoint = DOWNLOAD_INVOICE_DETAILS.replace(/^\//, '');
            const url = `${base}/${endpoint}/${orderId}/download`;
            console.log('Downloading from URL:', url);

            const dirs = RNFetchBlob.fs.dirs;
            const filePath = `${
                dirs.DownloadDir || dirs.DocumentDir
            }/invoice_${orderId}.pdf`;

            // RNFetchBlob handles the stream directly
            const res = await RNFetchBlob.config({
                path: filePath,
                fileCache: true,
                appendExt: 'pdf',
            }).fetch('GET', url, { Accept: 'application/pdf' });

            return res.path();
        } catch (error) {
            console.log('Error in DownloadInvoice API:', error);
            throw error;
        }
    }
);
