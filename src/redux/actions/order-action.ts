import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    ORDER_DETAILS_BY_ID,
    PURCHASE_ORDER_LISTING,
} from '~/constants/api-constant';

import { getApi, handleApiError, postApi } from '~/scripts/api-services';
import Alertify from '~/scripts/toast';

export const GetOrderListing = createAsyncThunk(
    'client/GetOrderListing',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(PURCHASE_ORDER_LISTING, {
                ...payload,
            });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*------------------- Order iNvoive Details ------------------ */

export const OrderInvoiceDetails: any = createAsyncThunk(
    'OrderInvoiceDetails',
    async ({ orderId }: any) => {
        try {
            const response = await getApi(`${ORDER_DETAILS_BY_ID}/${orderId}`);
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
