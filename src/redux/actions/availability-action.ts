import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    ALL_CLASS_TYPES,
    ALL_SERVICE_CATEGORIES,
    GET_FACILITY_BY_STAFF_ID,
} from '~/constants/api-constant';

import { getApi, handleApiError, postApi } from '~/scripts/api-services';

export const getAllClassTypeByStaffId = createAsyncThunk(
    'payRate/getAllClassTypeByStaffId',
    async ({ staffId }: any, { rejectWithValue }) => {
        try {
            const response = await getApi(`${ALL_CLASS_TYPES}/${staffId}`);
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch pay rate detail'
            );
        }
    }
);

export const getAllServiceCategories = createAsyncThunk(
    'payRate/getAllServiceCategories',
    async ({ staffId, data = {} }: any, { rejectWithValue }) => {
        try {
            const response = await postApi(
                `${ALL_SERVICE_CATEGORIES}/${staffId}`,
                data
            );
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch pay rate detail'
            );
        }
    }
);

export const GetFacilitiesByStaffId = createAsyncThunk(
    'availability/GetFacilitiesByStaffId',
    async ({ userId }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(GET_FACILITY_BY_STAFF_ID, {
                staffId: userId,
            });
            return res;
        } catch (error) {
            return handleApiError(error);
        }
    }
);
