import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    ALL_CLASS_TYPES,
    ALL_SERVICE_CATEGORIES,
    GET_FACILITY_BY_ORGANIZATION,
    GET_FACILITY_BY_STAFF_ID,
    GET_FACILITY_DETAILS,
} from '~/constants/api-constant';

import { getApi, handleApiError, postApi } from '~/scripts/api-services';

export const GetAllFacilitiesByOrganization = createAsyncThunk(
    'organization/GetAllFacilitiesByOrganization',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const organizationId = '66cea29781ee8034250895aa';
            const res = await postApi(
                `${GET_FACILITY_BY_ORGANIZATION}/${organizationId}`,
                { ...payload }
            );
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const GetAllFacilitiesByOrganizationV2 = createAsyncThunk(
    'organization/GetAllFacilitiesByOrganizationV2',
    async (
        { ...payload }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const organizationId = getState().auth_store.organizationId;
            const res = await postApi(
                `${GET_FACILITY_BY_ORGANIZATION}/${organizationId}`,
                { ...payload }
            );
            return res;
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const GetAllFacilitiesByStaffId = createAsyncThunk(
    'facility/GetAllFacilitiesByStaffId',
    async ({ userId }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            // const { userId } = getState().auth_store;
            const res = await postApi(GET_FACILITY_BY_STAFF_ID, {
                staffId: userId,
            });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

/*------------------- Facility Details ------------------ */

export const FacilityDetails: any = createAsyncThunk(
    'FacilityDetails',
    async ({ facilityId }: any) => {
        try {
            const response = await postApi(`${GET_FACILITY_DETAILS}`, {
                facilityId,
            });
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

export const getAllClassTypeByStaffId = createAsyncThunk(
    'payRate/getAllClassTypeByStaffId',
    async ({ staffId }: any, { rejectWithValue }) => {
        try {
            const response = await getApi(`${ALL_CLASS_TYPES}/${staffId}`);
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch pay rate detail'
            );
        }
    }
);

export const getAllServiceCategories = createAsyncThunk(
    'payRate/getAllServiceCategories',
    async ({ staffId, data = {} }: any, { rejectWithValue }) => {
        try {
            const response = await postApi(
                `${ALL_SERVICE_CATEGORIES}/${staffId}`,
                data
            );
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch pay rate detail'
            );
        }
    }
);
