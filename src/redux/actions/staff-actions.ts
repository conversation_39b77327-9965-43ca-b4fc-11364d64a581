import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    GET_STAFF_DETAILS_BY_ID,
    GET_STAFF_LISTS_BY_ORGANIZATIONS,
    GET_TRAINERS_LIST_BY_SUBTYPE,
} from '~/constants/api-constant';

import { getApi, handleApiError, postApi } from '~/scripts/api-services';

export const GetStaffListByRole = createAsyncThunk(
    'staff/GetStaffListByRole',
    async (
        { search = '', page = 1, pageSize = 10, role = undefined }: any,
        { dispatch, getState, rejectWithValue }
    ) => {
        try {
            const res = await postApi(GET_STAFF_LISTS_BY_ORGANIZATIONS, {
                search,
                page,
                pageSize,
                role: role?.length > 0 ? role : undefined,
            });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const StaffGetStaffDetails = createAsyncThunk(
    'staff/GetStaffDetails',
    async (
        { userId }: { userId: string },
        { dispatch, getState, rejectWithValue }
    ) => {
        try {
            const res = await getApi(`${GET_STAFF_DETAILS_BY_ID}/${userId}`);
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

/*--------------------- Staff List by syb type and start date and endDate ----------------------- */

export const GetStaffListBySubType = createAsyncThunk(
    'staff/GetStaffListBySubType',
    async ({ payload }: any, { dispatch, getState, rejectWithValue }: any) => {
        try {
            const res = await postApi(GET_TRAINERS_LIST_BY_SUBTYPE, {
                ...payload,
            });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);
