import { createAsyncThunk } from '@reduxjs/toolkit';
import { GetUserDetails } from '~/redux/actions/profile-actions';
import {
    SetOnboardingData,
    ToggleOnboardingStack,
} from '~/redux/slices/auth-slice';
import { RootState } from '~/redux/store';

import {
    DELETE_USER_ACCOUNT,
    FORGOT_PASSWORD_REQUEST_OTP,
    GET_FACILITY_BY_ORGANIZATION,
    ORGANIZATION_LIST,
    REGISTERED_USER_CHANGE_PASSWORD,
    REGISTER_NEW_USER,
    REGISTER_USER,
    REQUEST_OTP,
    RESET_PASSWORD,
    SET_USER_PASSWORD,
    STAFF_RESET_PASSWORD,
    USER_LOGIN,
    VERIFY_OTP,
} from '~/constants/api-constant';
import { AuthType } from '~/constants/enums';

import { handleApiError, patchApi, postApi } from '~/scripts/api-services';
import Alertify from '~/scripts/toast';

interface SendOtpPayload {
    type: AuthType;
    email?: string;
    mobile?: string;
}

interface VerifyOtpPayload {
    type: AuthType;
    email?: string;
    mobile?: string;
    forgotPasswordRequest: boolean;
    otp: number;
}

interface RegisterUserPayload {
    type: AuthType;
    name: string;
    email: string;
    mobile: string;
    password: string;
    confirmPassword: string;
    otpVerificationCode: string;
}

interface LoginPayload {
    type: string;
    email?: string;
    mobile?: string;
    password: string;
}

interface ResetPasswordPayload {
    type?: string;
    email?: string;
    mobile?: string;
    password?: string;
    confirmPassword?: string;
    otpVerificationCode?: string;
}

export const RequestOtpLoginRegistration = createAsyncThunk(
    'auth/RequestOtpLoginRegistration',
    async (
        { type = 'email', email, mobile }: SendOtpPayload,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const { selectedOrganization } = getState().auth_store;
            const res = await postApi(REQUEST_OTP, {
                type,
                email,
                mobile,
                organizationId: selectedOrganization.organizationId,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const VerifyOtp = createAsyncThunk(
    'auth/VerifyOtp',
    async (
        { forgotPasswordRequest, ...payload }: VerifyOtpPayload,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const { selectedOrganization } = getState().auth_store;
            const res = await postApi(VERIFY_OTP, {
                forgotPasswordRequest,
                ...payload,
                organizationId: selectedOrganization.organizationId,
            });
            return { res, forgotPasswordRequest };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const RegisterUser = createAsyncThunk(
    'auth/RegisterUser',
    async (
        { ...payload }: RegisterUserPayload,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const { facility } = getState().auth_store;
            const res = await postApi(REGISTER_USER, {
                ...payload,
                facilityId: facility._id,
                organizationId: facility.organizationId,
            });
            Alertify.success('Successfully registered');
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UserLogin = createAsyncThunk(
    'auth/UserLogin',
    async (
        { ...payload }: LoginPayload,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const { selectedOrganization } = getState().auth_store;
            const res = await postApi(USER_LOGIN, {
                ...payload,
                organizationId: selectedOrganization.organizationId,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const ForgotPasswordRequestOtp = createAsyncThunk(
    'auth/ForgotPassword',
    async (
        { ...payload }: SendOtpPayload,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const { selectedOrganization } = getState().auth_store;
            const res = await postApi(FORGOT_PASSWORD_REQUEST_OTP, {
                ...payload,
                organizationId: selectedOrganization.organizationId,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const RegisteredUserChangePassword = createAsyncThunk(
    'auth/change-password',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(REGISTERED_USER_CHANGE_PASSWORD, {
                ...payload,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const StaffResetPassword = createAsyncThunk(
    'stafff/reset-password/',
    async (
        { ...payload }: SendOtpPayload,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const clientId = getState().auth_store.userId;
            const res = await patchApi(`${STAFF_RESET_PASSWORD}/${clientId}`, {
                ...payload,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const ResetPassword = createAsyncThunk(
    'auth/ResetPassword',
    async (
        { ...payload }: ResetPasswordPayload,
        { dispatch, getState, rejectWithValue }
    ) => {
        try {
            const state = getState() as RootState;
            const { otpVerificationCode } = state.auth_store;
            const res = await postApi(RESET_PASSWORD, {
                ...payload,
                otpVerificationCode,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const OnboardUserData = createAsyncThunk(
    'auth/OnboardUserData',
    async (
        { _arg, measurement }: any,
        { dispatch, getState, rejectWithValue }
    ) => {
        try {
            dispatch(SetOnboardingData({ measurement }));
            const state = getState() as RootState;
            const { onboardingData } = state.auth_store;
            const res = await postApi(REGISTER_NEW_USER, {
                fitnessProfile: { ...onboardingData },
            });
            dispatch(ToggleOnboardingStack({ state: false }));
            Alertify.success('Onboarding successfully completed');
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const Logout = createAsyncThunk(
    'auth/LogoutUser',
    async (_, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = {};
            return { res };
        } catch (error) {
            console.error('Error in auth/LogoutUser', error);
        }
    }
);

/* -------------------------- Organization List -------------------------- */

export const organizationList = createAsyncThunk(
    'auth/organizationList',
    async ({ payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(ORGANIZATION_LIST, { ...payload });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const DeleteAccount: any = createAsyncThunk(
    'delete-account',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(DELETE_USER_ACCOUNT, reqData);
            Alertify.success(
                response?.data?.message || 'Delete account request sent.'
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------------- Set Password -------------------------- */

export const SetPassword = createAsyncThunk(
    'auth/SetPassword',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const state = getState() as RootState;
            const res = await postApi(SET_USER_PASSWORD, {
                ...payload,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
