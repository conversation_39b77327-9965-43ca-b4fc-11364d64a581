import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    GET_CITY,
    GET_STATES,
    GET_USER_DETAILS,
    UPDATE_USER_DETAILS,
} from '~/constants/api-constant';

import {
    getApi,
    handleApiError,
    patchApi,
    postApi,
} from '~/scripts/api-services';
import Alertify from '~/scripts/toast';

export const GetUserDetails = createAsyncThunk(
    'profile/GetUserDetails',
    async ({ _ }: any, { dispatch, getState, rejectWithValue }: any) => {
        try {
            const { userId } = getState().auth_store;
            const res = await getApi(`${GET_USER_DETAILS}/${userId}`);
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateUserDetails = createAsyncThunk(
    'profile/UpdateUserDetails',
    async (
        { values, gender, date, stateId, cityId }: any,
        { dispatch, getState }: any
    ) => {
        try {
            const {
                addressLine1,
                addressLine2,
                postalCode,
                city,
                state,
                country,
                ...rest
            } = values;
            const { photo, activityLevel } =
                getState().profile_store.personalDetails;
            const { organizationId } = getState().auth_store;
            const res = await patchApi(`${UPDATE_USER_DETAILS}`, {
                ...rest,
                address: {
                    addressLine1,
                    addressLine2,
                    postalCode: Number(postalCode),
                    city: cityId,
                    state: stateId,
                    country: 'India',
                },
                gender,
                dob: date,
                photo: photo || undefined,
                activityLevel: activityLevel || undefined,
                organizationId,
            });
            Alertify.success('Details update successfully');
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const GetState = createAsyncThunk(
    'profile/GetState',
    async (
        { search = '', page = 1, pageSize = 30 }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const res = await postApi(GET_STATES, { search, page, pageSize });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const CityList: any = createAsyncThunk(
    'city-list',
    async (
        { page = 1, pageSize = 50, stateId, search, cityId }: any,
        { getState }: any
    ) => {
        try {
            const response = await postApi(`${GET_CITY}`, {
                page,
                pageSize,
                search,
                stateId,
                cityId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
