import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    GET_CLIENT_DETAILS,
    GET_CLIENT_LISTING,
    GET_CLIENT_LIST_BY_STAFF,
    GET_STAFF_DETAILS,
} from '~/constants/api-constant';

import { getApi, handleApiError, postApi } from '~/scripts/api-services';

export const GetClientListing = createAsyncThunk(
    'client/GetClientListing',
    async ({ ...payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(GET_CLIENT_LISTING, {
                ...payload,
            });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const GetClientDetails = createAsyncThunk(
    'client/GetClientDetails',
    async ({ clientId }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await getApi(`${GET_CLIENT_DETAILS}/${clientId}`);
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const GetStaffDetails = createAsyncThunk(
    'client/GetStaffDetails',
    async ({ clientId }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await getApi(`${GET_STAFF_DETAILS}/${clientId}`);
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*------------------------- Client List Role Wise ------------------------ */

export const GetClientListingByRole = createAsyncThunk(
    'client/GetClientListingByRole',
    async (
        { ...payload }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            let { organizationId } = getState().auth_store;
            const res = await postApi(GET_CLIENT_LIST_BY_STAFF, {
                ...payload,
                // organizationId,
            });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
