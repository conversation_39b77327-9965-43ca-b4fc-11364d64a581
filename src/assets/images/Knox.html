<!DOCTYPE html>
<!-- saved from url=(0031)http://localhost:3000/dashboard -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style data-rc-order="prepend" rc-util-key="@ant-design-icons">
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1ppr2fz" data-token-hash="ceha6z" data-cache-path="ceha6z|Shared|ant">:where(.css-dev-only-do-not-override-qnu6hi) a{color:#1677ff;text-decoration:none;background-color:transparent;outline:none;cursor:pointer;transition:color 0.3s;-webkit-text-decoration-skip:objects;}:where(.css-dev-only-do-not-override-qnu6hi) a:hover{color:#69b1ff;}:where(.css-dev-only-do-not-override-qnu6hi) a:active{color:#0958d9;}:where(.css-dev-only-do-not-override-qnu6hi) a:active,:where(.css-dev-only-do-not-override-qnu6hi) a:hover{text-decoration:none;outline:0;}:where(.css-dev-only-do-not-override-qnu6hi) a:focus{text-decoration:none;outline:0;}:where(.css-dev-only-do-not-override-qnu6hi) a[disabled]{color:rgba(0, 0, 0, 0.25);cursor:not-allowed;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="15vcmxc" data-token-hash="ceha6z" data-cache-path="ceha6z|Tooltip-Tooltip|ant-tooltip|anticon">:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:absolute;z-index:1070;display:block;width:max-content;max-width:250px;visibility:visible;--valid-offset-x:var(--arrow-offset-horizontal, var(--arrow-x));transform-origin:var(--valid-offset-x, 50%) var(--arrow-y, 50%);--antd-arrow-background-color:rgba(0, 0, 0, 0.85);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-hidden{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip .ant-tooltip-inner{min-width:1em;min-height:32px;padding:6px 8px;color:#fff;text-align:start;text-decoration:none;word-wrap:break-word;background-color:rgba(0, 0, 0, 0.85);border-radius:6px;box-shadow:0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-left .ant-tooltip-inner,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-leftTop .ant-tooltip-inner,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-leftBottom .ant-tooltip-inner,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-right .ant-tooltip-inner,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-rightTop .ant-tooltip-inner,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-rightBottom .ant-tooltip-inner{border-radius:6px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip .ant-tooltip-content{position:relative;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-blue .ant-tooltip-inner{background-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-blue .ant-tooltip-arrow{--antd-arrow-background-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-purple .ant-tooltip-inner{background-color:#722ed1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-purple .ant-tooltip-arrow{--antd-arrow-background-color:#722ed1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-cyan .ant-tooltip-inner{background-color:#13c2c2;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-cyan .ant-tooltip-arrow{--antd-arrow-background-color:#13c2c2;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-green .ant-tooltip-inner{background-color:#52c41a;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-green .ant-tooltip-arrow{--antd-arrow-background-color:#52c41a;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-magenta .ant-tooltip-inner{background-color:#eb2f96;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-magenta .ant-tooltip-arrow{--antd-arrow-background-color:#eb2f96;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-pink .ant-tooltip-inner{background-color:#eb2f96;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-pink .ant-tooltip-arrow{--antd-arrow-background-color:#eb2f96;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-red .ant-tooltip-inner{background-color:#f5222d;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-red .ant-tooltip-arrow{--antd-arrow-background-color:#f5222d;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-orange .ant-tooltip-inner{background-color:#fa8c16;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-orange .ant-tooltip-arrow{--antd-arrow-background-color:#fa8c16;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-yellow .ant-tooltip-inner{background-color:#fadb14;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-yellow .ant-tooltip-arrow{--antd-arrow-background-color:#fadb14;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-volcano .ant-tooltip-inner{background-color:#fa541c;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-volcano .ant-tooltip-arrow{--antd-arrow-background-color:#fa541c;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-geekblue .ant-tooltip-inner{background-color:#2f54eb;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-geekblue .ant-tooltip-arrow{--antd-arrow-background-color:#2f54eb;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-lime .ant-tooltip-inner{background-color:#a0d911;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-lime .ant-tooltip-arrow{--antd-arrow-background-color:#a0d911;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-gold .ant-tooltip-inner{background-color:#faad14;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip.ant-tooltip-gold .ant-tooltip-arrow{--antd-arrow-background-color:#faad14;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip .ant-tooltip-arrow{position:absolute;z-index:1;display:block;pointer-events:none;width:16px;height:16px;overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip .ant-tooltip-arrow::before{position:absolute;bottom:0;inset-inline-start:0;width:16px;height:8px;background:var(--antd-arrow-background-color);clip-path:polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%);clip-path:path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z');content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip .ant-tooltip-arrow::after{content:"";position:absolute;width:8.970562748477143px;height:8.970562748477143px;bottom:0;inset-inline:0;margin:auto;border-radius:0 0 2px 0;transform:translateY(50%) rotate(-135deg);box-shadow:2px 2px 5px rgba(0, 0, 0, 0.05);z-index:0;background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip .ant-tooltip-arrow:before{background:var(--antd-arrow-background-color);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-top>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-topLeft>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-topRight>.ant-tooltip-arrow{bottom:0;transform:translateY(100%) rotate(180deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-top>.ant-tooltip-arrow{left:50%;transform:translateX(-50%) translateY(100%) rotate(180deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-topLeft{--arrow-offset-horizontal:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-topLeft >.ant-tooltip-arrow{left:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-topRight{--arrow-offset-horizontal:calc(100% - 12px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-topRight >.ant-tooltip-arrow{right:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottom>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottomLeft>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottomRight>.ant-tooltip-arrow{top:0;transform:translateY(-100%);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottom>.ant-tooltip-arrow{left:50%;transform:translateX(-50%) translateY(-100%);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottomLeft{--arrow-offset-horizontal:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottomLeft >.ant-tooltip-arrow{left:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottomRight{--arrow-offset-horizontal:calc(100% - 12px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-bottomRight >.ant-tooltip-arrow{right:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-left>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-leftTop>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-leftBottom>.ant-tooltip-arrow{right:0;transform:translateX(100%) rotate(90deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-left>.ant-tooltip-arrow{top:50%;transform:translateY(-50%) translateX(100%) rotate(90deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-leftTop>.ant-tooltip-arrow{top:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-leftBottom>.ant-tooltip-arrow{bottom:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-right>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-rightTop>.ant-tooltip-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-rightBottom>.ant-tooltip-arrow{left:0;transform:translateX(-100%) rotate(-90deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-right>.ant-tooltip-arrow{top:50%;transform:translateY(-50%) translateX(-100%) rotate(-90deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-rightTop>.ant-tooltip-arrow{top:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-placement-rightBottom>.ant-tooltip-arrow{bottom:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-tooltip-pure{position:relative;max-width:none;margin:16px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-appear{animation-duration:0.1s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-leave{animation-duration:0.1s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-enter.ant-zoom-big-fast-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-appear.ant-zoom-big-fast-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomBigIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-leave.ant-zoom-big-fast-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomBigOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-appear{transform:scale(0);opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-appear-prepare{transform:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-fast-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antZoomBigIn">@keyframes css-dev-only-do-not-override-qnu6hi-antZoomBigIn{0%{transform:scale(0.8);opacity:0;}100%{transform:scale(1);opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antZoomBigOut">@keyframes css-dev-only-do-not-override-qnu6hi-antZoomBigOut{0%{transform:scale(1);}100%{transform:scale(0.8);opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="10efvf3" data-token-hash="ceha6z" data-cache-path="ceha6z|Menu-Menu|ant-menu|anticon">:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"]{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"] [class^="ant-menu"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"] [class^="ant-menu"],:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"] [class*=" ant-menu"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"] [class*=" ant-menu"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"] [class^="ant-menu"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"] [class^="ant-menu"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"] [class*=" ant-menu"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"] [class*=" ant-menu"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"] [class^="ant-menu"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"] [class^="ant-menu"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-menu"] [class*=" ant-menu"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-menu"] [class*=" ant-menu"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi) .ant-menu::before{display:table;content:"";}:where(.css-dev-only-do-not-override-qnu6hi) .ant-menu::after{display:table;clear:both;content:"";}:where(.css-dev-only-do-not-override-qnu6hi) .ant-menu-hidden{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-hidden{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:0;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';margin-bottom:0;padding-inline-start:0;outline:none;transition:width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu::before{display:table;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu::after{display:table;clear:both;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu ul,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu ol{margin:0;padding:0;list-style:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-overflow{display:flex;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-overflow .ant-menu-item{flex:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title{border-radius:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-group-title{padding:8px 16px;font-size:14px;line-height:1.5714285714285714;transition:all 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal .ant-menu-submenu{transition:border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-inline{transition:border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),padding 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu .ant-menu-sub{cursor:initial;transition:background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-title-content{transition:color 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-title-content >.ant-typography-ellipsis-single-line{display:inline;vertical-align:unset;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item a::before{position:absolute;inset:0;background-color:transparent;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-divider{overflow:hidden;line-height:0;border-color:rgba(5, 5, 5, 0.06);border-style:solid;border-width:0;border-top-width:1px;margin-block:1px;padding:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-divider-dashed{border-style:dashed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title{position:relative;display:block;margin:0;white-space:nowrap;cursor:pointer;transition:border-color 0.3s,background 0.3s,padding calc(0.3s + 0.1s) cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .anticon{min-width:14px;font-size:14px;transition:font-size 0.2s cubic-bezier(0.215, 0.61, 0.355, 1),margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),color 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .anticon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .anticon +span{margin-inline-start:10px;opacity:1;transition:opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),margin 0.3s,color 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .ant-menu-item-icon{display:inline-flex;align-items:center;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .ant-menu-item-icon >*,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .ant-menu-item-icon >*{line-height:1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item .ant-menu-item-icon svg,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title .ant-menu-item-icon svg{display:inline-block;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item.ant-menu-item-only-child >.anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title.ant-menu-item-only-child >.anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item.ant-menu-item-only-child >.ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-title.ant-menu-item-only-child >.ant-menu-item-icon{margin-inline-end:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-disabled{background:none!important;cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-disabled::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-disabled::after{border-color:transparent!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-disabled a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-disabled a{color:inherit!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-disabled >.ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-disabled >.ant-menu-submenu-title{color:inherit!important;cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-group .ant-menu-item-group-list{margin:0;padding:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-group .ant-menu-item-group-list .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-item-group .ant-menu-item-group-list .ant-menu-submenu-title{padding-inline:28px 16px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup{position:absolute;z-index:1050;border-radius:8px;box-shadow:none;transform-origin:0 0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup.ant-menu-submenu{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup::before{position:absolute;inset:0;z-index:-1;width:100%;height:100%;opacity:0;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu{border-radius:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title{position:relative;display:block;margin:0;white-space:nowrap;cursor:pointer;transition:border-color 0.3s,background 0.3s,padding calc(0.3s + 0.1s) cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .anticon{min-width:14px;font-size:14px;transition:font-size 0.2s cubic-bezier(0.215, 0.61, 0.355, 1),margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),color 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .anticon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .anticon +span{margin-inline-start:10px;opacity:1;transition:opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),margin 0.3s,color 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .ant-menu-item-icon{display:inline-flex;align-items:center;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .ant-menu-item-icon >*,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .ant-menu-item-icon >*{line-height:1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item .ant-menu-item-icon svg,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title .ant-menu-item-icon svg{display:inline-block;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item.ant-menu-item-only-child >.anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title.ant-menu-item-only-child >.anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item.ant-menu-item-only-child >.ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title.ant-menu-item-only-child >.ant-menu-item-icon{margin-inline-end:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-disabled{background:none!important;cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item-disabled::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-disabled::after{border-color:transparent!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item-disabled a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-disabled a{color:inherit!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item-disabled >.ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-disabled >.ant-menu-submenu-title{color:inherit!important;cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-arrow{position:absolute;top:50%;inset-inline-end:16px;width:10px;color:currentcolor;transform:translateY(-50%);transition:transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-arrow::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-arrow::after{position:absolute;width:6px;height:1.5px;background-color:currentcolor;border-radius:6px;transition:background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-arrow::before{transform:rotate(45deg) translateY(-2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-arrow::after{transform:rotate(-45deg) translateY(2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu>.ant-menu-submenu-title{border-radius:4px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup >.ant-menu .ant-menu-submenu-title::after{transition:transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-leftTop,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-bottomRight,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu{transform-origin:100% 0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-leftBottom,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-topRight,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu{transform-origin:100% 100%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-rightBottom,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-topLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu{transform-origin:0 100%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-bottomLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-rightTop,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu{transform-origin:0 0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-leftTop,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-leftBottom{padding-inline-end:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-rightTop,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-rightBottom{padding-inline-start:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-topRight,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-topLeft{padding-bottom:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-bottomRight,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-placement-bottomLeft{padding-top:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-arrow{position:absolute;top:50%;inset-inline-end:16px;width:10px;color:currentcolor;transform:translateY(-50%);transition:transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-arrow::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-arrow::after{position:absolute;width:6px;height:1.5px;background-color:currentcolor;border-radius:6px;transition:background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-arrow::before{transform:rotate(45deg) translateY(-2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-arrow::after{transform:rotate(-45deg) translateY(2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed .ant-menu-submenu-arrow::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-submenu-arrow::before{transform:rotate(-45deg) translateX(2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed .ant-menu-submenu-arrow::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-submenu-arrow::after{transform:rotate(45deg) translateX(-2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-open.ant-menu-submenu-inline>.ant-menu-submenu-title>.ant-menu-submenu-arrow{transform:translateY(-2px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-open.ant-menu-submenu-inline>.ant-menu-submenu-title>.ant-menu-submenu-arrow::after{transform:rotate(-45deg) translateX(-2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-menu-submenu-open.ant-menu-submenu-inline>.ant-menu-submenu-title>.ant-menu-submenu-arrow::before{transform:rotate(45deg) translateX(2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout-header .ant-menu{line-height:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal{line-height:46px;border:0;border-bottom:1px solid rgba(5, 5, 5, 0.06);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal::after{display:block;clear:both;height:0;content:"\20";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal .ant-menu-submenu{position:relative;display:inline-block;vertical-align:bottom;padding-inline:16px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal >.ant-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal >.ant-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal >.ant-menu-submenu .ant-menu-submenu-title:hover{background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal .ant-menu-submenu-title{transition:border-color 0.3s,background 0.3s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-horizontal .ant-menu-submenu-arrow{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical.ant-menu-root{box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical .ant-menu-item{position:relative;overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical .ant-menu-submenu-title{height:40px;line-height:40px;padding-inline:16px;overflow:hidden;text-overflow:ellipsis;margin-inline:4px;margin-block:4px;width:calc(100% - 8px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline >.ant-menu-submenu>.ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical >.ant-menu-submenu>.ant-menu-submenu-title{height:40px;line-height:40px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-item-group-list .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical .ant-menu-item-group-list .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-vertical .ant-menu-submenu-title{padding-inline-end:34px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical{box-shadow:0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical .ant-menu-item{position:relative;overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-title{height:40px;line-height:40px;padding-inline:16px;overflow:hidden;text-overflow:ellipsis;margin-inline:4px;margin-block:4px;width:calc(100% - 8px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical >.ant-menu-submenu>.ant-menu-submenu-title{height:40px;line-height:40px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical .ant-menu-item-group-list .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-title{padding-inline-end:34px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub{min-width:160px;max-height:calc(100vh - 100px);padding:0;overflow:hidden;border-inline-end:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-popup .ant-menu-vertical.ant-menu-sub:not([class*='-active']){overflow-x:hidden;overflow-y:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline{width:100%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root .ant-menu-submenu-title{display:flex;align-items:center;transition:border-color 0.3s,background 0.3s,padding 0.1s cubic-bezier(0.215, 0.61, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root .ant-menu-item >.ant-menu-title-content,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root .ant-menu-submenu-title >.ant-menu-title-content{flex:auto;min-width:0;overflow:hidden;text-overflow:ellipsis;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root .ant-menu-item >*,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline.ant-menu-root .ant-menu-submenu-title >*{flex:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-sub.ant-menu-inline{padding:0;border:0;border-radius:0;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-sub.ant-menu-inline>.ant-menu-submenu>.ant-menu-submenu-title{height:40px;line-height:40px;list-style-position:inside;list-style-type:disc;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-sub.ant-menu-inline .ant-menu-item-group-title{padding-inline-start:32px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline .ant-menu-item{height:40px;line-height:40px;list-style-position:inside;list-style-type:disc;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed{width:80px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed.ant-menu-root .ant-menu-item >.ant-menu-inline-collapsed-noicon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed.ant-menu-root .ant-menu-submenu .ant-menu-submenu-title >.ant-menu-inline-collapsed-noicon{font-size:16px;text-align:center;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title{inset-inline-start:0;padding-inline:calc(50% - 8px - 4px);text-overflow:clip;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item .ant-menu-submenu-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .ant-menu-submenu-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-submenu-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-submenu-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item .ant-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .ant-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-submenu-expand-icon{opacity:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item .anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title .anticon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title .anticon{margin:0;font-size:16px;line-height:40px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title .ant-menu-item-icon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item .anticon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-item .anticon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-item-group>.ant-menu-item-group-list>.ant-menu-submenu>.ant-menu-submenu-title .anticon +span,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed >.ant-menu-submenu>.ant-menu-submenu-title .anticon +span{display:inline-block;opacity:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed .anticon{display:inline-block;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed-tooltip{pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed-tooltip .ant-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed-tooltip .anticon{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed-tooltip a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed-tooltip a:hover{color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-inline-collapsed .ant-menu-item-group-title{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;padding-inline:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu{color:rgba(0, 0, 0, 0.88);background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-root:focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-root:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-group-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-group-title{color:rgba(0, 0, 0, 0.45);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-submenu-selected >.ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-submenu-selected >.ant-menu-submenu-title{color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-submenu-title{color:rgba(0, 0, 0, 0.88);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item:not(.ant-menu-item-disabled):focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item:not(.ant-menu-item-disabled):focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-submenu-title:not(.ant-menu-item-disabled):focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-submenu-title:not(.ant-menu-item-disabled):focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-submenu-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-submenu-disabled{color:rgba(0, 0, 0, 0.25)!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected) >.ant-menu-submenu-title:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected) >.ant-menu-submenu-title:hover{color:rgba(0, 0, 0, 0.88);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover{background-color:rgba(0, 0, 0, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active{background-color:#e6f4ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light:not(.ant-menu-horizontal) .ant-menu-submenu-title:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu:not(.ant-menu-horizontal) .ant-menu-submenu-title:hover{background-color:rgba(0, 0, 0, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light:not(.ant-menu-horizontal) .ant-menu-submenu-title:active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu:not(.ant-menu-horizontal) .ant-menu-submenu-title:active{background-color:#e6f4ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-danger,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-danger{color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-danger.ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected),:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-danger.ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected){color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-danger.ant-menu-item:active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-danger.ant-menu-item:active{background:#fff2f0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item a:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item a:hover{color:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-selected{color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-selected.ant-menu-item-danger,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-selected.ant-menu-item-danger{color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-selected a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-selected a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-selected a:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-selected a:hover{color:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-selected{background-color:#e6f4ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light .ant-menu-item-selected.ant-menu-item-danger,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu .ant-menu-item-selected.ant-menu-item-danger{background-color:#fff2f0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-submenu>.ant-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-submenu>.ant-menu{background-color:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-popup>.ant-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-popup>.ant-menu{background-color:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-submenu-popup>.ant-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-submenu-popup>.ant-menu{background-color:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu{top:1px;margin-top:-1px;margin-bottom:0;border-radius:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu::after{position:absolute;inset-inline:16px;bottom:0;border-bottom:2px solid transparent;transition:border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-open,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-open,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-open,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-open{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-open::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-open::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-open::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-open::after{border-bottom-width:2px;border-bottom-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-selected{color:#1677ff;background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-selected:hover{background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-item-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-item-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-horizontal >.ant-menu-submenu-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-selected::after{border-bottom-width:2px;border-bottom-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-root.ant-menu-inline,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-root.ant-menu-inline,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-root.ant-menu-vertical,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-root.ant-menu-vertical{border-inline-end:1px solid rgba(5, 5, 5, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-inline .ant-menu-sub.ant-menu-inline{background:rgba(0, 0, 0, 0.02);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-inline .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-inline .ant-menu-item{position:relative;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-inline .ant-menu-item::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-inline .ant-menu-item::after{position:absolute;inset-block:0;inset-inline-end:0;border-inline-end:0px solid #1677ff;transform:scaleY(0.0001);opacity:0;transition:transform 0.2s cubic-bezier(0.215, 0.61, 0.355, 1),opacity 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-inline .ant-menu-item.ant-menu-item-danger::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-inline .ant-menu-item.ant-menu-item-danger::after{border-inline-end-color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-inline .ant-menu-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-inline .ant-menu-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light.ant-menu-inline .ant-menu-item-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-light>.ant-menu.ant-menu-inline .ant-menu-item-selected::after{transform:scaleY(1);opacity:1;transition:transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu{color:rgba(255, 255, 255, 0.65);background:#001529;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-root:focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-root:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-group-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-group-title{color:rgba(255, 255, 255, 0.65);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-submenu-selected >.ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-submenu-selected >.ant-menu-submenu-title{color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-submenu-title{color:rgba(255, 255, 255, 0.65);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item:not(.ant-menu-item-disabled):focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item:not(.ant-menu-item-disabled):focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-submenu-title:not(.ant-menu-item-disabled):focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-submenu-title:not(.ant-menu-item-disabled):focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-submenu-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-submenu-disabled{color:rgba(255, 255, 255, 0.25)!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected) >.ant-menu-submenu-title:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected) >.ant-menu-submenu-title:hover{color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover{background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active{background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu:not(.ant-menu-horizontal) .ant-menu-submenu-title:hover{background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title:active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu:not(.ant-menu-horizontal) .ant-menu-submenu-title:active{background-color:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-danger,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-danger{color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-danger.ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected),:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-danger.ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected){color:#ff7875;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-danger.ant-menu-item:active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-danger.ant-menu-item:active{background:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item a:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item a:hover{color:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-selected{color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-selected.ant-menu-item-danger,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-selected.ant-menu-item-danger{color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-selected a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-selected a,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-selected a:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-selected a:hover{color:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-selected{background-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark .ant-menu-item-selected.ant-menu-item-danger,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu .ant-menu-item-selected.ant-menu-item-danger{background-color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-submenu>.ant-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-submenu>.ant-menu{background-color:#000c17;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-popup>.ant-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-popup>.ant-menu{background-color:#001529;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-submenu-popup>.ant-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-submenu-popup>.ant-menu{background-color:#001529;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal{border-bottom:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu{top:0;margin-top:0;margin-bottom:0;border-radius:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu::after{position:absolute;inset-inline:16px;bottom:0;border-bottom:0px solid transparent;transition:border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-open,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-open,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-open,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-open{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu:hover::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-active::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-open::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-open::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-open::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-open::after{border-bottom-width:0;border-bottom-color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-selected{color:#fff;background-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-selected:hover{background-color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-item-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-item-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-horizontal >.ant-menu-submenu-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-horizontal >.ant-menu-submenu-selected::after{border-bottom-width:0;border-bottom-color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-root.ant-menu-inline,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-root.ant-menu-inline,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-root.ant-menu-vertical,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-root.ant-menu-vertical{border-inline-end:0px solid rgba(5, 5, 5, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-inline .ant-menu-sub.ant-menu-inline,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-inline .ant-menu-sub.ant-menu-inline{background:#000c17;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-inline .ant-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-inline .ant-menu-item{position:relative;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-inline .ant-menu-item::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-inline .ant-menu-item::after{position:absolute;inset-block:0;inset-inline-end:0;border-inline-end:0px solid #fff;transform:scaleY(0.0001);opacity:0;transition:transform 0.2s cubic-bezier(0.215, 0.61, 0.355, 1),opacity 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-inline .ant-menu-item.ant-menu-item-danger::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-inline .ant-menu-item.ant-menu-item-danger::after{border-inline-end-color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-inline .ant-menu-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-inline .ant-menu-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark.ant-menu-inline .ant-menu-item-selected::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-dark>.ant-menu.ant-menu-inline .ant-menu-item-selected::after{transform:scaleY(1);opacity:1;transition:transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-rtl{transform-origin:100% 0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-rtl.ant-menu-vertical .ant-menu-submenu-arrow::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-rtl .ant-menu-vertical .ant-menu-submenu-arrow::before{transform:rotate(-45deg) translateY(-2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-rtl.ant-menu-vertical .ant-menu-submenu-arrow::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-menu-submenu-rtl .ant-menu-vertical .ant-menu-submenu-arrow::after{transform:rotate(45deg) translateY(2.5px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-motion-collapse-legacy{overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-motion-collapse-legacy-active{transition:height 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-menu .ant-motion-collapse{overflow:hidden;transition:height 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1)!important;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter.ant-slide-up-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear.ant-slide-up-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideUpIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-leave.ant-slide-up-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideUpOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear{transform:scale(0);transform-origin:0% 0%;opacity:0;animation-timing-function:cubic-bezier(0.23, 1, 0.32, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear-prepare{transform:scale(1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-leave{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter.ant-slide-down-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear.ant-slide-down-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideDownIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-leave.ant-slide-down-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideDownOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear{transform:scale(0);transform-origin:0% 0%;opacity:0;animation-timing-function:cubic-bezier(0.23, 1, 0.32, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear-prepare{transform:scale(1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-leave{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter.ant-zoom-big-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear.ant-zoom-big-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomBigIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-leave.ant-zoom-big-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomBigOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear{transform:scale(0);opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear-prepare{transform:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antSlideUpIn">@keyframes css-dev-only-do-not-override-qnu6hi-antSlideUpIn{0%{transform:scaleY(0.8);transform-origin:0% 0%;opacity:0;}100%{transform:scaleY(1);transform-origin:0% 0%;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antSlideUpOut">@keyframes css-dev-only-do-not-override-qnu6hi-antSlideUpOut{0%{transform:scaleY(1);transform-origin:0% 0%;opacity:1;}100%{transform:scaleY(0.8);transform-origin:0% 0%;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antSlideDownIn">@keyframes css-dev-only-do-not-override-qnu6hi-antSlideDownIn{0%{transform:scaleY(0.8);transform-origin:100% 100%;opacity:0;}100%{transform:scaleY(1);transform-origin:100% 100%;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antSlideDownOut">@keyframes css-dev-only-do-not-override-qnu6hi-antSlideDownOut{0%{transform:scaleY(1);transform-origin:100% 100%;opacity:1;}100%{transform:scaleY(0.8);transform-origin:100% 100%;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1onpmkg" data-token-hash="ceha6z" data-cache-path="ceha6z|Button-Button|ant-btn|anticon">:where(.css-dev-only-do-not-override-qnu6hi).ant-btn{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn [class^="ant-btn"],:where(.css-dev-only-do-not-override-qnu6hi).ant-btn [class*=" ant-btn"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn [class^="ant-btn"]::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn [class*=" ant-btn"]::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn [class^="ant-btn"]::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn [class*=" ant-btn"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn{outline:none;position:relative;display:inline-flex;gap:8px;align-items:center;justify-content:center;font-weight:400;white-space:nowrap;text-align:center;background-image:none;background:transparent;border:1px solid transparent;cursor:pointer;transition:all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);user-select:none;touch-action:manipulation;color:rgba(0, 0, 0, 0.88);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn:disabled>*{pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn >span{display:inline-block;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn .ant-btn-icon{line-height:1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn >a{color:currentColor;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn:not(:disabled):focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-two-chinese-chars::first-letter{letter-spacing:0.34em;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-two-chinese-chars>*:not(.anticon){margin-inline-end:-0.34em;letter-spacing:0.34em;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-icon-end{flex-direction:row-reverse;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn{font-size:14px;line-height:1.5714285714285714;height:32px;padding:4px 15px;border-radius:6px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-icon-only{width:32px;padding-inline:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-icon-only.ant-btn-compact-item{flex:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-icon-only.ant-btn-round{width:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-icon-only .anticon{font-size:16px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-loading{opacity:0.65;cursor:default;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn .ant-btn-loading-icon{transition:width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-circle.ant-btn{min-width:32px;padding-inline-start:0;padding-inline-end:0;border-radius:50%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-round.ant-btn{border-radius:32px;padding-inline-start:16px;padding-inline-end:16px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm{font-size:14px;line-height:1.5714285714285714;height:24px;padding:0px 7px;border-radius:4px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm.ant-btn-icon-only{width:24px;padding-inline:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm.ant-btn-icon-only.ant-btn-compact-item{flex:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm.ant-btn-icon-only.ant-btn-round{width:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm.ant-btn-icon-only .anticon{font-size:14px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm.ant-btn-loading{opacity:0.65;cursor:default;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-sm .ant-btn-loading-icon{transition:width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-circle.ant-btn-sm{min-width:24px;padding-inline-start:0;padding-inline-end:0;border-radius:50%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-round.ant-btn-sm{border-radius:24px;padding-inline-start:12px;padding-inline-end:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg{font-size:16px;line-height:1.5;height:40px;padding:7px 15px;border-radius:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg.ant-btn-icon-only{width:40px;padding-inline:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg.ant-btn-icon-only.ant-btn-compact-item{flex:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg.ant-btn-icon-only.ant-btn-round{width:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg.ant-btn-icon-only .anticon{font-size:18px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg.ant-btn-loading{opacity:0.65;cursor:default;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-lg .ant-btn-loading-icon{transition:width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-circle.ant-btn-lg{min-width:40px;padding-inline-start:0;padding-inline-end:0;border-radius:50%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-round.ant-btn-lg{border-radius:40px;padding-inline-start:20px;padding-inline-end:20px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn.ant-btn-block{width:100%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default{background:#ffffff;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.88);box-shadow:0 2px 0 rgba(0, 0, 0, 0.02);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-disabled{cursor:not-allowed;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.25);background:rgba(0, 0, 0, 0.04);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover{color:#4096ff;border-color:#4096ff;background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default:not(:disabled):not(.ant-btn-disabled):active{color:#0958d9;border-color:#0958d9;background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-background-ghost{color:#ffffff;background:transparent;border-color:#ffffff;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous{color:#ff4d4f;border-color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):hover{color:#ff7875;border-color:#ffa39e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):active{color:#d9363e;border-color:#d9363e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous.ant-btn-background-ghost{color:#ff4d4f;background:transparent;border-color:#ff4d4f;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-default.ant-btn-dangerous.ant-btn-disabled{cursor:not-allowed;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.25);background:rgba(0, 0, 0, 0.04);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary{color:#fff;background:#1677ff;box-shadow:0 2px 0 rgba(5, 145, 255, 0.1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-disabled{cursor:not-allowed;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.25);background:rgba(0, 0, 0, 0.04);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary:not(:disabled):not(.ant-btn-disabled):hover{color:#fff;background:#4096ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary:not(:disabled):not(.ant-btn-disabled):active{color:#fff;background:#0958d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-background-ghost{color:#1677ff;background:transparent;border-color:#1677ff;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;color:#4096ff;border-color:#4096ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;color:#0958d9;border-color:#0958d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous{background:#ff4d4f;box-shadow:0 2px 0 rgba(255, 38, 5, 0.06);color:#fff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):hover{background:#ff7875;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):active{background:#d9363e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous.ant-btn-background-ghost{color:#ff4d4f;background:transparent;border-color:#ff4d4f;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;color:#ff7875;border-color:#ff7875;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;color:#d9363e;border-color:#d9363e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-primary.ant-btn-dangerous.ant-btn-disabled{cursor:not-allowed;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.25);background:rgba(0, 0, 0, 0.04);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed{background:#ffffff;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.88);box-shadow:0 2px 0 rgba(0, 0, 0, 0.02);border-style:dashed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-disabled{cursor:not-allowed;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.25);background:rgba(0, 0, 0, 0.04);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed:not(:disabled):not(.ant-btn-disabled):hover{color:#4096ff;border-color:#4096ff;background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed:not(:disabled):not(.ant-btn-disabled):active{color:#0958d9;border-color:#0958d9;background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-background-ghost{color:#ffffff;background:transparent;border-color:#ffffff;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous{color:#ff4d4f;border-color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):hover{color:#ff7875;border-color:#ffa39e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):active{color:#d9363e;border-color:#d9363e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous.ant-btn-background-ghost{color:#ff4d4f;background:transparent;border-color:#ff4d4f;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-dashed.ant-btn-dangerous.ant-btn-disabled{cursor:not-allowed;border-color:#d9d9d9;color:rgba(0, 0, 0, 0.25);background:rgba(0, 0, 0, 0.04);box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link{color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link:not(:disabled):not(.ant-btn-disabled):hover{color:#69b1ff;background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link:not(:disabled):not(.ant-btn-disabled):active{color:#0958d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link.ant-btn-disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link.ant-btn-dangerous{color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):hover{color:#ff7875;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):active{color:#d9363e;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link.ant-btn-dangerous:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-link.ant-btn-dangerous.ant-btn-disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text:not(:disabled):not(.ant-btn-disabled):hover{color:rgba(0, 0, 0, 0.88);background:rgba(0, 0, 0, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text:not(:disabled):not(.ant-btn-disabled):active{color:rgba(0, 0, 0, 0.88);background:rgba(0, 0, 0, 0.15);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text.ant-btn-disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text.ant-btn-dangerous{color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text.ant-btn-dangerous:disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text.ant-btn-dangerous.ant-btn-disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):hover{color:#ff7875;background:#fff2f0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-text.ant-btn-dangerous:not(:disabled):not(.ant-btn-disabled):active{color:#ff7875;background:#ffccc7;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-ghost.ant-btn-background-ghost{color:#ffffff;background:transparent;border-color:#ffffff;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-ghost.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):hover{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-ghost.ant-btn-background-ghost:not(:disabled):not(.ant-btn-disabled):active{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-ghost.ant-btn-background-ghost:disabled{cursor:not-allowed;color:rgba(0, 0, 0, 0.25);border-color:#d9d9d9;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group{position:relative;display:inline-flex;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:last-child),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn:not(:last-child),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:last-child)>.ant-btn,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn:not(:last-child)>.ant-btn{border-start-end-radius:0;border-end-end-radius:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn:not(:first-child){margin-inline-start:-1px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn:not(:first-child),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child)>.ant-btn,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn:not(:first-child)>.ant-btn{border-start-start-radius:0;border-end-start-radius:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group .ant-btn{position:relative;z-index:1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group .ant-btn:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group .ant-btn:focus,:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group .ant-btn:active{z-index:2;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group .ant-btn[disabled]{z-index:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group .ant-btn-icon-only{font-size:14px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:last-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-primary:not(:last-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:last-child)>.ant-btn-primary:not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-primary:not(:last-child)>.ant-btn-primary:not(:disabled){border-inline-end-color:#4096ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-primary:not(:first-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child)>.ant-btn-primary:not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-primary:not(:first-child)>.ant-btn-primary:not(:disabled){border-inline-start-color:#4096ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:last-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-danger:not(:last-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:last-child)>.ant-btn-danger:not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-danger:not(:last-child)>.ant-btn-danger:not(:disabled){border-inline-end-color:#ff7875;}:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-danger:not(:first-child):not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >span:not(:first-child)>.ant-btn-danger:not(:disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-btn-group >.ant-btn-danger:not(:first-child)>.ant-btn-danger:not(:disabled){border-inline-start-color:#ff7875;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1ujfi5m" data-token-hash="ceha6z" data-cache-path="ceha6z|Avatar-Avatar|ant-avatar|anticon">:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"]{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"] [class^="ant-avatar"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"] [class^="ant-avatar"],:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"] [class*=" ant-avatar"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"] [class*=" ant-avatar"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"] [class^="ant-avatar"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"] [class^="ant-avatar"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"] [class*=" ant-avatar"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"] [class*=" ant-avatar"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"] [class^="ant-avatar"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"] [class^="ant-avatar"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-avatar"] [class*=" ant-avatar"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-avatar"] [class*=" ant-avatar"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar{box-sizing:border-box;margin:0;padding:0;color:#fff;font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';position:relative;display:inline-flex;justify-content:center;align-items:center;overflow:hidden;white-space:nowrap;text-align:center;vertical-align:middle;background:rgba(0, 0, 0, 0.25);border:1px solid transparent;width:32px;height:32px;border-radius:50%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-image{background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar .ant-image-img{display:block;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar.ant-avatar-square{border-radius:6px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar.ant-avatar-icon{font-size:18px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar.ant-avatar-icon >.anticon{margin:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-lg{width:40px;height:40px;border-radius:50%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-lg.ant-avatar-square{border-radius:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-lg.ant-avatar-icon{font-size:24px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-lg.ant-avatar-icon >.anticon{margin:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-sm{width:24px;height:24px;border-radius:50%;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-sm.ant-avatar-square{border-radius:4px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-sm.ant-avatar-icon{font-size:14px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-sm.ant-avatar-icon >.anticon{margin:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar >img{display:block;width:100%;height:100%;object-fit:cover;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-group{display:inline-flex;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-group .ant-avatar{border-color:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-group >*:not(:first-child){margin-inline-start:-8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-avatar-group-popover .ant-avatar+.ant-avatar{margin-inline-start:4px;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1szva44" data-token-hash="ceha6z" data-cache-path="ceha6z|Dropdown-Dropdown|ant-dropdown|anticon">:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown{position:absolute;top:-9999px;left:-9999px;z-index:1050;display:block;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown::before{position:absolute;inset-block:-4px;z-index:-9999;opacity:0.0001;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-trigger.ant-btn>.anticon-down,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-trigger.ant-btn>.ant-btn-icon>.anticon-down{font-size:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-wrap{position:relative;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-wrap .ant-btn>.anticon-down{font-size:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-wrap .anticon-down::before{transition:transform 0.2s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-wrap-open .anticon-down::before{transform:rotate(180deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-hidden,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-hidden,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu-hidden{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-enter.ant-slide-down-enter-active.ant-dropdown-placement-bottomLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-appear.ant-slide-down-appear-active.ant-dropdown-placement-bottomLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-enter.ant-slide-down-enter-active.ant-dropdown-placement-bottom,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-appear.ant-slide-down-appear-active.ant-dropdown-placement-bottom,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-enter.ant-slide-down-enter-active.ant-dropdown-placement-bottomRight,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-appear.ant-slide-down-appear-active.ant-dropdown-placement-bottomRight{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideUpIn;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-enter.ant-slide-up-enter-active.ant-dropdown-placement-topLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-appear.ant-slide-up-appear-active.ant-dropdown-placement-topLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-enter.ant-slide-up-enter-active.ant-dropdown-placement-top,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-appear.ant-slide-up-appear-active.ant-dropdown-placement-top,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-enter.ant-slide-up-enter-active.ant-dropdown-placement-topRight,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-appear.ant-slide-up-appear-active.ant-dropdown-placement-topRight{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideDownIn;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-leave.ant-slide-down-leave-active.ant-dropdown-placement-bottomLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-leave.ant-slide-down-leave-active.ant-dropdown-placement-bottom,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-down-leave.ant-slide-down-leave-active.ant-dropdown-placement-bottomRight{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideUpOut;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-leave.ant-slide-up-leave-active.ant-dropdown-placement-topLeft,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-leave.ant-slide-up-leave-active.ant-dropdown-placement-top,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown.ant-slide-up-leave.ant-slide-up-leave-active.ant-dropdown-placement-topRight{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideDownOut;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-arrow{position:absolute;z-index:1;display:block;pointer-events:none;width:16px;height:16px;overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-arrow::before{position:absolute;bottom:0;inset-inline-start:0;width:16px;height:8px;background:#ffffff;clip-path:polygon(1.6568542494923806px 100%, 50% 1.6568542494923806px, 14.34314575050762px 100%, 1.6568542494923806px 100%);clip-path:path('M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 6.585786437626905 3.0710678118654755 A 2 2 0 0 1 9.414213562373096 3.0710678118654755 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z');content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-arrow::after{content:"";position:absolute;width:8.970562748477143px;height:8.970562748477143px;bottom:0;inset-inline:0;margin:auto;border-radius:0 0 2px 0;transform:translateY(50%) rotate(-135deg);box-shadow:2px 2px 5px rgba(0, 0, 0, 0.05);z-index:0;background:transparent;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-arrow:before{background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-top>.ant-dropdown-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-topLeft>.ant-dropdown-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-topRight>.ant-dropdown-arrow{bottom:0;transform:translateY(100%) rotate(180deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-top>.ant-dropdown-arrow{left:50%;transform:translateX(-50%) translateY(100%) rotate(180deg);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-topLeft{--arrow-offset-horizontal:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-topLeft >.ant-dropdown-arrow{left:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-topRight{--arrow-offset-horizontal:calc(100% - 12px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-topRight >.ant-dropdown-arrow{right:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottom>.ant-dropdown-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottomLeft>.ant-dropdown-arrow,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottomRight>.ant-dropdown-arrow{top:0;transform:translateY(-100%);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottom>.ant-dropdown-arrow{left:50%;transform:translateX(-50%) translateY(-100%);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottomLeft{--arrow-offset-horizontal:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottomLeft >.ant-dropdown-arrow{left:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottomRight{--arrow-offset-horizontal:calc(100% - 12px);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-placement-bottomRight >.ant-dropdown-arrow{right:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu{position:relative;margin:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu-popup{position:absolute;z-index:1050;background:transparent;box-shadow:none;transform-origin:0 0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu-popup ul,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu-popup li{list-style:none;margin:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu{box-sizing:border-box;margin:0;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu{padding:4px;list-style-type:none;background-color:#ffffff;background-clip:padding-box;border-radius:8px;outline:none;box-shadow:0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu:focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu:empty,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu:empty{padding:0;box-shadow:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-group-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-group-title{padding:5px 12px;color:rgba(0, 0, 0, 0.45);transition:all 0.2s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item{position:relative;display:flex;align-items:center;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-icon{min-width:14px;margin-inline-end:8px;font-size:12px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-title-content,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-title-content{flex:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-title-content >a,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-title-content >a{color:inherit;transition:all 0.2s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-title-content >a:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-title-content >a:hover{color:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-title-content >a::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-title-content >a::after{position:absolute;inset:0;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title{display:flex;margin:0;padding:5px 12px;color:rgba(0, 0, 0, 0.88);font-weight:normal;font-size:14px;line-height:1.5714285714285714;cursor:pointer;transition:all 0.2s;border-radius:4px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-active{background-color:rgba(0, 0, 0, 0.04);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item:focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title:focus-visible,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-selected,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-selected{color:#1677ff;background-color:#e6f4ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-selected:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-selected-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-selected-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-selected-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-selected-active{background-color:#bae0ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-disabled,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-disabled{color:rgba(0, 0, 0, 0.25);cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-disabled:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-disabled:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-disabled:hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-disabled:hover{color:rgba(0, 0, 0, 0.25);background-color:#ffffff;cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-disabled a,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-disabled a,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-disabled a,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-disabled a{pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-divider,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-divider,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-divider,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-divider{height:1px;margin:4px 0;overflow:hidden;line-height:0;background-color:rgba(5, 5, 5, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-expand-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-expand-icon{position:absolute;inset-inline-end:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-expand-icon .ant-dropdown-menu-submenu-arrow-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-expand-icon .ant-dropdown-menu-submenu-arrow-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-expand-icon .ant-dropdown-menu-submenu-arrow-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-expand-icon .ant-dropdown-menu-submenu-arrow-icon{margin-inline-end:0!important;color:rgba(0, 0, 0, 0.45);font-size:12px;font-style:normal;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-group-list,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-group-list{margin:0 8px;padding:0;list-style:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title{padding-inline-end:24px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-vertical,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-vertical{position:relative;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon{color:rgba(0, 0, 0, 0.25);background-color:#ffffff;cursor:not-allowed;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-selected .ant-dropdown-menu-submenu-title,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-selected .ant-dropdown-menu-submenu-title{color:#1677ff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter.ant-slide-up-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear.ant-slide-up-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideUpIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-leave.ant-slide-up-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideUpOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear{transform:scale(0);transform-origin:0% 0%;opacity:0;animation-timing-function:cubic-bezier(0.23, 1, 0.32, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-appear-prepare{transform:scale(1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-up-leave{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter.ant-slide-down-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear.ant-slide-down-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideDownIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-leave.ant-slide-down-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antSlideDownOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear{transform:scale(0);transform-origin:0% 0%;opacity:0;animation-timing-function:cubic-bezier(0.23, 1, 0.32, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-appear-prepare{transform:scale(1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-slide-down-leave{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-enter.ant-move-up-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-appear.ant-move-up-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antMoveUpIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-leave.ant-move-up-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antMoveUpOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-appear{opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-up-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-enter.ant-move-down-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-appear.ant-move-down-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antMoveDownIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-leave.ant-move-down-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antMoveDownOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-appear{opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-move-down-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter.ant-zoom-big-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear.ant-zoom-big-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomBigIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-leave.ant-zoom-big-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomBigOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear{transform:scale(0);opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-appear-prepare{transform:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-big-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled),:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled){color:#ff4d4f;}:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled):hover,:where(.css-dev-only-do-not-override-qnu6hi).ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled):hover{color:#fff;background-color:#ff4d4f;}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antMoveUpIn">@keyframes css-dev-only-do-not-override-qnu6hi-antMoveUpIn{0%{transform:translate3d(0, -100%, 0);transform-origin:0 0;opacity:0;}100%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antMoveUpOut">@keyframes css-dev-only-do-not-override-qnu6hi-antMoveUpOut{0%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}100%{transform:translate3d(0, -100%, 0);transform-origin:0 0;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antMoveDownIn">@keyframes css-dev-only-do-not-override-qnu6hi-antMoveDownIn{0%{transform:translate3d(0, 100%, 0);transform-origin:0 0;opacity:0;}100%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antMoveDownOut">@keyframes css-dev-only-do-not-override-qnu6hi-antMoveDownOut{0%{transform:translate3d(0, 0, 0);transform-origin:0 0;opacity:1;}100%{transform:translate3d(0, 100%, 0);transform-origin:0 0;opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="i249q0" data-token-hash="ceha6z" data-cache-path="ceha6z|Modal-Modal|ant-modal|anticon">:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"]{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"] [class^="ant-modal"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"] [class^="ant-modal"],:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"] [class*=" ant-modal"],:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"] [class*=" ant-modal"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"] [class^="ant-modal"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"] [class^="ant-modal"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"] [class*=" ant-modal"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"] [class*=" ant-modal"]::before,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"] [class^="ant-modal"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"] [class^="ant-modal"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class^="ant-modal"] [class*=" ant-modal"]::after,:where(.css-dev-only-do-not-override-qnu6hi)[class*=" ant-modal"] [class*=" ant-modal"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-wrap-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-centered{text-align:center;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-centered::before{display:inline-block;width:0;height:100%;vertical-align:middle;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-centered .ant-modal{top:0;display:inline-block;padding-bottom:0;text-align:start;vertical-align:middle;}@media (max-width: 767px){:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal{max-width:calc(100vw - 16px);margin:8px auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-centered .ant-modal{flex:1;}}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal{box-sizing:border-box;margin:0 auto;padding:0;color:rgba(0, 0, 0, 0.88);font-size:14px;line-height:1.5714285714285714;list-style:none;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';pointer-events:none;position:relative;top:100px;width:auto;max-width:calc(100vw - 32px);padding-bottom:24px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-title{margin:0;color:rgba(0, 0, 0, 0.88);font-weight:600;font-size:16px;line-height:1.5;word-wrap:break-word;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-content{position:relative;background-color:#ffffff;background-clip:padding-box;border:0;border-radius:8px;box-shadow:0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05);pointer-events:auto;padding:20px 24px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-close{position:absolute;top:12px;inset-inline-end:12px;z-index:1010;padding:0;color:rgba(0, 0, 0, 0.45);font-weight:600;line-height:1;text-decoration:none;background:transparent;border-radius:4px;width:32px;height:32px;border:0;outline:0;cursor:pointer;transition:color 0.2s,background-color 0.2s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-close-x{display:flex;font-size:16px;font-style:normal;line-height:32px;justify-content:center;text-transform:none;text-rendering:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-close:hover{color:rgba(0, 0, 0, 0.88);background-color:rgba(0, 0, 0, 0.06);text-decoration:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-close:active{background-color:rgba(0, 0, 0, 0.15);}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-close:focus-visible{outline:4px solid #91caff;outline-offset:1px;transition:outline-offset 0s,outline 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-header{color:rgba(0, 0, 0, 0.88);background:#ffffff;border-radius:8px 8px 0 0;margin-bottom:8px;padding:0;border-bottom:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-body{font-size:14px;line-height:1.5714285714285714;word-wrap:break-word;padding:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-body .ant-modal-body-skeleton{width:100%;height:100%;display:flex;justify-content:center;align-items:center;margin:16px auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-footer{text-align:end;background:transparent;margin-top:12px;padding:0;border-top:none;border-radius:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-footer >.ant-btn+.ant-btn{margin-inline-start:8px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal .ant-modal-open{overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-pure-panel{top:auto;padding:0;display:flex;flex-direction:column;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-pure-panel .ant-modal-content,:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-pure-panel .ant-modal-body,:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-pure-panel .ant-modal-confirm-body-wrapper{display:flex;flex-direction:column;flex:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-pure-panel .ant-modal-confirm-body{margin-bottom:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-wrap-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-wrap-rtl .ant-modal-confirm-body{direction:rtl;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal.ant-zoom-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal.ant-zoom-appear{transform:none;opacity:0;animation-duration:0.3s;user-select:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal.ant-zoom-leave .ant-modal-content{pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-mask{position:fixed;inset:0;z-index:1000;height:100%;background-color:rgba(0, 0, 0, 0.45);pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-mask .ant-modal-hidden{display:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-modal-wrap{position:fixed;inset:0;z-index:1000;overflow:auto;outline:0;-webkit-overflow-scrolling:touch;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-enter.ant-fade-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-appear.ant-fade-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antFadeIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-leave.ant-fade-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antFadeOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-appear{opacity:0;animation-timing-function:linear;}:where(.css-dev-only-do-not-override-qnu6hi).ant-modal-root .ant-fade-leave{animation-timing-function:linear;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-appear{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-leave{animation-duration:0.2s;animation-fill-mode:both;animation-play-state:paused;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-enter.ant-zoom-enter-active,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-appear.ant-zoom-appear-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomIn;animation-play-state:running;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-leave.ant-zoom-leave-active{animation-name:css-dev-only-do-not-override-qnu6hi-antZoomOut;animation-play-state:running;pointer-events:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-enter,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-appear{transform:scale(0);opacity:0;animation-timing-function:cubic-bezier(0.08, 0.82, 0.17, 1);}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-enter-prepare,:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-appear-prepare{transform:none;}:where(.css-dev-only-do-not-override-qnu6hi).ant-zoom-leave{animation-timing-function:cubic-bezier(0.78, 0.14, 0.15, 0.86);}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antFadeIn">@keyframes css-dev-only-do-not-override-qnu6hi-antFadeIn{0%{opacity:0;}100%{opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antFadeOut">@keyframes css-dev-only-do-not-override-qnu6hi-antFadeOut{0%{opacity:1;}100%{opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antZoomIn">@keyframes css-dev-only-do-not-override-qnu6hi-antZoomIn{0%{transform:scale(0.2);opacity:0;}100%{transform:scale(1);opacity:1;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="_effect-css-dev-only-do-not-override-qnu6hi-antZoomOut">@keyframes css-dev-only-do-not-override-qnu6hi-antZoomOut{0%{transform:scale(1);}100%{transform:scale(0.2);opacity:0;}}</style><style data-rc-order="prependQueue" data-rc-priority="-999" data-css-hash="1dtn2b3" data-token-hash="ceha6z" data-cache-path="ceha6z|Layout-Layout|ant-layout|anticon">:where(.css-dev-only-do-not-override-qnu6hi).ant-layout{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';font-size:14px;box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout [class^="ant-layout"],:where(.css-dev-only-do-not-override-qnu6hi).ant-layout [class*=" ant-layout"]{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout [class^="ant-layout"]::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout [class*=" ant-layout"]::before,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout [class^="ant-layout"]::after,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout [class*=" ant-layout"]::after{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout{display:flex;flex:auto;flex-direction:column;min-height:0;background:#f5f5f5;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout *{box-sizing:border-box;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout.ant-layout-has-sider{flex-direction:row;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout.ant-layout-has-sider >.ant-layout,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout.ant-layout-has-sider >.ant-layout-content{width:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-header,:where(.css-dev-only-do-not-override-qnu6hi).ant-layout.ant-layout-footer{flex:0 0 auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider{position:relative;min-width:0;background:#001529;transition:all 0.2s,background 0s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-children{height:100%;margin-top:-0.1px;padding-top:0.1px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-children .ant-menu.ant-menu-inline-collapsed{width:auto;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-has-trigger{padding-bottom:48px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-right{order:1;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-trigger{position:fixed;bottom:0;z-index:1;height:48px;color:#fff;line-height:48px;text-align:center;background:#002140;cursor:pointer;transition:all 0.2s;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-zero-width >*{overflow:hidden;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-zero-width-trigger{position:absolute;top:64px;inset-inline-end:-40px;z-index:1;width:40px;height:40px;color:#fff;font-size:20px;display:flex;align-items:center;justify-content:center;background:#001529;border-start-start-radius:0;border-start-end-radius:6px;border-end-end-radius:6px;border-end-start-radius:0;cursor:pointer;transition:background 0.3s ease;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-zero-width-trigger::after{position:absolute;inset:0;background:transparent;transition:all 0.3s;content:"";}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-zero-width-trigger:hover::after{background:rgba(255, 255, 255, 0.2);}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-zero-width-trigger-right{inset-inline-start:-40px;border-start-start-radius:6px;border-start-end-radius:0;border-end-end-radius:0;border-end-start-radius:6px;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-light{background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-light .ant-layout-sider-trigger{color:rgba(0, 0, 0, 0.88);background:#ffffff;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout .ant-layout-sider-light .ant-layout-sider-zero-width-trigger{color:rgba(0, 0, 0, 0.88);background:#ffffff;border:1px solid #f5f5f5;border-inline-start:0;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout-rtl{direction:rtl;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout-header{height:64px;padding:0 50px;color:rgba(0, 0, 0, 0.88);line-height:64px;background:#001529;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout-header .ant-menu{line-height:inherit;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout-footer{padding:24px 50px;color:rgba(0, 0, 0, 0.88);font-size:14px;background:#f5f5f5;}:where(.css-dev-only-do-not-override-qnu6hi).ant-layout-content{flex:auto;color:rgba(0, 0, 0, 0.88);min-height:0;}</style><style data-rc-order="prependQueue" data-css-hash="1gvl60q" data-token-hash="ceha6z" data-cache-path="ceha6z|ant-design-icons|anticon">.anticon{display:inline-flex;align-items:center;color:inherit;font-style:normal;line-height:0;text-align:center;text-transform:none;vertical-align:-0.125em;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}.anticon >*{line-height:1;}.anticon svg{display:inline-block;}.anticon .anticon .anticon-icon{display:block;}</style>
    <script type="module">
import RefreshRuntime from "/@react-refresh"
RefreshRuntime.injectIntoGlobalHook(window)
window.$RefreshReg$ = () => {}
window.$RefreshSig$ = () => (type) => type
window.__vite_plugin_react_preamble_installed__ = true
</script>

    <script type="module" src="./Knox_files/client"></script>

    
    <link rel="icon" type="image/svg+xml" href="http://localhost:3000/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com/">
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
<link href="./Knox_files/css2" rel="stylesheet">
    <title>Knox</title>
  <style id="_goober"> @keyframes go2264125279{from{transform:scale(0) rotate(45deg);opacity:0;}to{transform:scale(1) rotate(45deg);opacity:1;}}@keyframes go3020080000{from{transform:scale(0);opacity:0;}to{transform:scale(1);opacity:1;}}@keyframes go463499852{from{transform:scale(0) rotate(90deg);opacity:0;}to{transform:scale(1) rotate(90deg);opacity:1;}}@keyframes go1268368563{from{transform:rotate(0deg);}to{transform:rotate(360deg);}}@keyframes go1310225428{from{transform:scale(0) rotate(45deg);opacity:0;}to{transform:scale(1) rotate(45deg);opacity:1;}}@keyframes go651618207{0%{height:0;width:0;opacity:0;}40%{height:0;width:6px;opacity:1;}100%{opacity:1;height:10px;}}@keyframes go901347462{from{transform:scale(0.6);opacity:0.4;}to{transform:scale(1);opacity:1;}}.go4109123758{z-index:9999;}.go4109123758 > *{pointer-events:auto;}</style><style type="text/css" data-vite-dev-id="/Users/<USER>/Documents/Reactjs Projects/admin-gym-react/node_modules/react-quill/dist/quill.snow.css">/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */
.ql-container {
  box-sizing: border-box;
  font-family: Helvetica, Arial, sans-serif;
  font-size: 13px;
  height: 100%;
  margin: 0px;
  position: relative;
}
.ql-container.ql-disabled .ql-tooltip {
  visibility: hidden;
}
.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
  pointer-events: none;
}
.ql-clipboard {
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  position: absolute;
  top: 50%;
}
.ql-clipboard p {
  margin: 0;
  padding: 0;
}
.ql-editor {
  box-sizing: border-box;
  line-height: 1.42;
  height: 100%;
  outline: none;
  overflow-y: auto;
  padding: 12px 15px;
  -o-tab-size: 4;
     tab-size: 4;
  -moz-tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-editor > * {
  cursor: text;
}
.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5em;
}
.ql-editor ol > li,
.ql-editor ul > li {
  list-style-type: none;
}
.ql-editor ul > li::before {
  content: '\2022';
}
.ql-editor ul[data-checked=true],
.ql-editor ul[data-checked=false] {
  pointer-events: none;
}
.ql-editor ul[data-checked=true] > li *,
.ql-editor ul[data-checked=false] > li * {
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before,
.ql-editor ul[data-checked=false] > li::before {
  color: #777;
  cursor: pointer;
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before {
  content: '\2611';
}
.ql-editor ul[data-checked=false] > li::before {
  content: '\2610';
}
.ql-editor li::before {
  display: inline-block;
  white-space: nowrap;
  width: 1.2em;
}
.ql-editor li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
  margin-right: 0.3em;
  text-align: right;
}
.ql-editor li.ql-direction-rtl::before {
  margin-left: 0.3em;
  margin-right: -1.5em;
}
.ql-editor ol li:not(.ql-direction-rtl),
.ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 1.5em;
}
.ql-editor ol li.ql-direction-rtl,
.ql-editor ul li.ql-direction-rtl {
  padding-right: 1.5em;
}
.ql-editor ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0;
}
.ql-editor ol li:before {
  content: counter(list-0, decimal) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
}
.ql-editor ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
}
.ql-editor ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-2 {
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
}
.ql-editor ol li.ql-indent-3:before {
  content: counter(list-3, decimal) '. ';
}
.ql-editor ol li.ql-indent-3 {
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
}
.ql-editor ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-4 {
  counter-reset: list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
}
.ql-editor ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-5 {
  counter-reset: list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
}
.ql-editor ol li.ql-indent-6:before {
  content: counter(list-6, decimal) '. ';
}
.ql-editor ol li.ql-indent-6 {
  counter-reset: list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
}
.ql-editor ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-7 {
  counter-reset: list-8 list-9;
}
.ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
}
.ql-editor ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-8 {
  counter-reset: list-9;
}
.ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9:before {
  content: counter(list-9, decimal) '. ';
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 3em;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 4.5em;
}
.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 3em;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 4.5em;
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 7.5em;
}
.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 7.5em;
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 9em;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 10.5em;
}
.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 9em;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 10.5em;
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 13.5em;
}
.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 13.5em;
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 15em;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 16.5em;
}
.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 15em;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 16.5em;
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 19.5em;
}
.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 19.5em;
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 21em;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 22.5em;
}
.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 21em;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 22.5em;
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 24em;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 25.5em;
}
.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 24em;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 25.5em;
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 27em;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 28.5em;
}
.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 27em;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 28.5em;
}
.ql-editor .ql-video {
  display: block;
  max-width: 100%;
}
.ql-editor .ql-video.ql-align-center {
  margin: 0 auto;
}
.ql-editor .ql-video.ql-align-right {
  margin: 0 0 0 auto;
}
.ql-editor .ql-bg-black {
  background-color: #000;
}
.ql-editor .ql-bg-red {
  background-color: #e60000;
}
.ql-editor .ql-bg-orange {
  background-color: #f90;
}
.ql-editor .ql-bg-yellow {
  background-color: #ff0;
}
.ql-editor .ql-bg-green {
  background-color: #008a00;
}
.ql-editor .ql-bg-blue {
  background-color: #06c;
}
.ql-editor .ql-bg-purple {
  background-color: #93f;
}
.ql-editor .ql-color-white {
  color: #fff;
}
.ql-editor .ql-color-red {
  color: #e60000;
}
.ql-editor .ql-color-orange {
  color: #f90;
}
.ql-editor .ql-color-yellow {
  color: #ff0;
}
.ql-editor .ql-color-green {
  color: #008a00;
}
.ql-editor .ql-color-blue {
  color: #06c;
}
.ql-editor .ql-color-purple {
  color: #93f;
}
.ql-editor .ql-font-serif {
  font-family: Georgia, Times New Roman, serif;
}
.ql-editor .ql-font-monospace {
  font-family: Monaco, Courier New, monospace;
}
.ql-editor .ql-size-small {
  font-size: 0.75em;
}
.ql-editor .ql-size-large {
  font-size: 1.5em;
}
.ql-editor .ql-size-huge {
  font-size: 2.5em;
}
.ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
.ql-editor .ql-align-center {
  text-align: center;
}
.ql-editor .ql-align-justify {
  text-align: justify;
}
.ql-editor .ql-align-right {
  text-align: right;
}
.ql-editor.ql-blank::before {
  color: rgba(0,0,0,0.6);
  content: attr(data-placeholder);
  font-style: italic;
  left: 15px;
  pointer-events: none;
  position: absolute;
  right: 15px;
}
.ql-snow.ql-toolbar:after,
.ql-snow .ql-toolbar:after {
  clear: both;
  content: '';
  display: table;
}
.ql-snow.ql-toolbar button,
.ql-snow .ql-toolbar button {
  background: none;
  border: none;
  cursor: pointer;
  display: inline-block;
  float: left;
  height: 24px;
  padding: 3px 5px;
  width: 28px;
}
.ql-snow.ql-toolbar button svg,
.ql-snow .ql-toolbar button svg {
  float: left;
  height: 100%;
}
.ql-snow.ql-toolbar button:active:hover,
.ql-snow .ql-toolbar button:active:hover {
  outline: none;
}
.ql-snow.ql-toolbar input.ql-image[type=file],
.ql-snow .ql-toolbar input.ql-image[type=file] {
  display: none;
}
.ql-snow.ql-toolbar button:hover,
.ql-snow .ql-toolbar button:hover,
.ql-snow.ql-toolbar button:focus,
.ql-snow .ql-toolbar button:focus,
.ql-snow.ql-toolbar button.ql-active,
.ql-snow .ql-toolbar button.ql-active,
.ql-snow.ql-toolbar .ql-picker-label:hover,
.ql-snow .ql-toolbar .ql-picker-label:hover,
.ql-snow.ql-toolbar .ql-picker-label.ql-active,
.ql-snow .ql-toolbar .ql-picker-label.ql-active,
.ql-snow.ql-toolbar .ql-picker-item:hover,
.ql-snow .ql-toolbar .ql-picker-item:hover,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  color: #06c;
}
.ql-snow.ql-toolbar button:hover .ql-fill,
.ql-snow .ql-toolbar button:hover .ql-fill,
.ql-snow.ql-toolbar button:focus .ql-fill,
.ql-snow .ql-toolbar button:focus .ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,
.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {
  fill: #06c;
}
.ql-snow.ql-toolbar button:hover .ql-stroke,
.ql-snow .ql-toolbar button:hover .ql-stroke,
.ql-snow.ql-toolbar button:focus .ql-stroke,
.ql-snow .ql-toolbar button:focus .ql-stroke,
.ql-snow.ql-toolbar button.ql-active .ql-stroke,
.ql-snow .ql-toolbar button.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,
.ql-snow.ql-toolbar button:hover .ql-stroke-miter,
.ql-snow .ql-toolbar button:hover .ql-stroke-miter,
.ql-snow.ql-toolbar button:focus .ql-stroke-miter,
.ql-snow .ql-toolbar button:focus .ql-stroke-miter,
.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,
.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,
.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {
  stroke: #06c;
}
@media (pointer: coarse) {
  .ql-snow.ql-toolbar button:hover:not(.ql-active),
  .ql-snow .ql-toolbar button:hover:not(.ql-active) {
    color: #444;
  }
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {
    fill: #444;
  }
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,
  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,
  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {
    stroke: #444;
  }
}
.ql-snow {
  box-sizing: border-box;
}
.ql-snow * {
  box-sizing: border-box;
}
.ql-snow .ql-hidden {
  display: none;
}
.ql-snow .ql-out-bottom,
.ql-snow .ql-out-top {
  visibility: hidden;
}
.ql-snow .ql-tooltip {
  position: absolute;
  transform: translateY(10px);
}
.ql-snow .ql-tooltip a {
  cursor: pointer;
  text-decoration: none;
}
.ql-snow .ql-tooltip.ql-flip {
  transform: translateY(-10px);
}
.ql-snow .ql-formats {
  display: inline-block;
  vertical-align: middle;
}
.ql-snow .ql-formats:after {
  clear: both;
  content: '';
  display: table;
}
.ql-snow .ql-stroke {
  fill: none;
  stroke: #444;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}
.ql-snow .ql-stroke-miter {
  fill: none;
  stroke: #444;
  stroke-miterlimit: 10;
  stroke-width: 2;
}
.ql-snow .ql-fill,
.ql-snow .ql-stroke.ql-fill {
  fill: #444;
}
.ql-snow .ql-empty {
  fill: none;
}
.ql-snow .ql-even {
  fill-rule: evenodd;
}
.ql-snow .ql-thin,
.ql-snow .ql-stroke.ql-thin {
  stroke-width: 1;
}
.ql-snow .ql-transparent {
  opacity: 0.4;
}
.ql-snow .ql-direction svg:last-child {
  display: none;
}
.ql-snow .ql-direction.ql-active svg:last-child {
  display: inline;
}
.ql-snow .ql-direction.ql-active svg:first-child {
  display: none;
}
.ql-snow .ql-editor h1 {
  font-size: 2em;
}
.ql-snow .ql-editor h2 {
  font-size: 1.5em;
}
.ql-snow .ql-editor h3 {
  font-size: 1.17em;
}
.ql-snow .ql-editor h4 {
  font-size: 1em;
}
.ql-snow .ql-editor h5 {
  font-size: 0.83em;
}
.ql-snow .ql-editor h6 {
  font-size: 0.67em;
}
.ql-snow .ql-editor a {
  text-decoration: underline;
}
.ql-snow .ql-editor blockquote {
  border-left: 4px solid #ccc;
  margin-bottom: 5px;
  margin-top: 5px;
  padding-left: 16px;
}
.ql-snow .ql-editor code,
.ql-snow .ql-editor pre {
  background-color: #f0f0f0;
  border-radius: 3px;
}
.ql-snow .ql-editor pre {
  white-space: pre-wrap;
  margin-bottom: 5px;
  margin-top: 5px;
  padding: 5px 10px;
}
.ql-snow .ql-editor code {
  font-size: 85%;
  padding: 2px 4px;
}
.ql-snow .ql-editor pre.ql-syntax {
  background-color: #23241f;
  color: #f8f8f2;
  overflow: visible;
}
.ql-snow .ql-editor img {
  max-width: 100%;
}
.ql-snow .ql-picker {
  color: #444;
  display: inline-block;
  float: left;
  font-size: 14px;
  font-weight: 500;
  height: 24px;
  position: relative;
  vertical-align: middle;
}
.ql-snow .ql-picker-label {
  cursor: pointer;
  display: inline-block;
  height: 100%;
  padding-left: 8px;
  padding-right: 2px;
  position: relative;
  width: 100%;
}
.ql-snow .ql-picker-label::before {
  display: inline-block;
  line-height: 22px;
}
.ql-snow .ql-picker-options {
  background-color: #fff;
  display: none;
  min-width: 100%;
  padding: 4px 8px;
  position: absolute;
  white-space: nowrap;
}
.ql-snow .ql-picker-options .ql-picker-item {
  cursor: pointer;
  display: block;
  padding-bottom: 5px;
  padding-top: 5px;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: #ccc;
  z-index: 2;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: #ccc;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: #ccc;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  display: block;
  margin-top: -1px;
  top: 100%;
  z-index: 1;
}
.ql-snow .ql-color-picker,
.ql-snow .ql-icon-picker {
  width: 28px;
}
.ql-snow .ql-color-picker .ql-picker-label,
.ql-snow .ql-icon-picker .ql-picker-label {
  padding: 2px 4px;
}
.ql-snow .ql-color-picker .ql-picker-label svg,
.ql-snow .ql-icon-picker .ql-picker-label svg {
  right: 4px;
}
.ql-snow .ql-icon-picker .ql-picker-options {
  padding: 4px 0px;
}
.ql-snow .ql-icon-picker .ql-picker-item {
  height: 24px;
  width: 24px;
  padding: 2px 4px;
}
.ql-snow .ql-color-picker .ql-picker-options {
  padding: 3px 5px;
  width: 152px;
}
.ql-snow .ql-color-picker .ql-picker-item {
  border: 1px solid transparent;
  float: left;
  height: 16px;
  margin: 2px;
  padding: 0px;
  width: 16px;
}
.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  position: absolute;
  margin-top: -9px;
  right: 0;
  top: 50%;
  width: 18px;
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=''])::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=''])::before {
  content: attr(data-label);
}
.ql-snow .ql-picker.ql-header {
  width: 98px;
}
.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: 'Normal';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: 'Heading 1';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: 'Heading 2';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: 'Heading 3';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: 'Heading 4';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: 'Heading 5';
}
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: 'Heading 6';
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  font-size: 2em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  font-size: 1.5em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  font-size: 1.17em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  font-size: 1em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  font-size: 0.83em;
}
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  font-size: 0.67em;
}
.ql-snow .ql-picker.ql-font {
  width: 108px;
}
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: 'Sans Serif';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: 'Serif';
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: 'Monospace';
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  font-family: Georgia, Times New Roman, serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  font-family: Monaco, Courier New, monospace;
}
.ql-snow .ql-picker.ql-size {
  width: 98px;
}
.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: 'Normal';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: 'Small';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: 'Large';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: 'Huge';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  font-size: 10px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  font-size: 18px;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  font-size: 32px;
}
.ql-snow .ql-color-picker.ql-background .ql-picker-item {
  background-color: #fff;
}
.ql-snow .ql-color-picker.ql-color .ql-picker-item {
  background-color: #000;
}
.ql-toolbar.ql-snow {
  border: 1px solid #ccc;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  padding: 8px;
}
.ql-toolbar.ql-snow .ql-formats {
  margin-right: 15px;
}
.ql-toolbar.ql-snow .ql-picker-label {
  border: 1px solid transparent;
}
.ql-toolbar.ql-snow .ql-picker-options {
  border: 1px solid transparent;
  box-shadow: rgba(0,0,0,0.2) 0 2px 8px;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: #ccc;
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border-color: #ccc;
}
.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,
.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {
  border-color: #000;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border-top: 0px;
}
.ql-snow .ql-tooltip {
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0px 0px 5px #ddd;
  color: #444;
  padding: 5px 12px;
  white-space: nowrap;
}
.ql-snow .ql-tooltip::before {
  content: "Visit URL:";
  line-height: 26px;
  margin-right: 8px;
}
.ql-snow .ql-tooltip input[type=text] {
  display: none;
  border: 1px solid #ccc;
  font-size: 13px;
  height: 26px;
  margin: 0px;
  padding: 3px 5px;
  width: 170px;
}
.ql-snow .ql-tooltip a.ql-preview {
  display: inline-block;
  max-width: 200px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
}
.ql-snow .ql-tooltip a.ql-action::after {
  border-right: 1px solid #ccc;
  content: 'Edit';
  margin-left: 16px;
  padding-right: 8px;
}
.ql-snow .ql-tooltip a.ql-remove::before {
  content: 'Remove';
  margin-left: 8px;
}
.ql-snow .ql-tooltip a {
  line-height: 26px;
}
.ql-snow .ql-tooltip.ql-editing a.ql-preview,
.ql-snow .ql-tooltip.ql-editing a.ql-remove {
  display: none;
}
.ql-snow .ql-tooltip.ql-editing input[type=text] {
  display: inline-block;
}
.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: 'Save';
  padding-right: 0px;
}
.ql-snow .ql-tooltip[data-mode=link]::before {
  content: "Enter link:";
}
.ql-snow .ql-tooltip[data-mode=formula]::before {
  content: "Enter formula:";
}
.ql-snow .ql-tooltip[data-mode=video]::before {
  content: "Enter video:";
}
.ql-snow a {
  color: #06c;
}
.ql-container.ql-snow {
  border: 1px solid #ccc;
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/Documents/Reactjs Projects/admin-gym-react/node_modules/react-big-calendar/lib/css/react-big-calendar.css">@charset "UTF-8";
.rbc-btn {
  color: inherit;
  font: inherit;
  margin: 0;
}

button.rbc-btn {
  overflow: visible;
  text-transform: none;
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled].rbc-btn {
  cursor: not-allowed;
}

button.rbc-input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.rbc-calendar {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.rbc-calendar *,
.rbc-calendar *:before,
.rbc-calendar *:after {
  box-sizing: inherit;
}

.rbc-abs-full, .rbc-row-bg {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.rbc-ellipsis, .rbc-show-more, .rbc-row-segment .rbc-event-content, .rbc-event-label {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rbc-rtl {
  direction: rtl;
}

.rbc-off-range {
  color: #999999;
}

.rbc-off-range-bg {
  background: #e6e6e6;
}

.rbc-header {
  overflow: hidden;
  flex: 1 0 0%;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 3px;
  text-align: center;
  vertical-align: middle;
  font-weight: bold;
  font-size: 90%;
  min-height: 0;
  border-bottom: 1px solid #ddd;
}
.rbc-header + .rbc-header {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-header + .rbc-header {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-header > a, .rbc-header > a:active, .rbc-header > a:visited {
  color: inherit;
  text-decoration: none;
}

.rbc-button-link {
  color: inherit;
  background: none;
  margin: 0;
  padding: 0;
  border: none;
  cursor: pointer;
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
}

.rbc-row-content {
  position: relative;
  -moz-user-select: none;
       user-select: none;
  -webkit-user-select: none;
  z-index: 4;
}

.rbc-row-content-scrollable {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.rbc-row-content-scrollable .rbc-row-content-scroll-container {
  height: 100%;
  overflow-y: scroll; /* IE and Edge */ /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  /* Hide scrollbar for Chrome, Safari and Opera */
}
.rbc-row-content-scrollable .rbc-row-content-scroll-container::-webkit-scrollbar {
  display: none;
}

.rbc-today {
  background-color: #eaf6ff;
}

.rbc-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
}
.rbc-toolbar .rbc-toolbar-label {
  flex-grow: 1;
  padding: 0 10px;
  text-align: center;
}
.rbc-toolbar button {
  color: #373a3c;
  display: inline-block;
  margin: 0;
  text-align: center;
  vertical-align: middle;
  background: none;
  background-image: none;
  border: 1px solid #ccc;
  padding: 0.375rem 1rem;
  border-radius: 4px;
  line-height: normal;
  white-space: nowrap;
}
.rbc-toolbar button:active, .rbc-toolbar button.rbc-active {
  background-image: none;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  background-color: #e6e6e6;
  border-color: #adadad;
}
.rbc-toolbar button:active:hover, .rbc-toolbar button:active:focus, .rbc-toolbar button.rbc-active:hover, .rbc-toolbar button.rbc-active:focus {
  color: #373a3c;
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}
.rbc-toolbar button:focus {
  color: #373a3c;
  background-color: #e6e6e6;
  border-color: #adadad;
}
.rbc-toolbar button:hover {
  color: #373a3c;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.rbc-btn-group {
  display: inline-block;
  white-space: nowrap;
}
.rbc-btn-group > button:first-child:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rbc-btn-group > button:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rbc-rtl .rbc-btn-group > button:first-child:not(:last-child) {
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rbc-rtl .rbc-btn-group > button:last-child:not(:first-child) {
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rbc-btn-group > button:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.rbc-btn-group button + button {
  margin-left: -1px;
}
.rbc-rtl .rbc-btn-group button + button {
  margin-left: 0;
  margin-right: -1px;
}
.rbc-btn-group + .rbc-btn-group, .rbc-btn-group + button {
  margin-left: 10px;
}

@media (max-width: 767px) {
  .rbc-toolbar {
    flex-direction: column;
  }
}
.rbc-event, .rbc-day-slot .rbc-background-event {
  border: none;
  box-sizing: border-box;
  box-shadow: none;
  margin: 0;
  padding: 2px 5px;
  background-color: #3174ad;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  width: 100%;
  text-align: left;
}
.rbc-slot-selecting .rbc-event, .rbc-slot-selecting .rbc-day-slot .rbc-background-event, .rbc-day-slot .rbc-slot-selecting .rbc-background-event {
  cursor: inherit;
  pointer-events: none;
}
.rbc-event.rbc-selected, .rbc-day-slot .rbc-selected.rbc-background-event {
  background-color: #265985;
}
.rbc-event:focus, .rbc-day-slot .rbc-background-event:focus {
  outline: 5px auto #3b99fc;
}

.rbc-event-label {
  font-size: 80%;
}

.rbc-event-overlaps {
  box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);
}

.rbc-event-continues-prior {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rbc-event-continues-after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-event-continues-earlier {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.rbc-event-continues-later {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-row {
  display: flex;
  flex-direction: row;
}

.rbc-row-segment {
  padding: 0 1px 1px 1px;
}
.rbc-selected-cell {
  background-color: rgba(0, 0, 0, 0.1);
}

.rbc-show-more {
  background-color: rgba(255, 255, 255, 0.3);
  z-index: 4;
  font-weight: bold;
  font-size: 85%;
  height: auto;
  line-height: normal;
  color: #3174ad;
}
.rbc-show-more:hover, .rbc-show-more:focus {
  color: #265985;
}

.rbc-month-view {
  position: relative;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  flex: 1 0 0;
  width: 100%;
  -moz-user-select: none;
       user-select: none;
  -webkit-user-select: none;
  height: 100%;
}

.rbc-month-header {
  display: flex;
  flex-direction: row;
}

.rbc-month-row {
  display: flex;
  position: relative;
  flex-direction: column;
  flex: 1 0 0;
  flex-basis: 0px;
  overflow: hidden;
  height: 100%;
}
.rbc-month-row + .rbc-month-row {
  border-top: 1px solid #ddd;
}

.rbc-date-cell {
  flex: 1 1 0;
  min-width: 0;
  padding-right: 5px;
  text-align: right;
}
.rbc-date-cell.rbc-now {
  font-weight: bold;
}
.rbc-date-cell > a, .rbc-date-cell > a:active, .rbc-date-cell > a:visited {
  color: inherit;
  text-decoration: none;
}

.rbc-row-bg {
  display: flex;
  flex-direction: row;
  flex: 1 0 0;
  overflow: hidden;
}

.rbc-day-bg {
  flex: 1 0 0%;
}
.rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-day-bg + .rbc-day-bg {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}

.rbc-overlay {
  position: absolute;
  z-index: 5;
  border: 1px solid #e5e5e5;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
  padding: 10px;
}
.rbc-overlay > * + * {
  margin-top: 1px;
}

.rbc-overlay-header {
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -10px 5px -10px;
  padding: 2px 10px;
}

.rbc-agenda-view {
  display: flex;
  flex-direction: column;
  flex: 1 0 0;
  overflow: auto;
}
.rbc-agenda-view table.rbc-agenda-table {
  width: 100%;
  border: 1px solid #ddd;
  border-spacing: 0;
  border-collapse: collapse;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr > td {
  padding: 5px 10px;
  vertical-align: top;
}
.rbc-agenda-view table.rbc-agenda-table .rbc-agenda-time-cell {
  padding-left: 15px;
  padding-right: 15px;
  text-transform: lowercase;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-agenda-view table.rbc-agenda-table tbody > tr + tr {
  border-top: 1px solid #ddd;
}
.rbc-agenda-view table.rbc-agenda-table thead > tr > th {
  padding: 3px 5px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}
.rbc-rtl .rbc-agenda-view table.rbc-agenda-table thead > tr > th {
  text-align: right;
}

.rbc-agenda-time-cell {
  text-transform: lowercase;
}
.rbc-agenda-time-cell .rbc-continues-after:after {
  content: " »";
}
.rbc-agenda-time-cell .rbc-continues-prior:before {
  content: "« ";
}

.rbc-agenda-date-cell,
.rbc-agenda-time-cell {
  white-space: nowrap;
}

.rbc-agenda-event-cell {
  width: 100%;
}

.rbc-time-column {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
.rbc-time-column .rbc-timeslot-group {
  flex: 1;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #ddd;
  min-height: 40px;
  display: flex;
  flex-flow: column nowrap;
}

.rbc-time-gutter,
.rbc-header-gutter {
  flex: none;
}

.rbc-label {
  padding: 0 5px;
}

.rbc-day-slot {
  position: relative;
}
.rbc-day-slot .rbc-events-container {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  margin-right: 10px;
  top: 0;
}
.rbc-day-slot .rbc-events-container.rbc-rtl {
  left: 10px;
  right: 0;
}
.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {
  border: 1px solid #265985;
  display: flex;
  max-height: 100%;
  min-height: 20px;
  flex-flow: column wrap;
  align-items: flex-start;
  overflow: hidden;
  position: absolute;
}
.rbc-day-slot .rbc-background-event {
  opacity: 0.75;
}
.rbc-day-slot .rbc-event-label {
  flex: none;
  padding-right: 5px;
  width: auto;
}
.rbc-day-slot .rbc-event-content {
  width: 100%;
  flex: 1 1 0;
  word-wrap: break-word;
  line-height: 1;
  height: 100%;
  min-height: 1em;
}
.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #f7f7f7;
}

.rbc-time-view-resources .rbc-time-gutter,
.rbc-time-view-resources .rbc-time-header-gutter {
  position: sticky;
  left: 0;
  background-color: white;
  border-right: 1px solid #ddd;
  z-index: 10;
  margin-right: -1px;
}
.rbc-time-view-resources .rbc-time-header {
  overflow: hidden;
}
.rbc-time-view-resources .rbc-time-header-content {
  min-width: auto;
  flex: 1 0 0;
  flex-basis: 0px;
}
.rbc-time-view-resources .rbc-time-header-cell-single-day {
  display: none;
}
.rbc-time-view-resources .rbc-day-slot {
  min-width: 140px;
}
.rbc-time-view-resources .rbc-header,
.rbc-time-view-resources .rbc-day-bg {
  width: 140px;
  flex: 1 1 0;
  flex-basis: 0 px;
}

.rbc-time-header-content + .rbc-time-header-content {
  margin-left: -1px;
}

.rbc-time-slot {
  flex: 1 0 0;
}
.rbc-time-slot.rbc-now {
  font-weight: bold;
}

.rbc-day-header {
  text-align: center;
}

.rbc-slot-selection {
  z-index: 10;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 75%;
  width: 100%;
  padding: 3px;
}

.rbc-slot-selecting {
  cursor: move;
}

.rbc-time-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  border: 1px solid #ddd;
  min-height: 0;
}
.rbc-time-view .rbc-time-gutter {
  white-space: nowrap;
  text-align: right;
}
.rbc-time-view .rbc-allday-cell {
  box-sizing: content-box;
  width: 100%;
  height: 100%;
  position: relative;
}
.rbc-time-view .rbc-allday-cell + .rbc-allday-cell {
  border-left: 1px solid #ddd;
}
.rbc-time-view .rbc-allday-events {
  position: relative;
  z-index: 4;
}
.rbc-time-view .rbc-row {
  box-sizing: border-box;
  min-height: 20px;
}

.rbc-time-header {
  display: flex;
  flex: 0 0 auto;
  flex-direction: row;
}
.rbc-time-header.rbc-overflowing {
  border-right: 1px solid #ddd;
}
.rbc-rtl .rbc-time-header.rbc-overflowing {
  border-right-width: 0;
  border-left: 1px solid #ddd;
}
.rbc-time-header > .rbc-row:first-child {
  border-bottom: 1px solid #ddd;
}
.rbc-time-header > .rbc-row.rbc-row-resource {
  border-bottom: 1px solid #ddd;
}

.rbc-time-header-cell-single-day {
  display: none;
}

.rbc-time-header-content {
  flex: 1;
  display: flex;
  min-width: 0;
  flex-direction: column;
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-time-header-content {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-time-header-content > .rbc-row.rbc-row-resource {
  border-bottom: 1px solid #ddd;
  flex-shrink: 0;
}

.rbc-time-content {
  display: flex;
  flex: 1 0 0%;
  align-items: flex-start;
  width: 100%;
  border-top: 2px solid #ddd;
  overflow-y: auto;
  position: relative;
}
.rbc-time-content > .rbc-time-gutter {
  flex: none;
}
.rbc-time-content > * + * > * {
  border-left: 1px solid #ddd;
}
.rbc-rtl .rbc-time-content > * + * > * {
  border-left-width: 0;
  border-right: 1px solid #ddd;
}
.rbc-time-content > .rbc-day-slot {
  width: 100%;
  -moz-user-select: none;
       user-select: none;
  -webkit-user-select: none;
}

.rbc-current-time-indicator {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #74ad31;
  pointer-events: none;
}

                                                  </style><style type="text/css" data-vite-dev-id="/Users/<USER>/Documents/Reactjs Projects/admin-gym-react/node_modules/antd/dist/reset.css">/* stylelint-disable */
html,
body {
  width: 100%;
  height: 100%;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  margin: 0;
}
[tabindex='-1']:focus {
  outline: none;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
}
p {
  margin-top: 0;
  margin-bottom: 1em;
}
abbr[title],
abbr[data-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline;
  text-decoration: underline dotted;
  border-bottom: 0;
  cursor: help;
}
address {
  margin-bottom: 1em;
  font-style: normal;
  line-height: inherit;
}
input[type='text'],
input[type='password'],
input[type='number'],
textarea {
  -webkit-appearance: none;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1em;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 500;
}
dd {
  margin-bottom: 0.5em;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1em;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
pre,
code,
kbd,
samp {
  font-size: 1em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}
pre {
  margin-top: 0;
  margin-bottom: 1em;
  overflow: auto;
}
figure {
  margin: 0 0 1em;
}
img {
  vertical-align: middle;
  border-style: none;
}
a,
area,
button,
[role='button'],
input:not([type='range']),
label,
select,
summary,
textarea {
  touch-action: manipulation;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75em;
  padding-bottom: 0.3em;
  text-align: left;
  caption-side: bottom;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type='radio'],
input[type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}
input[type='date'],
input[type='time'],
input[type='datetime-local'],
input[type='month'] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 0.5em;
  padding: 0;
  color: inherit;
  font-size: 1.5em;
  line-height: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}
[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
mark {
  padding: 0.2em;
  background-color: #feffe6;
}
</style><style type="text/css" data-vite-dev-id="/Users/<USER>/Documents/Reactjs Projects/admin-gym-react/src/styles/globals.css">/* "base": for resetting the css and style the native elements */
/* ! tailwindcss v3.4.10 | MIT License | https://tailwindcss.com */
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
/* "components": for style your own custom classes */
/* always set this like this html and body element */
html,
    body {
        background-color: #ffffff;
        font-family: 'Roboto', sans-serif;
        min-height: 100vh;
        width: 100%;
    }
html {
        font-size: 62.5%;
    }
body {
        font-size: 1.6rem;
        font-family: 'Roboto', sans-serif !important;
    }
.font-semibold {
        font-weight: 600;
        font-family: 'Roboto', sans-serif;
    }
.checkbox-text {
        font-family: 'Roboto', sans-serif;
    }
h4 {
        /* font-size: 1.35vw !important; */
        font-weight: bold;
        font-family: 'Roboto', sans-serif !important;
        margin-bottom: 0 !important;
    }
label {
        font-family: 'Roboto', sans-serif !important;
    }
input {
        font-family: 'Roboto', sans-serif !important;
    }
div {
        font-family: 'Roboto', sans-serif !important;
    }
p {
        font-family: 'Roboto', sans-serif !important;
    }
.custom-collapse .ant-collapse-content {
        background-color: #f8f8f8;
        /* Replace with your desired color */
    }
#root {
        height: 100vh;
        width: 100%;
    }
/* min-height is not inherited by the child  so we have replace the min-height by the height property */
/* "utilities": for styling of the classes provided by the tailwind */
.visible {
  visibility: visible !important;
}
.static {
  position: static !important;
}
.absolute {
  position: absolute !important;
}
.relative {
  position: relative !important;
}
.sticky {
  position: sticky !important;
}
.bottom-0 {
  bottom: 0px !important;
}
.bottom-\[5\%\] {
  bottom: 5% !important;
}
.left-0 {
  left: 0px !important;
}
.right-0 {
  right: 0px !important;
}
.top-0 {
  top: 0px !important;
}
.z-10 {
  z-index: 10 !important;
}
.z-20 {
  z-index: 20 !important;
}
.m-0 {
  margin: 0px !important;
}
.m-2 {
  margin: 0.5rem !important;
}
.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}
.my-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.my-10 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}
.my-5 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}
.mb-10 {
  margin-bottom: 2.5rem !important;
}
.mb-12 {
  margin-bottom: 3rem !important;
}
.mb-2 {
  margin-bottom: 0.5rem !important;
}
.mb-4 {
  margin-bottom: 1rem !important;
}
.mb-6 {
  margin-bottom: 1.5rem !important;
}
.mb-8 {
  margin-bottom: 2rem !important;
}
.me-3 {
  margin-inline-end: 0.75rem !important;
}
.me-6 {
  margin-inline-end: 1.5rem !important;
}
.ml-12 {
  margin-left: 3rem !important;
}
.ml-2 {
  margin-left: 0.5rem !important;
}
.ms-3 {
  margin-inline-start: 0.75rem !important;
}
.ms-4 {
  margin-inline-start: 1rem !important;
}
.ms-auto {
  margin-inline-start: auto !important;
}
.mt-10 {
  margin-top: 2.5rem !important;
}
.mt-14 {
  margin-top: 3.5rem !important;
}
.mt-16 {
  margin-top: 4rem !important;
}
.mt-2 {
  margin-top: 0.5rem !important;
}
.mt-3 {
  margin-top: 0.75rem !important;
}
.mt-4 {
  margin-top: 1rem !important;
}
.mt-5 {
  margin-top: 1.25rem !important;
}
.mt-6 {
  margin-top: 1.5rem !important;
}
.mt-7 {
  margin-top: 1.75rem !important;
}
.mt-8 {
  margin-top: 2rem !important;
}
.-mt-4 {
  margin-top: -1rem !important;
}
.mb-7 {
  margin-bottom: 1.75rem !important;
}
.block {
  display: block !important;
}
.inline {
  display: inline !important;
}
.flex {
  display: flex !important;
}
.inline-flex {
  display: inline-flex !important;
}
.grid {
  display: grid !important;
}
.hidden {
  display: none !important;
}
.h-16 {
  height: 4rem !important;
}
.h-20 {
  height: 5rem !important;
}
.h-24 {
  height: 6rem !important;
}
.h-52 {
  height: 13rem !important;
}
.h-6 {
  height: 1.5rem !important;
}
.h-\[100vh\] {
  height: 100vh !important;
}
.h-\[10px\] {
  height: 10px !important;
}
.h-\[15px\] {
  height: 15px !important;
}
.h-\[20px\] {
  height: 20px !important;
}
.h-\[20vh\] {
  height: 20vh !important;
}
.h-\[40px\] {
  height: 40px !important;
}
.h-\[90vh\] {
  height: 90vh !important;
}
.h-full {
  height: 100% !important;
}
.h-screen {
  height: 100vh !important;
}
.h-9 {
  height: 2.25rem !important;
}
.max-h-\[25px\] {
  max-height: 25px !important;
}
.max-h-full {
  max-height: 100% !important;
}
.min-h-screen {
  min-height: 100vh !important;
}
.w-1\/2 {
  width: 50% !important;
}
.w-1\/3 {
  width: 33.333333% !important;
}
.w-12 {
  width: 3rem !important;
}
.w-52 {
  width: 13rem !important;
}
.w-6 {
  width: 1.5rem !important;
}
.w-\[100\%\] {
  width: 100% !important;
}
.w-\[110px\] {
  width: 110px !important;
}
.w-\[120px\] {
  width: 120px !important;
}
.w-\[15\%\] {
  width: 15% !important;
}
.w-\[20\%\] {
  width: 20% !important;
}
.w-\[250px\] {
  width: 250px !important;
}
.w-\[25vw\] {
  width: 25vw !important;
}
.w-\[30vw\] {
  width: 30vw !important;
}
.w-\[45px\] {
  width: 45px !important;
}
.w-\[77\%\] {
  width: 77% !important;
}
.w-\[85\%\] {
  width: 85% !important;
}
.w-\[90\%\] {
  width: 90% !important;
}
.w-auto {
  width: auto !important;
}
.w-fit {
  width: -moz-fit-content !important;
  width: fit-content !important;
}
.w-full {
  width: 100% !important;
}
.w-max {
  width: -moz-max-content !important;
  width: max-content !important;
}
.w-\[23\%\] {
  width: 23% !important;
}
.w-\[4\%\] {
  width: 4% !important;
}
.w-\[40\%\] {
  width: 40% !important;
}
.w-\[48\%\] {
  width: 48% !important;
}
.w-\[90px\] {
  width: 90px !important;
}
.min-w-min {
  min-width: -moz-min-content !important;
  min-width: min-content !important;
}
.max-w-full {
  max-width: 100% !important;
}
.cursor-pointer {
  cursor: pointer !important;
}
.resize {
  resize: both !important;
}
.list-disc {
  list-style-type: disc !important;
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
}
.flex-row {
  flex-direction: row !important;
}
.flex-col {
  flex-direction: column !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.items-end {
  align-items: flex-end !important;
}
.items-center {
  align-items: center !important;
}
.justify-start {
  justify-content: flex-start !important;
}
.justify-end {
  justify-content: flex-end !important;
}
.justify-center {
  justify-content: center !important;
}
.justify-between {
  justify-content: space-between !important;
}
.justify-around {
  justify-content: space-around !important;
}
.gap-10 {
  gap: 2.5rem !important;
}
.gap-12 {
  gap: 3rem !important;
}
.gap-14 {
  gap: 3.5rem !important;
}
.gap-16 {
  gap: 4rem !important;
}
.gap-2 {
  gap: 0.5rem !important;
}
.gap-2\.5 {
  gap: 0.625rem !important;
}
.gap-3 {
  gap: 0.75rem !important;
}
.gap-4 {
  gap: 1rem !important;
}
.gap-5 {
  gap: 1.25rem !important;
}
.gap-7 {
  gap: 1.75rem !important;
}
.gap-8 {
  gap: 2rem !important;
}
.gap-1 {
  gap: 0.25rem !important;
}
.gap-x-10 {
  -moz-column-gap: 2.5rem !important;
       column-gap: 2.5rem !important;
}
.gap-y-5 {
  row-gap: 1.25rem !important;
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0 !important;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))) !important;
  margin-bottom: calc(1rem * var(--tw-space-y-reverse)) !important;
}
.overflow-hidden {
  overflow: hidden !important;
}
.overflow-x-auto {
  overflow-x: auto !important;
}
.overflow-y-auto {
  overflow-y: auto !important;
}
.overflow-y-scroll {
  overflow-y: scroll !important;
}
.break-words {
  overflow-wrap: break-word !important;
}
.rounded {
  border-radius: 0.25rem !important;
}
.rounded-2xl {
  border-radius: 1rem !important;
}
.rounded-3xl {
  border-radius: 1.5rem !important;
}
.rounded-\[6px\] {
  border-radius: 6px !important;
}
.rounded-full {
  border-radius: 9999px !important;
}
.rounded-lg {
  border-radius: 0.5rem !important;
}
.rounded-md {
  border-radius: 0.375rem !important;
}
.rounded-xl {
  border-radius: 0.75rem !important;
}
.rounded-sm {
  border-radius: 0.125rem !important;
}
.border {
  border-width: 1px !important;
}
.border-0 {
  border-width: 0px !important;
}
.border-1 {
  border-width: 1px !important;
}
.border-2 {
  border-width: 2px !important;
}
.border-b {
  border-bottom-width: 1px !important;
}
.border-b-1 {
  border-bottom-width: 1px !important;
}
.border-b-2 {
  border-bottom-width: 2px !important;
}
.border-l {
  border-left-width: 1px !important;
}
.border-\[\#1A3353\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(26 51 83 / var(--tw-border-opacity)) !important;
}
.border-\[\#3E79F7\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(62 121 247 / var(--tw-border-opacity)) !important;
}
.border-\[\#406CF9\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(64 108 249 / var(--tw-border-opacity)) !important;
}
.border-\[\#E6EBF1\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(230 235 241 / var(--tw-border-opacity)) !important;
}
.border-gray-300 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(209 213 219 / var(--tw-border-opacity)) !important;
}
.border-\[\#1a3353\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(26 51 83 / var(--tw-border-opacity)) !important;
}
.border-b-gray-200 {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
}
.bg-\[\#2C2C2E\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(44 44 46 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#3E79F7\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(62 121 247 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#D0D4D7\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(208 212 215 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#D9D9D9\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(217 217 217 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#E7ECED\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(231 236 237 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#EFE1E0\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 225 224 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#f8f8f8\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(248 248 248 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#fff\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#ffff\] {
  background-color: #ffff !important;
}
.bg-\[F2F2F280\] {
  background-color: F2F2F280 !important;
}
.bg-black {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity)) !important;
}
.bg-gray-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
}
.bg-gray-300 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity)) !important;
}
.bg-green-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity)) !important;
}
.bg-red-100 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity)) !important;
}
.bg-red-200 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity)) !important;
}
.bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2 !important;
}
.fill-current {
  fill: currentColor !important;
}
.object-contain {
  -o-object-fit: contain !important;
     object-fit: contain !important;
}
.object-cover {
  -o-object-fit: cover !important;
     object-fit: cover !important;
}
.p-1 {
  padding: 0.25rem !important;
}
.p-16 {
  padding: 4rem !important;
}
.p-3 {
  padding: 0.75rem !important;
}
.p-4 {
  padding: 1rem !important;
}
.p-5 {
  padding: 1rem !important;
}
.p-6 {
  padding: 1.5rem !important;
}
.p-8 {
  padding: 2rem !important;
}
.p-2 {
  padding: 0.5rem !important;
}
.px-14 {
  padding-left: 3.5rem !important;
  padding-right: 3.5rem !important;
}
.px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}
.px-20 {
  padding-left: 5rem !important;
  padding-right: 5rem !important;
}
.px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}
.px-5 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}
.px-6 {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}
.px-8 {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}
.px-\[6\%\] {
  padding-left: 6% !important;
  padding-right: 6% !important;
}
.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.py-10 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}
.py-14 {
  padding-top: 3.5rem !important;
  padding-bottom: 3.5rem !important;
}
.py-16 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}
.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}
.py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}
.py-4 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}
.py-5 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}
.py-6 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}
.py-7 {
  padding-top: 1.75rem !important;
  padding-bottom: 1.75rem !important;
}
.py-\[3px\] {
  padding-top: 3px !important;
  padding-bottom: 3px !important;
}
.py-8 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}
.pb-10 {
  padding-bottom: 2.5rem !important;
}
.pb-16 {
  padding-bottom: 4rem !important;
}
.pb-2 {
  padding-bottom: 0.5rem !important;
}
.pb-4 {
  padding-bottom: 1rem !important;
}
.pb-5 {
  padding-bottom: 1rem !important;
}
.pb-7 {
  padding-bottom: 1.75rem !important;
}
.pb-8 {
  padding-bottom: 2rem !important;
}
.pl-\[28\%\] {
  padding-left: 28% !important;
}
.pr-10 {
  padding-right: 2.5rem !important;
}
.ps-12 {
  padding-inline-start: 3rem !important;
}
.ps-14 {
  padding-inline-start: 3.5rem !important;
}
.ps-4 {
  padding-inline-start: 1rem !important;
}
.ps-5 {
  padding-inline-start: 1rem !important;
}
.pt-10 {
  padding-top: 2.5rem !important;
}
.pt-2 {
  padding-top: 0.5rem !important;
}
.pt-3 {
  padding-top: 0.75rem !important;
}
.pt-4 {
  padding-top: 1rem !important;
}
.pt-5 {
  padding-top: 1rem !important;
}
.pt-6 {
  padding-top: 1.5rem !important;
}
.pt-8 {
  padding-top: 2rem !important;
}
.text-center {
  text-align: center !important;
}
.text-end {
  text-align: end !important;
}
.text-2xl {
  font-size: 1.5rem !important;
  line-height: 2rem !important;
}
.text-3xl {
  font-size: 1.875rem !important;
  line-height: 2.25rem !important;
}
.text-4xl {
  font-size: 2.25rem !important;
  line-height: 2.5rem !important;
}
.text-5xl {
  font-size: 3rem !important;
  line-height: 1 !important;
}
.text-6xl {
  font-size: 3.75rem !important;
  line-height: 1 !important;
}
.text-\[12px\] {
  font-size: 12px !important;
}
.text-\[24px\] {
  font-size: 24px !important;
}
.text-base {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}
.text-lg {
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
}
.text-xl {
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
}
.text-\[19px\] {
  font-size: 19px !important;
}
.font-bold {
  font-weight: 700 !important;
}
.font-medium {
  font-weight: 500 !important;
}
.font-normal {
  font-weight: 400 !important;
}
.font-semibold {
  font-weight: 600 !important;
}
.uppercase {
  text-transform: uppercase !important;
}
.capitalize {
  text-transform: capitalize !important;
}
.text-\[\#000000\] {
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
}
.text-\[\#1A3353\] {
  --tw-text-opacity: 1 !important;
  color: rgb(26 51 83 / var(--tw-text-opacity)) !important;
}
.text-\[\#1a3353\] {
  --tw-text-opacity: 1 !important;
  color: rgb(26 51 83 / var(--tw-text-opacity)) !important;
}
.text-\[\#393939\] {
  --tw-text-opacity: 1 !important;
  color: rgb(57 57 57 / var(--tw-text-opacity)) !important;
}
.text-\[\#3E79F7\] {
  --tw-text-opacity: 1 !important;
  color: rgb(62 121 247 / var(--tw-text-opacity)) !important;
}
.text-\[\#455560\] {
  --tw-text-opacity: 1 !important;
  color: rgb(69 85 96 / var(--tw-text-opacity)) !important;
}
.text-\[\#72849A\] {
  --tw-text-opacity: 1 !important;
  color: rgb(114 132 154 / var(--tw-text-opacity)) !important;
}
.text-\[\#D9D9D9\] {
  --tw-text-opacity: 1 !important;
  color: rgb(217 217 217 / var(--tw-text-opacity)) !important;
}
.text-\[\#acabab\] {
  --tw-text-opacity: 1 !important;
  color: rgb(172 171 171 / var(--tw-text-opacity)) !important;
}
.text-\[\#ffffff\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-\[white\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-black {
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
}
.text-indigo-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(79 70 229 / var(--tw-text-opacity)) !important;
}
.text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-gray-600 {
  --tw-text-opacity: 1 !important;
  color: rgb(75 85 99 / var(--tw-text-opacity)) !important;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 150ms !important;
}
.transition-all {
  transition-property: all !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 150ms !important;
}
.duration-300 {
  transition-duration: 300ms !important;
}
.ease-linear {
  transition-timing-function: linear !important;
}

/* Ripple Animation */
.custom_btn {
    position: relative;
    overflow: hidden;
    transition: 500ms;
    cursor: pointer;
}

.custom_btn:hover {
    background-color: transparent;
}

span.custom_ripple {
    position: absolute;

    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: ripple_animation 500ms linear;
    background-color: rgba(255, 255, 255, 0.7);
}

@keyframes ripple_animation {
    to {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* table pagination */
.ant-table-wrapper .ant-table-pagination-right {
    justify-content: center !important;
}

.ant-pagination .ant-pagination-prev,
.ant-pagination .ant-pagination-next {
    border: 1px solid #e6ebf1;
    border-radius: 50% !important;
    overflow: hidden;
}

.ant-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-pagination .ant-pagination-next .ant-pagination-item-link {
    display: grid;
    place-items: center;
}

.ant-pagination .ant-pagination-item-active {
    background-color: #3e79f7;
    border-color: #3e79f7 !important;
    border-radius: 50%;
}

.ant-pagination .ant-pagination-item-active a {
    color: #fff !important;
}

.ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover {
    border-radius: 50%;
}

.custom-tabs .ant-tabs-ink-bar {
    display: none;
}

.custom-tabs .ant-tabs-nav::before {
    display: none;
}

.justified-tabs .ant-tabs-nav-list {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.justified-tabs .ant-tabs-tab {
    flex: 1;
    text-align: center;
    justify-content: center;
}

#swtich-off td.ant-table-cell button {
    background-color: #a9abae;
}

.ql-toolbar.ql-snow {
    border-radius: 8px 8px 0 0;
}

.ql-container.ql-snow {
    border-radius: 0 0 8px 8px;
    min-height: 200px;
}

.ant-upload.ant-upload-select {
    width: 100% !important;
    height: 30vh !important;
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {
    .ant-input {
        font-size: 14px !important;
        padding: 5px 8px !important;
    }

    .ant-form-item-label {
        padding-bottom: 0 !important;

    }

    .ant-select,
    .ant-select-selection-search-input {
        font-size: 14px !important;
        height: 30px !important;
    }

    .ant-select-selection-item {
        font-size: 14px !important;
    }

    .justified-tabs .ant-tabs-nav-list {
        display: flex;
        justify-content: space-between;
        width: 100%;


    }

    .justified-tabs .ant-tabs-tab {
        flex: 1;
        text-align: center;
        justify-content: center;
    }

    #customer-listing-tab {
        width: 150px !important;
    }

}

.ant-form-item-required {
    flex-direction: row-reverse !important;
    -moz-column-gap: 5px;
         column-gap: 5px;
}

.ant-form-item-required::after {
    display: none;
}


/* =====================checkup collapse==================== */
.ant-collapse-borderless>.ant-collapse-item {
    background-color: #ffffff !important;
    border: 1px solid #E6EBF1 !important
}

/* ===========================================input margin bottom======================== */

.ant-form-item {
    margin-bottom: 15px !important;
}

/* ===========================================Global PLaceholders======================== */
::-moz-placeholder {
    font-size: 13px !important;
}
::placeholder {
    font-size: 13px !important;
}

.ant-select-selection-placeholder {
    font-size: 13px !important;
}


/* =============================global label======================== */

.ant-form-item-label>label {
    color: #1A3353 !important;
    font-size: 13px !important;
    font-weight: 500;

    /* Add any other styles you want */
}

/* ==========================antd layout content====================== */
@media screen and (max-width: 768px) {
    .ant-layout-content {
        margin: 0 !important;
        padding: 14px !important;
    }
}


/* ============================client profile collapse=========================== */

.client-profile-collapse.ant-collapse-borderless>.ant-collapse-item:last-child,
.ant-collapse-borderless>.ant-collapse-item:last-child .ant-collapse-header {
    /* background-color: #ffffff !important; */
    border-radius: 15px !important;
    background-color: #f8f8f8 !important;
}

.hover\:text-indigo-700:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(67 56 202 / var(--tw-text-opacity)) !important;
}

@media (min-width: 640px) {
  .sm\:hidden {
    display: none !important;
  }
  .sm\:w-\[120px\] {
    width: 120px !important;
  }
  .sm\:w-\[80\%\] {
    width: 80% !important;
  }
  .sm\:text-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .md\:w-1\/2 {
    width: 50% !important;
  }
  .md\:w-1\/3 {
    width: 33.333333% !important;
  }
}

@media (min-width: 1024px) {
  .lg\:-mt-7 {
    margin-top: -1.75rem !important;
  }
  .lg\:mb-12 {
    margin-bottom: 3rem !important;
  }
  .lg\:mb-16 {
    margin-bottom: 4rem !important;
  }
  .lg\:ms-7 {
    margin-inline-start: 1.75rem !important;
  }
  .lg\:mt-8 {
    margin-top: 2rem !important;
  }
  .lg\:mb-6 {
    margin-bottom: 1.5rem !important;
  }
  .lg\:mb-4 {
    margin-bottom: 1rem !important;
  }
  .lg\:mb-8 {
    margin-bottom: 2rem !important;
  }
  .lg\:mt-16 {
    margin-top: 4rem !important;
  }
  .lg\:flex {
    display: flex !important;
  }
  .lg\:grid {
    display: grid !important;
  }
  .lg\:hidden {
    display: none !important;
  }
  .lg\:h-24 {
    height: 6rem !important;
  }
  .lg\:w-1\/2 {
    width: 50% !important;
  }
  .lg\:w-\[100\%\] {
    width: 100% !important;
  }
  .lg\:w-\[120px\] {
    width: 120px !important;
  }
  .lg\:w-\[15\%\] {
    width: 15% !important;
  }
  .lg\:w-\[18\%\] {
    width: 18% !important;
  }
  .lg\:w-\[200px\] {
    width: 200px !important;
  }
  .lg\:w-\[25\%\] {
    width: 25% !important;
  }
  .lg\:w-\[30\%\] {
    width: 30% !important;
  }
  .lg\:w-\[31\%\] {
    width: 31% !important;
  }
  .lg\:w-\[35\%\] {
    width: 35% !important;
  }
  .lg\:w-\[40\%\] {
    width: 40% !important;
  }
  .lg\:w-\[50\%\] {
    width: 50% !important;
  }
  .lg\:w-\[60\%\] {
    width: 60% !important;
  }
  .lg\:w-\[60vw\] {
    width: 60vw !important;
  }
  .lg\:w-\[75\%\] {
    width: 75% !important;
  }
  .lg\:w-\[77\%\] {
    width: 77% !important;
  }
  .lg\:w-\[80\%\] {
    width: 80% !important;
  }
  .lg\:w-\[85\%\] {
    width: 85% !important;
  }
  .lg\:w-\[95\%\] {
    width: 95% !important;
  }
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .lg\:flex-row {
    flex-direction: row !important;
  }
  .lg\:flex-wrap {
    flex-wrap: wrap !important;
  }
  .lg\:items-center {
    align-items: center !important;
  }
  .lg\:justify-end {
    justify-content: flex-end !important;
  }
  .lg\:justify-between {
    justify-content: space-between !important;
  }
  .lg\:gap-10 {
    gap: 2.5rem !important;
  }
  .lg\:gap-8 {
    gap: 2rem !important;
  }
  .lg\:rounded-3xl {
    border-radius: 1.5rem !important;
  }
  .lg\:bg-\[\#3E79F7\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(62 121 247 / var(--tw-bg-opacity)) !important;
  }
  .lg\:bg-\[\#f8f8f8f8\] {
    background-color: #f8f8f8f8 !important;
  }
  .lg\:object-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important;
  }
  .lg\:p-10 {
    padding: 2.5rem !important;
  }
  .lg\:p-16 {
    padding: 4rem !important;
  }
  .lg\:p-8 {
    padding: 2rem !important;
  }
  .lg\:px-14 {
    padding-left: 3.5rem !important;
    padding-right: 3.5rem !important;
  }
  .lg\:px-20 {
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }
  .lg\:px-40 {
    padding-left: 10rem !important;
    padding-right: 10rem !important;
  }
  .lg\:px-\[6\%\] {
    padding-left: 6% !important;
    padding-right: 6% !important;
  }
  .lg\:py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .lg\:py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .lg\:pb-5 {
    padding-bottom: 1rem !important;
  }
  .lg\:pe-10 {
    padding-inline-end: 2.5rem !important;
  }
  .lg\:pe-14 {
    padding-inline-end: 3.5rem !important;
  }
  .lg\:pr-10 {
    padding-right: 2.5rem !important;
  }
  .lg\:ps-2 {
    padding-inline-start: 0.5rem !important;
  }
  .lg\:pt-32 {
    padding-top: 8rem !important;
  }
  .lg\:pt-5 {
    padding-top: 1rem !important;
  }
  .lg\:pt-6 {
    padding-top: 1.5rem !important;
  }
  .lg\:pb-6 {
    padding-bottom: 1.5rem !important;
  }
  .lg\:ps-10 {
    padding-inline-start: 2.5rem !important;
  }
  .lg\:pb-10 {
    padding-bottom: 2.5rem !important;
  }
  .lg\:text-lg {
    font-size: 1.125rem !important;
    line-height: 1.75rem !important;
  }
  .lg\:font-bold {
    font-weight: 700 !important;
  }
  .lg\:text-\[white\] {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
  }
}

@media (max-width: 768px) {
  .\@sm\:fixed {
    position: fixed !important;
  }
  .\@sm\:order-1 {
    order: 1 !important;
  }
  .\@sm\:mb-10 {
    margin-bottom: 2.5rem !important;
  }
  .\@sm\:me-2 {
    margin-inline-end: 0.5rem !important;
  }
  .\@sm\:mt-2 {
    margin-top: 0.5rem !important;
  }
  .\@sm\:mt-5 {
    margin-top: 1.25rem !important;
  }
  .\@sm\:flex {
    display: flex !important;
  }
  .\@sm\:grid {
    display: grid !important;
  }
  .\@sm\:hidden {
    display: none !important;
  }
  .\@sm\:h-\[70px\] {
    height: 70px !important;
  }
  .\@sm\:h-screen {
    height: 100vh !important;
  }
  .\@sm\:h-24 {
    height: 6rem !important;
  }
  .\@sm\:w-0 {
    width: 0px !important;
  }
  .\@sm\:w-\[100px\] {
    width: 100px !important;
  }
  .\@sm\:w-\[100vw\] {
    width: 100vw !important;
  }
  .\@sm\:w-full {
    width: 100% !important;
  }
  .\@sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  .\@sm\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  .\@sm\:flex-col {
    flex-direction: column !important;
  }
  .\@sm\:justify-center {
    justify-content: center !important;
  }
  .\@sm\:gap-10 {
    gap: 2.5rem !important;
  }
  .\@sm\:gap-2 {
    gap: 0.5rem !important;
  }
  .\@sm\:gap-4 {
    gap: 1rem !important;
  }
  .\@sm\:gap-6 {
    gap: 1.5rem !important;
  }
  .\@sm\:rounded-3xl {
    border-radius: 1.5rem !important;
  }
  .\@sm\:rounded-lg {
    border-radius: 0.5rem !important;
  }
  .\@sm\:bg-\[\#fff\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  }
  .\@sm\:object-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
  .\@sm\:p-0 {
    padding: 0px !important;
  }
  .\@sm\:p-3 {
    padding: 0.75rem !important;
  }
  .\@sm\:p-4 {
    padding: 1rem !important;
  }
  .\@sm\:p-5 {
    padding: 1rem !important;
  }
  .\@sm\:p-8 {
    padding: 2rem !important;
  }
  .\@sm\:px-10 {
    padding-left: 2.5rem !important;
    padding-right: 2.5rem !important;
  }
  .\@sm\:px-5 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .\@sm\:px-6 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  .\@sm\:py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .\@sm\:py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  .\@sm\:ps-5 {
    padding-inline-start: 1rem !important;
  }
  .\@sm\:pb-10 {
    padding-bottom: 2.5rem !important;
  }
  .\@sm\:pt-3 {
    padding-top: 0.75rem !important;
  }
  .\@sm\:pt-6 {
    padding-top: 1.5rem !important;
  }
  .\@sm\:pt-8 {
    padding-top: 2rem !important;
  }
  .\@sm\:pb-5 {
    padding-bottom: 1rem !important;
  }
  .\@sm\:pb-7 {
    padding-bottom: 1.75rem !important;
  }
  .\@sm\:text-center {
    text-align: center !important;
  }
  .\@sm\:text-2xl {
    font-size: 1.5rem !important;
    line-height: 2rem !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .\@md\:h-auto {
    height: auto !important;
  }
  .\@md\:w-\[10px\] {
    width: 10px !important;
  }
  .\@md\:py-\[5px\] {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .\@lg\:h-auto {
    height: auto !important;
  }
  .\@lg\:py-\[4px\] {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
  }
}</style></head>
  <body>
    <div id="root"><div class="h-full w-full"><div class="ant-layout ant-layout-has-sider min-h-screen css-dev-only-do-not-override-qnu6hi"><div class="sticky top-0 h-screen bg-white"><aside class="ant-layout-sider ant-layout-sider-dark  bg-white @sm:hidden @sm:w-0" style="flex: 0 0 290px; max-width: 290px; min-width: 290px; width: 290px;"><div class="ant-layout-sider-children"><div class="sticky top-0 z-10 py-1 shadow"><img src="./Knox_files/logo.svg" alt="logo" class="mx-auto my-5 h-[40px] "></div><div class=" h-[90vh] overflow-y-auto  "><ul class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-dev-only-do-not-override-qnu6hi" role="menu" tabindex="0" data-menu-list="true"><li class="ant-menu-submenu ant-menu-submenu-inline" role="none"><div role="menuitem" class="ant-menu-submenu-title" tabindex="-1" aria-expanded="false" aria-haspopup="true" style="padding-left: 24px;" data-menu-id="rc-menu-uuid-84739-1-1" aria-controls="rc-menu-uuid-84739-1-1-popup"><div class="flex justify-center w-1/3 ant-menu-item-icon"><img src="./Knox_files/home.svg" alt="icon" class="max-h-[25px]"></div><span class="ant-menu-title-content"><p class="m-0 text-xl font-medium ">Home</p></span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item" role="menuitem" tabindex="-1" style="padding-left: 24px;" data-menu-id="rc-menu-uuid-84739-1-7"><div class="flex justify-center w-1/3 ant-menu-item-icon"><img src="./Knox_files/Clients.svg" alt="icon" class="max-h-[25px]"></div><span class="ant-menu-title-content"><p class="m-0 text-xl font-medium " href="/customers">Clients</p></span></li></ul><div aria-hidden="true" style="display: none;"></div></div></div></aside></div><div class="bottom-0   right-0 top-0 z-20 min-h-screen overflow-y-scroll border-0  transition-all duration-300 ease-linear sm:hidden sm:text-center @sm:fixed @sm:bg-[#fff] overflow-hidden @sm:w-0 @sm:p-0"><div class="flex items-center justify-between"><img src="./Knox_files/logo.svg" alt="logo" class="my-0 h-[40px]"><span role="img" aria-label="close" tabindex="-1" class="anticon anticon-close text-[19px] sm:hidden"><svg fill-rule="evenodd" viewBox="64 64 896 896" focusable="false" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"></path></svg></span></div><div class="mt-16 "><ul class="ant-menu ant-menu-root ant-menu-inline ant-menu-light border-0 css-dev-only-do-not-override-qnu6hi" role="menu" tabindex="0" data-menu-list="true"><li class="ant-menu-submenu ant-menu-submenu-inline" role="none"><div role="menuitem" class="ant-menu-submenu-title" tabindex="-1" aria-expanded="false" aria-haspopup="true" data-menu-id="rc-menu-uuid-84739-2-1" aria-controls="rc-menu-uuid-84739-2-1-popup" style="padding-left: 24px;"><div class="flex    justify-center  ant-menu-item-icon"><img src="./Knox_files/home.svg" alt="icon" class="max-h-[25px]"></div><span class="ant-menu-title-content"><p class="m-0 text-xl font-medium">Home</p></span><i class="ant-menu-submenu-arrow"></i></div></li><li class="ant-menu-item" role="menuitem" tabindex="-1" data-menu-id="rc-menu-uuid-84739-2-7" style="padding-left: 24px;"><div class="flex    justify-center  ant-menu-item-icon"><img src="./Knox_files/Clients.svg" alt="icon" class="max-h-[25px]"></div><span class="ant-menu-title-content"><p class="m-0 text-xl font-medium" href="/customers">Clients</p></span></li></ul><div aria-hidden="true" style="display: none;"></div></div></div><div class="ant-layout css-dev-only-do-not-override-qnu6hi"><div class=" sticky top-0 z-10 border-l bg-white py-[3px] shadow-md"><header class="ant-layout-header   flex items-center justify-between  css-dev-only-do-not-override-qnu6hi" style="padding: 0px; background: rgb(255, 255, 255);"><div class="flex items-center gap-16 "><button type="button" class="ant-btn css-dev-only-do-not-override-qnu6hi ant-btn-text ant-btn-icon-only" style="font-size: 16px; width: 64px; height: 64px;"><span class="ant-btn-icon"><div> <svg viewBox="64 64 896 896" focusable="false" data-icon="menu-fold" width="1.2em" height="1.2em" fill="currentColor" aria-hidden="true"><path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"></path></svg></div></span></button></div><div class="px-5"><span class="ant-avatar ant-avatar-circle ant-avatar-icon ant-dropdown-trigger css-dev-only-do-not-override-qnu6hi" style="width: 45px; height: 45px; font-size: 22.5px; cursor: pointer;"><span role="img" aria-label="user" class="anticon anticon-user"><svg viewBox="64 64 896 896" focusable="false" data-icon="user" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"></path></svg></span></span></div></header></div><main class="ant-layout-content shadow-md css-dev-only-do-not-override-qnu6hi" style="margin: 20px; padding: 24px; min-height: 280px; background: rgb(255, 255, 255); border-radius: 8px;"><div class="flex flex-col justify-center rounded-2xl items-center h-full bg-gray-300 text-white text-center p-4"><h1 class="text-6xl font-bold mb-4 text-[#000000] ">Coming Soon</h1><h2 class="text-2xl mb-8 text-[#393939]">We're working hard to bring you something amazing!</h2></div></main></div></div></div><div style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;"></div></div>
    <script type="module" src="./Knox_files/index.tsx"></script>
  

</body></html>