import __vite__cjsImport0_react_jsxRuntime from "/node_modules/.vite/deps/react_jsx-runtime.js?v=454dadac"; const _jsx = __vite__cjsImport0_react_jsxRuntime["jsx"]; const _jsxs = __vite__cjsImport0_react_jsxRuntime["jsxs"]; const _Fragment = __vite__cjsImport0_react_jsxRuntime["Fragment"];
import store from "/src/redux/store.ts?t=1726569558496";
import "/node_modules/react-quill/dist/quill.snow.css";
import "/node_modules/react-big-calendar/lib/css/react-big-calendar.css";
import __vite__cjsImport4_reactDom_client from "/node_modules/.vite/deps/react-dom_client.js?v=454dadac"; const ReactDOM = __vite__cjsImport4_reactDom_client.__esModule ? __vite__cjsImport4_reactDom_client.default : __vite__cjsImport4_reactDom_client;
import { Toaster } from "/node_modules/.vite/deps/react-hot-toast.js?v=454dadac";
import { Provider } from "/node_modules/.vite/deps/react-redux.js?v=454dadac";
import App from "/src/App.tsx?t=1726569558496";
import "/src/styles/globals.css?t=1726569558496";
ReactDOM.createRoot(document.getElementById("root")).render(_jsx(_Fragment, { children: _jsxs(Provider, { store, children: [_jsx(App, {}), _jsx(Toaster, {})] }) }));

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJtYXBwaW5ncyI6IjtBQUFBLE9BQU9BLFdBQVc7QUFDbEIsT0FBTztBQUNQLE9BQU87QUFFUCxPQUFPQyxjQUFjO0FBQ3JCLFNBQVNDLGVBQWU7QUFDeEIsU0FBU0MsZ0JBQWdCO0FBQ3pCLE9BQU9DLFNBQVM7QUFDaEIsT0FBTztBQUVQSCxTQUFTSSxXQUFXQyxTQUFTQyxlQUFlLE1BQU0sQ0FBRSxFQUFFQyxPQUNsREMsS0FBQUMsV0FBQSxFQUFBQyxVQUNJQyxNQUFDVCxVQUFRLEVBQUNILE9BQVlXLFVBQUEsQ0FDbEJGLEtBQUNMLEtBQUcsS0FDSkssS0FBQ1AsU0FBTyxHQUFHLEtBQ0osRUFDWiIsIm5hbWVzIjpbInN0b3JlIiwiUmVhY3RET00iLCJUb2FzdGVyIiwiUHJvdmlkZXIiLCJBcHAiLCJjcmVhdGVSb290IiwiZG9jdW1lbnQiLCJnZXRFbGVtZW50QnlJZCIsInJlbmRlciIsIl9qc3giLCJfRnJhZ21lbnQiLCJjaGlsZHJlbiIsIl9qc3hzIl0sInNvdXJjZXMiOlsiaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBzdG9yZSBmcm9tICd+L3JlZHV4L3N0b3JlJztcbmltcG9ydCAncmVhY3QtcXVpbGwvZGlzdC9xdWlsbC5zbm93LmNzcyc7XG5pbXBvcnQgJ3JlYWN0LWJpZy1jYWxlbmRhci9saWIvY3NzL3JlYWN0LWJpZy1jYWxlbmRhci5jc3MnO1xuXG5pbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tL2NsaWVudCc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xuaW1wb3J0IEFwcCBmcm9tICd+L0FwcC50c3gnO1xuaW1wb3J0ICd+L3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5cblJlYWN0RE9NLmNyZWF0ZVJvb3QoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3Jvb3QnKSEpLnJlbmRlcihcbiAgICA8PlxuICAgICAgICA8UHJvdmlkZXIgc3RvcmU9e3N0b3JlfT5cbiAgICAgICAgICAgIDxBcHAgLz5cbiAgICAgICAgICAgIDxUb2FzdGVyIC8+XG4gICAgICAgIDwvUHJvdmlkZXI+XG4gICAgPC8+XG4pO1xuIl0sImZpbGUiOiIvVXNlcnMvaGtzbWFucG93ZXIvRG9jdW1lbnRzL1JlYWN0anMgUHJvamVjdHMvYWRtaW4tZ3ltLXJlYWN0L3NyYy9pbmRleC50c3gifQ==