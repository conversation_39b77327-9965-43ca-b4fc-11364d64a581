import {
    TypedUseSelectorHook,
    shallowEqual,
    useDispatch,
    useSelector,
} from 'react-redux';
import { AppDispatch, RootState } from '~/redux/store';

// Adjust the import path as needed

export const useAppSelector: TypedUseSelectorHook<RootState> = (selector) => {
    // Get the selector result using the useSelector hook
    const selectedData = useSelector(selector, shallowEqual);

    return selectedData;
};

export const useAppDispatch = () => useDispatch<AppDispatch>();
