// import {
//     FasterImageProps,
//     FasterImageView,
//     clearCache,
// } from '@candlefinance/faster-image';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Image,
    ImageStyle,
    StyleSheet,
    Text,
    View,
    ViewStyle,
} from 'react-native';

// interface FastImageProps extends FasterImageProps {
//     containerStyle?: ViewStyle;
//     imageStyle?: ImageStyle;
//     placeholderSource: any; // Add this line for the placeholder image source
//     onSuccess?: (event: any) => void;
//     onError?: (event: any) => void;
//     cachePolicy?: 'diskWithCacheControl' | 'disk' | 'memory';
// }

const FastImage = ({
    source,
    style,
    containerStyle,
    imageStyle,
    placeholderSource, // Add this line for the placeholder image source
    onSuccess,
    onError,
    cachePolicy = 'diskWithCacheControl',
    ...props
}: any) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const handleSuccess = (event: any) => {
        setLoading(false);
        if (onSuccess) {
            onSuccess(event);
        }
    };

    const handleError = (event: any) => {
        setLoading(false);
        setError(event.nativeEvent.error);
        if (onError) {
            onError(event);
        }
    };

    return (
        <View style={[styles.container, containerStyle]}>
            {loading && (
                <Image
                    source={placeholderSource}
                    style={[styles.image, imageStyle, style]}
                />
            )}
            {/* {loading && <ActivityIndicator style={styles.loader} />} */}
            {error && (
                <View style={styles.error}>
                    <Text>{error}</Text>
                </View>
            )}
            {!loading && !error && (
                // <FasterImageView
                //     style={[styles.image, imageStyle, style]}
                //     source={source}
                //     cachePolicy={cachePolicy}
                //     onSuccess={handleSuccess}
                //     onError={handleError}
                //     {...props}
                // />
                <View></View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'relative',
    },
    loader: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -50 }, { translateY: -50 }],
    },
    error: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -50 }, { translateY: -50 }],
        backgroundColor: 'red',
        padding: 10,
        borderRadius: 5,
    },
    image: {
        width: '100%',
        height: '100%',
    },
});

export default FastImage;
