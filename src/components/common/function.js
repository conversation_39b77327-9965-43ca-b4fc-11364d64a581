import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { Staff_Roles } from '~/constants/enums';

dayjs.extend(utc);
dayjs.extend(timezone);
export const capitalizeFirstLetter = (string) => {
  return string?.split(' ')?.map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ');
};

export function goBack() {
  window.history.back();
}

export function renderImage(imageLink) {
  if (imageLink) {
    return imageLink;
  } else {
    return 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  }
}

export function formatDateString(isoDate) {
  if (!isoDate) {
    return '-';
  }
  const date = new Date(isoDate);
  const day = date.getDate();
  const month = date.toLocaleString('en-US', { month: 'short' }).toUpperCase();
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}

export function formatStringWithSpaces(input) {
  if (typeof input !== 'string') {
    return '';
  }

  let result = input?.charAt(0).toUpperCase() + input?.slice(1);

  result = result?.replace(/([A-Z])/g, (match, p1, offset) => {
    return (offset > 0 && result[offset - 1] !== ' ') ? ` ${p1}` : p1;
  });

  return result;
}



export function formatDate(dateString) {
  const date = new Date(dateString);

  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const month = months[date.getUTCMonth()];
  const day = date.getUTCDate();
  const year = date.getUTCFullYear();

  const formattedDate = `${day}-${month}-${year}`;

  return formattedDate;
}


export const cleanSchedule = (slots) => {
  const schedule = {};

  Object.keys(slots).forEach(day => {
    if (slots[day].length > 0) {
      schedule[day] = slots[day].map(slot => ({
        from: slot.from,
        to: slot.to,
        payRateIds: slot.payRateIds || [],
      })).filter(slot => slot.from && slot.to); // Only include slots with both times
    }
  });

  return schedule;
};

export const generateUniqueKey = () => {
  return `${Date.now()}-${Math.random()}`;
};


// Function to get human-readable role
export const getFormattedRole = (role) => {
  return Staff_Roles[role] || role;
};
