import React, { useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import Svg, { Path } from 'react-native-svg';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { navigationRef } from '~/hooks/useLocation';

const Icon = ({ children, ...props }: any) => (
    <Svg
        width={20}
        height={20}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        {...props}
    >
        {children}
    </Svg>
);

const LocationIcon = React.memo((props: any) => (
    <Icon {...props}>
        <Path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" />
        <Path d="M12 13a3 3 0 100-6 3 3 0 000 6z" />
    </Icon>
));

type Props = {
    goBack?: () => void;
    Heading: string;
    AppointmentFilter?: boolean;
    openModal?: () => void;
    handleLocation?: () => void;
    facilityName?: string;
    setIsCalendarMinimized?: (value: boolean) => void;
    isCalendarMinimized?: boolean;
    isCalender?: boolean;
};

const BackButtonHeading = ({
    goBack,
    Heading,
    AppointmentFilter,
    openModal,
    handleLocation,
    facilityName,
    setIsCalendarMinimized,
    isCalendarMinimized,
    isCalender,
}: any) => {
    const onBack = () => {
        if (!goBack) {
            navigationRef.goBack();
        } else {
            goBack();
            // navigationRef.goBack();
        }
    };
    return (
        <>
            <View
                style={tw` bg-primary py-6 flex flex-row justify-between items-center px-5 `}
            >
                <View style={tw`  flex flex-row  items-center  gap-4`}>
                    <TouchableOpacity
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                        onPress={onBack}
                    >
                        <Image
                            style={tw` h-5 w-3 `}
                            source={Asset.BackButton}
                        />
                    </TouchableOpacity>
                    {!isCalender && (
                        <View>
                            <Text
                                style={tw`text-xl text-[#ffffff] font-bold uppercase`}
                            >
                                {Heading}
                            </Text>
                        </View>
                    )}
                    {isCalender && (
                        <TouchableOpacity onPress={handleLocation}>
                            <View style={tw`flex flex-row gap-3  items-center`}>
                                <LocationIcon stroke="#fff" />
                                <Text
                                    style={tw`text-[#fff] text-24 font-semibold`}
                                >
                                    {facilityName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    )}
                </View>
                <View style={tw`flex flex-row items-center gap-4`}>
                    {isCalender && (
                        <TouchableOpacity
                            onPress={() =>
                                setIsCalendarMinimized(!isCalendarMinimized)
                            }
                        >
                            <Image
                                style={tw`h-6 w-6`}
                                source={Asset.CalenderWhite}
                            />
                        </TouchableOpacity>
                    )}
                    {AppointmentFilter && (
                        <View>
                            <TouchableOpacity onPress={openModal}>
                                <Image
                                    style={tw` h-6 w-6`}
                                    source={Asset.FilterWhiteIcon}
                                />
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </View>
            <View style={tw` px-4 bg-primary`}></View>
            {/* <Provider>
                <Modal
                    popup
                    modalType="modal"
                    visible={filterModal}
                    animationType="none"
                    onClose={closeModal}
                    style={tw`w-[50%] my-auto mx-auto rounded-xl `}
                >
                    <View style={tw`flex flex-row justify-end`}>
                        <Text
                            onPress={closeModal}
                            style={tw`text-xl pr-3 pt-1`}
                        >
                            X
                        </Text>
                    </View>

                    <View style={tw`flex flex-col gap-2 p pb-7`}>
                        <Text
                            style={tw`text-center border-b pb-1.5 border-MainTextColor border-opacity-20`}
                        >
                            Room
                        </Text>
                        <Text
                            style={tw`text-center border-b pb-1.5 border-MainTextColor border-opacity-30`}
                        >
                            Instructor{' '}
                        </Text>
                        <Text
                            style={tw`text-center border-b pb-1.5 border-MainTextColor border-opacity-30`}
                        >
                            Service Type
                        </Text>
                    </View>
                </Modal>
            </Provider> */}
        </>
    );
};

export default BackButtonHeading;
