import React, { useState } from 'react';
import {
    FlatList,
    Modal,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { State } from 'react-native-gesture-handler';
import { GetStaffAvailabilityList } from '~/redux/actions/scheduling-actions';
import { SelectTrainer } from '~/redux/slices/scheduling-slice';

import tw from '~/styles/tailwind';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

const TrainerByOrgModal = ({
    visible,
    onClose,
    selectedDate,
    facilityId,
}: any) => {
    const [searchText, setSearchText] = useState('');
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        trainers: state.scheduling_store.trainersListByOrganization,
    }));

    const filteredTrainers = store.trainers?.filter((trainer: any) =>
        `${trainer.firstName} ${trainer.lastName}`
            .toLowerCase()
            .includes(searchText.toLowerCase())
    );

    const handleTrainerSelect = (trainer: any) => {
        // setSelectedTrainer(trainer);
        console.log('Selected Trainer:', trainer);
        dispatch(SelectTrainer(trainer));
        dispatch(
            GetStaffAvailabilityList({
                selectedDate,
                facilityId: facilityId,
            })
        )
            .unwrap()
            .then(() => {})
            .catch((error) => {
                console.error('Error fetching staff availability:', error);
            });
        onClose();
    };

    return (
        <Modal
            transparent={true}
            visible={visible}
            onRequestClose={onClose}
            animationType="slide"
            style={tw` `}
        >
            <TouchableOpacity style={styles.overlay} onPress={onClose}>
                <View style={styles.modalContainer}>
                    <Text
                        style={tw`text-base font-medium py-2  text-[#455560]`}
                    >
                        Select Trainers
                    </Text>
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Search Trainers"
                        value={searchText}
                        onChangeText={setSearchText}
                    />
                    <FlatList
                        style={tw` -mt-3`}
                        data={filteredTrainers}
                        keyExtractor={(item: any) => item.userId}
                        renderItem={({ item }) => (
                            <TouchableOpacity
                                onPress={() => handleTrainerSelect(item)}
                            >
                                <Text
                                    style={tw`text-base my-1 border-b border-gray-200 pb-1 text-[#455560]`}
                                >
                                    {item.firstName} {item.lastName}
                                </Text>
                            </TouchableOpacity>
                        )}
                    />
                </View>
            </TouchableOpacity>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
        width: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        // paddingTop: 20,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        height: '60%',
    },
    searchInput: {
        width: '100%',
        padding: 10,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 5,
        marginBottom: 20,
        color: '#455560',
    },
});

export default TrainerByOrgModal;
