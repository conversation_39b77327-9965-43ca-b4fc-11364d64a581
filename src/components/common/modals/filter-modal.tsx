// components/modals/filter-modal.tsx
import React, { useEffect, useState } from 'react';
import {
    Animated,
    FlatList,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { GetStaffAvailabilityList } from '~/redux/actions/scheduling-actions';
import { SelectTrainer } from '~/redux/slices/scheduling-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { UserRole } from '~/constants/enums';

import { Button } from '~/components/atoms';
import Radio from '~/components/library/radio';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

type Props = {
    visible: boolean;
    onClose: () => void;
    selectedDate?: any;
    onFilterChange: (filters: {
        roomOrInstructor?: any;
        serviceType?: string;
    }) => void;
};

const FilterModal = ({
    visible,
    onClose,
    onFilterChange,
    selectedDate,
}: Props) => {
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        trainers: state.scheduling_store.trainersListByOrganization,
        facilityId: state.facility_store.facility._id,
    }));
    const [roomOrInstructor, setRoomOrInstructor] = useState<string | null>(
        'staff'
    );
    const [expandedSections, setExpandedSections] = useState<any>({
        room: false,
        staff: false,
    });

    const [searchText, setSearchText] = useState('');
    const [selectedTrainer, setSelectedTrainer] = useState<any>(null);

    const filteredTrainers = store.trainers?.filter((trainer: any) =>
        `${trainer.firstName} ${trainer.lastName}`
            .toLowerCase()
            .includes(searchText.toLowerCase())
    );

    const handleTrainerSelect = (trainer: any) => {
        setSelectedTrainer(trainer);
    };

    const handleApply = () => {
        if (selectedTrainer) {
            dispatch(SelectTrainer(selectedTrainer));
            dispatch(
                GetStaffAvailabilityList({
                    selectedDate,
                    facilityId: store.facilityId,
                })
            ).unwrap();
        }
        onFilterChange({ roomOrInstructor: selectedTrainer });
        setExpandedSections({ room: false, staff: false });
        onClose();
    };

    const handleClearFilter = () => {
        setSelectedTrainer(null);
        setRoomOrInstructor('staff');
        onFilterChange({ roomOrInstructor: null });
        setExpandedSections({ room: false, staff: false });
        dispatch(SelectTrainer({}));
    };

    useEffect(() => {
        if (selectedTrainer) {
            onFilterChange({ roomOrInstructor: selectedTrainer });
            setExpandedSections({ ...expandedSections, staff: true });
        }
    }, [selectedTrainer]);

    const [serviceType, setServiceType] = useState<string | null>(null);
    const slideAnim = React.useRef(new Animated.Value(300)).current;

    React.useEffect(() => {
        if (visible) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 300,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    const handleSelect = (type: 'room' | 'instructor' | 'service') => {
        if (type === 'service') {
            setServiceType('serviceType');
            onFilterChange({ serviceType: 'serviceType' });
            onClose();
        } else {
            const value = type === 'room' ? 'room' : 'instructor';
            setRoomOrInstructor(value);
            onFilterChange({ roomOrInstructor: value });
            onClose();
        }
    };

    const rooms = [
        { id: '1', name: 'Room 1' },
        { id: '2', name: 'Room 2' },
        { id: '3', name: 'Room 3' },
    ];

    const staff = [
        {
            id: '1',
            name: 'Dwivedi',
        },
        {
            id: '2',
            name: 'Dwivedi',
        },
        {
            id: '3',
            name: 'Dwivedi',
        },
    ];

    const FilterName = [
        {
            id: 'room',
            name: 'Room',
            data: rooms,
        },
        {
            id: 'staff',
            name: 'Staff',
            data: filteredTrainers,
        },
    ];

    const toggleSection = (sectionId: any) => {
        setExpandedSections((prev: any) => ({
            ...prev,
            [sectionId]: !prev[sectionId],
        }));
    };

    return (
        <Modal
            // style={tw`w-[50%] my-auto mx-auto rounded-xl `}
            visible={visible}
            transparent={true}
            animationType="none"
        >
            <View style={styles.overlay}>
                <TouchableOpacity style={styles.overlay} onPress={onClose} />
                <Animated.View
                    style={[
                        styles.modalContainer,
                        { transform: [{ translateY: slideAnim }] },
                        tw` h-[80%]`,
                    ]}
                >
                    <View style={tw``}>
                        <View style={tw`bg-white   rounded-xl px-2 py-0`}>
                            <View
                                style={tw`flex flex-row justify-between items-center`}
                            >
                                <Text
                                    style={tw`text-[#455560] font-semibold text-lg`}
                                >
                                    Filter
                                </Text>
                                <TouchableOpacity onPress={onClose}>
                                    <Text
                                        style={tw`text-primary font-semibold text-base`}
                                    >
                                        Cancel
                                    </Text>
                                </TouchableOpacity>
                            </View>
                            {/* <View style={tw`flex flex-col gap-2 bg-blue-200`}>
                                <TouchableOpacity
                                    onPress={() => handleSelect('room')}
                                >
                                    <Text
                                        style={tw`text-center border-b pb-1.5 border-MainTextColor border-opacity-20`}
                                    >
                                        Room
                                        {roomOrInstructor === 'room' && '✓'}
                                    </Text>
                                </TouchableOpacity>

                                {store.role !== UserRole.Trainer && (
                                    <TouchableOpacity
                                        onPress={() =>
                                            handleSelect('instructor')
                                        }
                                    >
                                        <Text
                                            style={tw`text-center border-b pb-1.5 border-MainTextColor border-opacity-30`}
                                        >
                                            Instructor{' '}
                                            {roomOrInstructor ===
                                                'instructor' && '✓'}
                                        </Text>
                                    </TouchableOpacity>
                                )}
                                <TouchableOpacity
                                    onPress={() => handleSelect('service')}
                                >
                                    <Text
                                        style={tw`text-center border-b pb-1.5 border-MainTextColor border-opacity-30`}
                                    >
                                        Service Type {serviceType && '✓'}
                                    </Text>
                                </TouchableOpacity>
                            </View> */}

                            {/* {FilterName.map((filter) => (
                                <View key={filter.id}>
                                    <View
                                        style={tw`flex flex-row items-center pt-3 border-b pb-1.5 border-[#455560] border-opacity-20 justify-between pr-2`}
                                    >
                                        <Text
                                            style={tw`text-[#455560] font-semibold text-base`}
                                        >
                                            {filter.name}
                                        </Text>

                                        <TouchableOpacity
                                            onPress={() =>
                                                toggleSection(filter.id)
                                            }
                                            style={tw`p-2`}
                                        >
                                            <Image
                                                style={[
                                                    tw`w-4`,
                                                    expandedSections[filter.id]
                                                        ? tw`h-.5`
                                                        : tw`h-4`,
                                                ]}
                                                source={
                                                    expandedSections[filter.id]
                                                        ? Asset.MinusIcon
                                                        : Asset.PLusImage
                                                }
                                            />
                                        </TouchableOpacity>
                                    </View>

                                    {expandedSections[filter.id] && (
                                        <View style={tw`mt-2`}>
                                            <FlatList
                                                data={filter.data}
                                                keyExtractor={(item) => item.id}
                                                renderItem={({ item }) => (
                                                    <View style={tw`py-2`}>
                                                        <Text
                                                            style={tw`text-base text-[#455560]`}
                                                        >
                                                            {item.name}
                                                        </Text>
                                                    </View>
                                                )}
                                            />
                                        </View>
                                    )}
                                </View>
                            ))} */}

                            {/* <View style={tw`mt-5`}>
                                <TouchableOpacity
                                    onPress={() => toggleSection('room')}
                                >
                                    <View
                                        style={tw`flex flex-row justify-between border-b pb-1.5 border-[#455560] border-opacity-20  items-center`}
                                    >
                                        <Text
                                            style={tw`text-[#455560] font-medium py-2`}
                                        >
                                            Room
                                        </Text>
                                        <Image
                                            style={tw`w-4 h-4`}
                                            source={
                                                expandedSections.room
                                                    ? Asset.MinusIcon
                                                    : Asset.PLusImage
                                            }
                                        />
                                    </View>
                                </TouchableOpacity>
                                {expandedSections.room && (
                                    <FlatList
                                        data={['Room 1', 'Room 2', 'Room 3']} // Static list or dynamic data
                                        keyExtractor={(item, index) =>
                                            index.toString()
                                        }
                                        renderItem={({ item }) => (
                                            <TouchableOpacity
                                                onPress={() =>
                                                    onFilterChange({
                                                        roomOrInstructor: item,
                                                    })
                                                }
                                                style={tw`py-2 border-b border-gray-200`}
                                            >
                                                <Text
                                                    style={tw`text-[#455560]`}
                                                >
                                                    {item}
                                                </Text>
                                            </TouchableOpacity>
                                        )}
                                    />
                                )}
                            </View> */}

                            {/* Accordion Section for Staff */}
                            <View style={tw`mt-5`}>
                                <TouchableOpacity
                                    onPress={() => toggleSection('staff')}
                                >
                                    <View
                                        style={tw`flex flex-row justify-between border-b pb-1.5 border-[#455560] border-opacity-20 items-center`}
                                    >
                                        <Text
                                            style={tw`text-[#455560] font-medium py-2`}
                                        >
                                            Staff
                                        </Text>
                                        <Image
                                            style={tw`w-4 h-4`}
                                            resizeMode="contain"
                                            source={
                                                expandedSections.staff
                                                    ? Asset.MinusIcon
                                                    : Asset.PLusImage
                                            }
                                        />
                                    </View>
                                </TouchableOpacity>
                                {expandedSections.staff && (
                                    <FlatList
                                        data={filteredTrainers}
                                        keyExtractor={(item: any) =>
                                            item.userId
                                        }
                                        renderItem={({ item }) => (
                                            <TouchableOpacity
                                                onPress={() =>
                                                    handleTrainerSelect(item)
                                                }
                                                style={tw`py-2 border-b border-gray-200`}
                                            >
                                                <View
                                                    style={tw`flex flex-row justify-between items-center`}
                                                >
                                                    <Text
                                                        style={tw`text-[#455560]`}
                                                    >
                                                        {item.firstName}{' '}
                                                        {item.lastName}
                                                    </Text>
                                                    {selectedTrainer?.userId ===
                                                        item.userId && (
                                                        <Text
                                                            style={tw`text-primary`}
                                                        >
                                                            ✓
                                                        </Text>
                                                    )}
                                                </View>
                                            </TouchableOpacity>
                                        )}
                                    />
                                )}
                            </View>

                            <View
                                style={tw`flex flex-row justify-between pt-10 `}
                            >
                                <Button
                                    bgColor="bg-transparent"
                                    TextColor="text-[#455560]"
                                    TextSize="text-base"
                                    style={tw`w-[48%] rounded-lg text-[#455560] border-[#455560]  border`}
                                    onPress={handleClearFilter}
                                >
                                    Clear Filter
                                </Button>
                                <Button
                                    TextSize="text-base"
                                    style={tw`w-[48%] rounded-lg bg-transparent`}
                                    onPress={handleApply}
                                >
                                    Apply
                                </Button>
                            </View>
                        </View>
                    </View>
                </Animated.View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    button: {
        padding: 15,
        alignItems: 'center',
    },
    buttonText: {
        fontSize: 18,
        color: 'blue',
    },
});

export default FilterModal;
