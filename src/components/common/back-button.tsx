import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import { navigationRef } from '~/hooks/useLocation';

const BackButton = ({ goBack }: any) => {
    const onBack = () => {
        if (!goBack) {
            navigationRef.goBack();
        } else {
            goBack();
            // navigationRef.goBack();
        }
    };
    return (
        <>
            <View style={tw`w-[15%] mt-5`}>
                <View style={tw` bg-transparent pr-3 `}>
                    <TouchableOpacity onPress={onBack}>
                        <Image
                            source={require('../../assets/images/BackButton.png')}
                            style={[tw`w-10 h-10`, { resizeMode: 'contain' }]}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </>
    );
};

export default BackButton;

const styles = StyleSheet.create({});
