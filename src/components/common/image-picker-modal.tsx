import React from 'react';
import {
    Animated,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { openCamera, openGallery } from '~/scripts/image-picker-services';

const ImagePickerModal = ({ visible, onClose, onImagePicked }) => {
    const slideAnim = React.useRef(new Animated.Value(300)).current;

    React.useEffect(() => {
        if (visible) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 300,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    const handleOpenCamera = async () => {
        try {
            const image = await openCamera({
                width: 300,
                height: 400,
                cropping: true,
            });
            onImagePicked(image);
            toggleModal();
        } catch (error) {
            console.log('ImagePicker Error: ', error);
        }
    };

    const handleOpenGallery = async () => {
        try {
            const image = await openGallery({
                width: 300,
                height: 400,
                cropping: true,
            });
            onImagePicked(image);
            toggleModal();
        } catch (error) {
            console.log('ImagePicker Error: ', error);
        }
    };

    function toggleModal() {
        onClose();
    }

    return (
        <Modal
            transparent={true}
            visible={visible}
            animationType="none"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity style={styles.overlay} onPress={onClose} />
                <Animated.View
                    style={[
                        styles.modalContainer,
                        { transform: [{ translateY: slideAnim }] },
                    ]}
                >
                    <TouchableOpacity
                        onPress={handleOpenCamera}
                        style={{
                            alignItems: 'center',
                            flexDirection: 'row',
                            borderBottomColor: '#DEDEDE',
                            borderBottomWidth: 1,
                            paddingVertical: 15,
                            marginHorizontal: 5,
                        }}
                    >
                        <Image
                            source={Asset.CameraIcon}
                            style={tw`h-8 w-8 mx-2`}
                        />
                        <Text
                            style={{
                                color: '#75b9f6',
                            }}
                        >
                            Take Photo
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={handleOpenGallery}
                        style={{
                            alignItems: 'center',
                            flexDirection: 'row',
                            paddingVertical: 15,
                            marginHorizontal: 5,
                            borderBottomColor: '#DEDEDE',
                            borderBottomWidth: 1,
                        }}
                    >
                        <Image
                            source={Asset.GalleryIcon}
                            style={tw`h-8 w-8 mx-2`}
                        />
                        <Text
                            style={{
                                color: '#75b9f6',
                            }}
                        >
                            Choose Photo From Your Library
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={toggleModal}
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            paddingVertical: 15,
                            marginHorizontal: 5,
                            borderBottomColor: '#DEDEDE',
                            borderBottomWidth: 1,
                        }}
                    >
                        <Text style={tw`text-danger font-700`}>Cancel</Text>
                    </TouchableOpacity>
                </Animated.View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    button: {
        padding: 15,
        alignItems: 'center',
    },
    buttonText: {
        fontSize: 18,
        color: 'blue',
    },
});

export default ImagePickerModal;
