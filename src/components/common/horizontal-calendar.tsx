import moment from 'moment';
import React, { useEffect, useState } from 'react';
import {
    FlatList,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { Calendar } from 'react-native-calendars';
import Svg, { Path } from 'react-native-svg';

import tw from '~/styles/tailwind';

import CalendarIcon from '~/assets/svg/calendar_Icon.svg';

const ChevronLeft = (props: any) => (
    <Svg
        width={20}
        height={20}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        {...props}
    >
        <Path d="M15 18l-6-6 6-6" />
    </Svg>
);

const ChevronRight = (props: any) => (
    <Svg
        width={20}
        height={20}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        {...props}
    >
        <Path d="M9 18l6-6-6-6" />
    </Svg>
);

const HorizontalCalendar = () => {
    const [selectedDate, setSelectedDate] = useState(
        moment().format('YYYY-MM-DD')
    );
    const [isCalendarMinimized, setIsCalendarMinimized] = useState(true);
    const [dateRange, setDateRange] = useState<any>([]);

    useEffect(() => {
        updateDateRange(selectedDate);
    }, [selectedDate]);

    const updateDateRange = (date: any) => {
        const start = moment(date).subtract(3, 'days');
        const end = moment(date).add(3, 'days');
        const range = [];
        for (
            let m = moment(start);
            m.diff(end, 'days') <= 0;
            m.add(1, 'days')
        ) {
            range.push(m.format('YYYY-MM-DD'));
        }
        setDateRange(range);
    };

    const events = [
        {
            time: '09:00 AM',
            name: 'Rahul',
            activity: 'Animal Flow',
            color: '#E6E6FA',
        },
        {
            time: '10:00 AM',
            name: 'Group Class',
            activity: 'Cap: 10+',
            color: '#E0FFFF',
            duration: '1h',
        },
        {
            time: '01:00 PM',
            name: 'Vinay',
            activity: 'Boxing',
            color: '#FFE4E1',
        },
    ];

    const renderTimeSlot = (hour: any) => {
        const period = hour < 12 ? 'AM' : 'PM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        const timeString = `${displayHour
            .toString()
            .padStart(2, '0')}:00 ${period}`;

        const event = events.find((e) => e.time === timeString);

        return (
            <View style={styles.timeSlot} key={hour}>
                <View style={styles.timeContainer}>
                    <Text style={styles.timeText}>{displayHour}</Text>
                    <Text style={styles.periodText}>{period}</Text>
                </View>
                {event ? (
                    <View
                        style={[
                            styles.eventCard,
                            { backgroundColor: event.color },
                        ]}
                    >
                        <Text style={styles.eventName}>{event.name}</Text>
                        <Text style={styles.eventActivity}>
                            {event.activity}
                        </Text>
                        {event.duration && (
                            <Text style={styles.eventDuration}>
                                {event.duration}
                            </Text>
                        )}
                    </View>
                ) : (
                    <View style={styles.emptySlot} />
                )}
            </View>
        );
    };

    const renderSelectedDate = (dateString) => {
        return moment(dateString).format('dddd D MMMM YYYY');
    };

    const formatDate = (dateString) => {
        const date = moment(dateString).format('D'); // Date (e.g., 1, 2, 3, ...)
        const day = moment(dateString).format('ddd'); // Day (e.g., Mon, Tue, Wed, ...)
        return { date, day };
    };

    const renderDateItem = ({ item }) => {
        const { date, day } = formatDate(item);

        return (
            <TouchableOpacity
                style={[
                    styles.dateItem,
                    item === selectedDate && styles.selectedDateItem,
                ]}
                onPress={() => setSelectedDate(item)}
            >
                <View style={styles.dateItemContainer}>
                    <Text
                        style={[
                            styles.dateItemText,
                            item === selectedDate &&
                                styles.selectedDateItemText,
                        ]}
                    >
                        {date}
                    </Text>
                    <Text
                        style={[
                            styles.dayItemText,
                            item === selectedDate && styles.selectedDayItemText,
                        ]}
                    >
                        {day}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    };

    return (
        <View style={styles.container}>
            {isCalendarMinimized ? (
                <View>
                    <TouchableOpacity
                        style={[
                            styles.expandButton,
                            tw`flex flex-row justify-between items-center`,
                        ]}
                        onPress={() => setIsCalendarMinimized(false)}
                    >
                        <Text style={styles.selectedDateText}>
                            {renderSelectedDate(selectedDate)}
                        </Text>
                        <CalendarIcon />
                    </TouchableOpacity>
                    <FlatList
                        horizontal
                        data={dateRange}
                        renderItem={renderDateItem}
                        keyExtractor={(item) => item}
                        showsHorizontalScrollIndicator={false}
                        style={styles.dateList}
                    />
                </View>
            ) : (
                <Calendar
                    current={selectedDate}
                    onDayPress={(day: any) => {
                        setSelectedDate(day.dateString);
                        setIsCalendarMinimized(true);
                    }}
                    monthFormat={'MMMM yyyy'}
                    renderArrow={(direction) =>
                        direction === 'left' ? (
                            <ChevronLeft stroke="#000" />
                        ) : (
                            <ChevronRight stroke="#000" />
                        )
                    }
                    markedDates={{
                        [selectedDate]: {
                            selected: true,
                            selectedColor: '#000',
                        },
                    }}
                    theme={{
                        todayTextColor: '#000',
                        selectedDayBackgroundColor: '#000',
                        selectedDayTextColor: '#fff',
                    }}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        // flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,

        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerText: {
        marginLeft: 10,
        fontSize: 16,
        fontWeight: 'bold',
    },
    dateList: {
        paddingVertical: 4,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    dateItem: {
        paddingHorizontal: 15,
        paddingVertical: 5,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    selectedDateItem: {
        backgroundColor: '#000',
    },
    dateItemText: {
        fontSize: 14,
    },
    selectedDateItemText: {
        color: '#fff',
    },
    expandButton: {
        alignItems: 'center',
        padding: 10,
        backgroundColor: '#f0f0f0',
    },
    expandButtonText: {
        color: '#000',
        fontWeight: 'bold',
    },
    schedule: {
        flex: 1,
    },

    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
    },
    headerText: {
        marginLeft: 10,
        fontSize: 16,
        fontWeight: 'bold',
    },
    minimizedCalendar: {
        padding: 10,
        backgroundColor: '#f0f0f0',
        alignItems: 'center',
    },
    selectedDateText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    schedule: {
        flex: 1,
    },
    timeSlot: {
        flexDirection: 'row',
        height: 60,
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    timeContainer: {
        width: 50,
        alignItems: 'flex-end',
        marginRight: 10,
    },
    timeText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    periodText: {
        fontSize: 12,
    },
    eventCard: {
        flex: 1,
        padding: 10,
        borderRadius: 5,
        marginRight: 10,
    },
    emptySlot: {
        flex: 1,
    },
    eventName: {
        fontWeight: 'bold',
    },
    eventActivity: {
        fontSize: 12,
    },
    eventDuration: {
        fontSize: 12,
        position: 'absolute',
        right: 5,
        bottom: 5,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        padding: 10,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    footerButton: {
        padding: 10,
    },
    dateItemContainer: {
        flexDirection: 'column', // Makes date and day appear in a column layout
        alignItems: 'center', // Centers the text in the column
    },
    dateItemText: {
        fontSize: 11, // Customize this based on your design
        fontWeight: 'bold',
    },
    dayItemText: {
        fontSize: 11, // Customize this based on your design
        color: 'gray',
    },
    selectedDateItemText: {
        color: 'white', // Change color/style when date is selected
    },
    selectedDayItemText: {
        color: 'white', // Change color/style when day is selected
    },
    // ... (Keep the rest of your existing styles)
});

export default HorizontalCalendar;
