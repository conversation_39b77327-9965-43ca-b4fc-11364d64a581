import React, { useState } from 'react';
import {
    FlatList,
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import TextInput from '../atoms/text-input';

const suggestions = [
    {
        id: 1,
        name: 'OPEN CELL ACCOSOUND SHEET 6MM : Pack of 15',
        category: 'Test',
    },
    {
        id: 2,
        name: 'OPEN CELL ACCOSOUND SHEET 8MM : Pack of 10',
        category: 'Test',
    },
    {
        id: 3,
        name: 'OPEN CELL ACCOSOUND SHEET 10MM : Pack of 20',
        category: 'Test',
    },
    {
        id: 4,
        name: 'OPEN CELL ACCOSOUND SHEET 12MM : Pack of 15',
        category: 'Test',
    },
    {
        id: 5,
        name: 'OPEN CELL ACCOSOUND SHEET 15MM : Pack of 8',
        category: 'Test',
    },
    {
        id: 6,
        name: 'ACCOUSTIC FOAM PANEL 25MM : Pack of 12',
        category: 'Foam',
    },
    {
        id: 7,
        name: 'ACCOUSTIC FOAM PANEL 50MM : Pack of 6',
        category: 'Foam',
    },
    {
        id: 8,
        name: 'ACCOUSTIC BARRIER SHEET 5MM : Pack of 20',
        category: 'Barrier',
    },
];

const SearchBar = () => {
    const [searchText, setSearchText] = useState('');
    const [filteredSuggestions, setFilteredSuggestions] = useState([]);

    const handleInputChange = (text: string) => {
        setSearchText(text);

        // Filter suggestions based on input
        if (text.length > 0) {
            const filtered = suggestions.filter((item) =>
                item.name.toLowerCase().includes(text.toLowerCase())
            );
            setFilteredSuggestions(filtered);
        } else {
            setFilteredSuggestions([]);
        }
    };
    return (
        <View>
            <View
                style={tw`bg-[#F7F7F7] rounded-xl my-3 shadow-md py-1 px-2 flex flex-row justify-between items-center`}
            >
                <View style={tw`w-[10%]`}>
                    <Image
                        resizeMode="contain"
                        style={tw`w-6 h-6`}
                        source={Asset.SearchIcon}
                    />
                </View>
                <View style={tw`w-[75%]`}>
                    <TextInput
                        value={searchText}
                        onChangeText={handleInputChange}
                        placeholder={'Search'}
                        style={tw` px-2   text-[#3b3b3b]  text-14 font-400`}
                    />
                </View>
                <TouchableOpacity
                    style={tw`w-[15%] flex justify-center items-center`}
                >
                    {/* <FontAwesome name="search" size={20} color="#3b3b3b" /> */}
                    <Image
                        resizeMode="contain"
                        style={tw`w-7 h-7`}
                        source={Asset.FilterIcon}
                    />
                </TouchableOpacity>
            </View>
            {/* Suggestion List */}
            {filteredSuggestions.length > 0 && (
                <View style={tw`bottom-2 bg-white rounded-lg shadow-lg`}>
                    <FlatList
                        data={filteredSuggestions}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={({ item }) => (
                            <TouchableOpacity
                                style={tw`p-2 border-b border-gray-200 flex flex-row justify-between w-[100%]`}
                                onPress={() => {
                                    setSearchText(item.name);
                                    setFilteredSuggestions([]);
                                }}
                            >
                                <Text
                                    style={tw`text-[#3b3b3b] text-sm w-[70%]`}
                                >
                                    {item.name}
                                </Text>
                                <Text
                                    style={tw`text-[#3b3b3b] bg-[#f2f2f2] text-sm rounded-sm w-fit p-2 text-center flex justify-center items-center`}
                                >
                                    {item.category}
                                </Text>
                            </TouchableOpacity>
                        )}
                    />
                </View>
            )}
        </View>
    );
};

export default SearchBar;

const styles = StyleSheet.create({});
