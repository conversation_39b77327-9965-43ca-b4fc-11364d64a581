// RadioButtonGroup.tsx
import React, { useState } from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    ViewStyle,
} from 'react-native';

import tw from '~/styles/tailwind';

// Define types for props
interface RadioOption {
    label: string;
    value: string | number;
}

interface RadioButtonGroupProps {
    options: RadioOption[];
    selectedValue?: string | number;
    onValueChange?: any;
    style?: ViewStyle;
}

const RadioButtonGroup: React.FC<RadioButtonGroupProps> = ({
    options,
    selectedValue = null,
    onValueChange,
    style,
}) => {
    const [selected, setSelected] = useState<string | number | null>(
        selectedValue
    );

    const handleSelect = (value: string | number | any) => {
        setSelected(value);
        onValueChange(value);
    };

    return (
        <View style={[styles.container, style]}>
            {options?.filter(Boolean).map((option, index) => (
                <TouchableOpacity
                    key={index}
                    style={[tw`flex`, styles.radioButton]}
                    onPress={() => handleSelect(option.value)}
                >
                    <View style={styles.outerCircle}>
                        {selected === option.value && (
                            <View style={styles.innerCircle} />
                        )}
                    </View>
                    <Text style={styles.optionText}>{option.label}</Text>
                </TouchableOpacity>
            ))}
        </View>
    );
};

// Styling
const styles = StyleSheet.create({
    container: {
        flexDirection: 'column',
        justifyContent: 'center',
    },
    radioButton: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    outerCircle: {
        height: 16,
        width: 16,
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#455560',
        justifyContent: 'center',
        alignItems: 'center',
        color: '#455560',
    },
    innerCircle: {
        height: 8,
        width: 8,
        borderRadius: 5,
        backgroundColor: '#455560',
    },
    optionText: {
        marginLeft: 10,
        fontSize: 14,
        color: '#455560',
    },
});

export default RadioButtonGroup;
