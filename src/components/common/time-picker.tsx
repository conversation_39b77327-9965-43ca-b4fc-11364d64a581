import DateTimePicker, {
    DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import React, { useEffect, useState } from 'react';
import {
    Button,
    Image,
    Platform,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

interface TimePickerProps {
    title: any;
    value: string; // Add value prop
    onChange: (time: string) => void; // Ensure onChange is defined
    isOpen?: boolean;
    placeholder?: any;
}

const TimePicker: React.FC<TimePickerProps> = ({
    title,
    value,
    onChange,
    isOpen,
    placeholder,
}) => {
    // Convert the value prop to a Date object
    const [time, setTime] = useState<Date>(() => {
        if (!value) {
            return new Date();
        }
        const [hours, minutes] = value.split(':').map(Number);
        const date = new Date();
        date.setHours(hours, minutes);
        return date;
    });
    const [show, setShow] = useState<boolean>(false);

    const formatTo24Hour = (date: Date) => {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    };

    useEffect(() => {
        if (value) {
            const [hours, minutes] = value.split(':').map(Number);
            const updatedTime = new Date();
            updatedTime.setHours(hours, minutes, 0, 0);
            setTime(updatedTime);
        }
    }, [value]);

    const onTimeChange = (event: DateTimePickerEvent, selectedTime?: Date) => {
        const currentTime = selectedTime || time;
        setShow(Platform.OS === 'ios');
        setTime(currentTime);
        onChange && onChange(formatTo24Hour(currentTime));
    };

    const showTimepicker = () => {
        setShow(true);
    };

    const formatTime = (date: Date): string => {
        if (!value) {
            // return title === 'START TIME' ? 'Start Time' : 'Start Time';
            return placeholder;
        }
        let hours = date.getHours();
        let minutes: any = date.getMinutes();
        let ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12;
        minutes = minutes < 10 ? '0' + minutes : minutes;
        return `${hours}:${minutes} ${ampm}`;
    };

    return (
        <View style={tw`mt-2.5 w-full`}>
            <Text style={tw`text-MainTextColor text-[13px] font-medium pb-2`}>
                {title}
            </Text>
            <TouchableOpacity
                style={tw`w-auto border-b border-[#45556066] flex flex-row justify-between px-1`}
                onPress={showTimepicker}
            >
                <Text style={tw`text-[#455560] text-[13px]`}>
                    {formatTime(time)}{' '}
                </Text>
                <Image style={tw`w-3 h-4 mb-2`} source={Asset.UpArrows} />
                {show && (
                    <DateTimePicker
                        value={time}
                        mode="time"
                        disabled={isOpen}
                        display="default"
                        onChange={onTimeChange}
                        is24Hour={false}
                        // minuteInterval={15}
                    />
                )}
            </TouchableOpacity>
        </View>
    );
};

export default TimePicker;
