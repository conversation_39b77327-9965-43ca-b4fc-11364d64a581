import React from 'react';
import {
    Animated,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { UserRole } from '~/constants/enums';
import {
    AVAILABILITY_SCREEN,
    BOOK_APPOINTMENT,
    QR_CODE_SCANNER,
} from '~/constants/navigation-constant';

import { navigationRef, useLocation } from '~/hooks/useLocation';

const AvailabilityModal = ({
    visible,
    onClose,
    selectedDate,
    setDeleteCustomRangeModal,
    role,
}: any) => {
    const { setLocation } = useLocation();
    const slideAnim = React.useRef(new Animated.Value(300)).current;

    React.useEffect(() => {
        if (visible) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 300,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    function toggleModal() {
        onClose();
    }

    function handleNavigation(type: any) {
        if (type === 'availability') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: 'available',
                selectedDate,
            });
        } else {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: 'unavailable',
                selectedDate,
            });
        }
    }

    return (
        <Modal
            transparent={true}
            visible={visible}
            animationType="none"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity style={styles.overlay} onPress={onClose} />
                <Animated.View
                    style={[
                        styles.modalContainer,
                        { transform: [{ translateY: slideAnim }] },
                    ]}
                >
                    <TouchableOpacity
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            paddingVertical: 15,
                            marginHorizontal: 5,
                            borderBottomColor: '#DEDEDE',
                            borderBottomWidth: 1,
                        }}
                        onPress={() =>
                            (navigationRef as any).navigate(BOOK_APPOINTMENT)
                        }
                    >
                        <Text
                            style={{
                                color: '#455560',
                            }}
                        >
                            Book Session
                        </Text>
                    </TouchableOpacity>
                    {role !== UserRole.Trainer && (
                        <>
                            <TouchableOpacity
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderBottomColor: '#DEDEDE',
                                    borderBottomWidth: 1,
                                    paddingVertical: 15,
                                    marginHorizontal: 5,
                                }}
                                onPress={() => handleNavigation('availability')}
                            >
                                <Text
                                    style={{
                                        color: '#455560',
                                    }}
                                >
                                    Add New Shift
                                </Text>
                            </TouchableOpacity>
                            {/* <TouchableOpacity
                                style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderBottomColor: '#DEDEDE',
                                    borderBottomWidth: 1,
                                    paddingVertical: 15,
                                    marginHorizontal: 5,
                                }}
                                onPress={() => handleNavigation('availability')}
                            >
                                <Text
                                    style={{
                                        color: '#455560',
                                    }}
                                >
                                    Edit Shift(s)
                                </Text>
                            </TouchableOpacity> */}
                        </>
                    )}
                    <TouchableOpacity
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            paddingVertical: 15,
                            marginHorizontal: 5,
                            borderBottomColor: '#DEDEDE',
                            borderBottomWidth: 1,
                        }}
                        onPress={() => handleNavigation('unavailability')}
                    >
                        <Text
                            style={{
                                color: '#455560',
                            }}
                        >
                            Add Unavailability
                        </Text>
                    </TouchableOpacity>
                    {role !== UserRole.Trainer && (
                        <TouchableOpacity
                            style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexDirection: 'row',
                                paddingVertical: 15,
                                marginHorizontal: 5,
                                borderBottomColor: '#DEDEDE',
                                borderBottomWidth: 1,
                            }}
                            onPress={() => {
                                onClose();
                                setDeleteCustomRangeModal(true);
                            }}
                        >
                            <Text
                                style={{
                                    color: '#455560',
                                }}
                            >
                                Clear Shift(s)
                            </Text>
                        </TouchableOpacity>
                    )}

                    <TouchableOpacity
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            paddingVertical: 15,
                            marginHorizontal: 5,
                            borderBottomColor: '#DEDEDE',
                            borderBottomWidth: 1,
                        }}
                        onPress={() =>
                            (navigationRef as any).navigate(QR_CODE_SCANNER)
                        }
                    >
                        <Text
                            style={{
                                color: '#455560',
                            }}
                        >
                            Scan For Booking
                        </Text>
                    </TouchableOpacity>
                    {/* <TouchableOpacity
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            paddingVertical: 15,
                            marginHorizontal: 5,
                        }}
                        onPress={onClose}
                    >
                        <Text style={tw`text-[#455560] font-700`}>Cancel</Text>
                    </TouchableOpacity> */}
                </Animated.View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    button: {
        padding: 15,
        alignItems: 'center',
    },
    buttonText: {
        fontSize: 18,
        color: 'blue',
    },
});

export default AvailabilityModal;
