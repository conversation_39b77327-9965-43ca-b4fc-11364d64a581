import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

interface CheckboxProps {
    label: string;
    onValueChange: (value: boolean) => void;
    value: boolean;
}

const CustomCheckbox: React.FC<CheckboxProps> = ({
    label,
    onValueChange,
    value,
}) => {
    const handlePress = () => {
        onValueChange(!value);
    };

    return (
        <TouchableOpacity style={styles.container} onPress={handlePress}>
            <View style={styles.checkbox}>
                {value ? (
                    <Image
                        resizeMode="cover"
                        style={tw`w-5 h-5`}
                        source={Asset.CheckboxChecked}
                    />
                ) : (
                    <Image
                        resizeMode="contain"
                        style={tw`w-5 h-5`}
                        source={Asset.CheckboxUnchecked}
                    />
                )}
            </View>
            <Text style={styles.label}>{label}</Text>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        // margin: 10,
    },
    checkbox: {
        // marginRight: 10,
    },
    label: {
        fontSize: 16,
    },
});

export default CustomCheckbox;
