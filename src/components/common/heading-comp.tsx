import { useNavigation } from '@react-navigation/native';
import React, { memo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import tw from '~/styles/tailwind';

import BackIcon from '~/assets/svg/back-arrow.svg';

import { navigationRef } from '~/hooks/useLocation';

const HeadingComp = (Heading: string) => {
    const navigation = useNavigation();
    return (
        <View style={tw`bg-#D0FF01] w-[100%] h-32 px-3 pt-5`}>
            <View style={tw`flex flex-row items-center gap-3`}>
                <TouchableOpacity
                    style={tw`bg-black rounded-full p-2 h-10 w-10 flex justify-center items-center`}
                    onPress={() => navigationRef.goBack()}
                >
                    <BackIcon width={15} height={15} />
                </TouchableOpacity>
                <Text
                    style={tw`text-[#455560] text-[20px] uppercase font-bold`}
                >
                    {Heading}
                </Text>
            </View>
        </View>
    );
};

export default memo(HeadingComp);
