import { Image, TouchableOpacity, View } from 'react-native';
import { connect } from 'react-redux';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { UserRole } from '~/constants/enums';
import {
    BOOK_CLASSES,
    CALE<PERSON>AR_SCREEN,
    HOME_SCREEN,
    ORDER_LISTING,
    STAFF_DETAIL,
    STAFF_PROFILE,
} from '~/constants/navigation-constant';
import { TAB_FOOTER_SCREENS } from '~/constants/special-navigation-constant';

import Text from '~/components/library/text';

import { useAppSelector } from '~/hooks/redux-hooks';
import { navigationRef, useLocation } from '~/hooks/useLocation';

const TabFooter = (props) => {
    const { setLocation } = useLocation();
    const store = useAppSelector((state) => ({
        userId: state.auth_store.userId,
    }));

    const isActiveScreen = (currentScreen, tabScreens) => {
        return tabScreens?.includes(currentScreen);
    };

    //! remove this function as soon as possible
    function handleNavigation(screenName) {
        navigationRef.navigate(screenName);
    }
    console.log('User Role:', props.user_role);
    // return null;

    return (
        <>
            {props.show_tab_footer ? (
                <View style={tw`w-full flex justify-center items-center`}>
                    <View
                        style={tw`flex flex-row items-center justify-around py-2.5 rounded-full px-5 bottom-.5 w-[95%] bg-primary shadow-md`}
                    >
                        <TouchableOpacity
                            onPress={() => handleNavigation(HOME_SCREEN)}
                            style={tw` w-[25%]`}
                        >
                            <View
                                style={tw`flex justify-center gap-1 items-center`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={[
                                        tw`w-6 h-6`,
                                        {
                                            opacity:
                                                props.current_screen ===
                                                    HOME_SCREEN
                                                    ? 1
                                                    : 0.5,
                                        },
                                    ]}
                                    // source={Asset.HomeWhiteIcon}
                                    source={
                                        props.current_screen === HOME_SCREEN
                                            ? Asset.HomeIconFooter
                                            : Asset.HomeWhiteIcon
                                    }
                                />
                                <Text
                                    style={tw`text-[14px] mt-1 text-white text-center opacity-70 ${props.current_screen === HOME_SCREEN
                                        ? 'text-[14px] font-500 opacity-100'
                                        : ''
                                        }`}
                                >
                                    Home
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                // Check the role and navigate accordingly
                                if (props.user_role === UserRole.User) {
                                    handleNavigation(BOOK_CLASSES);
                                } else if (props.user_role !== UserRole.User) {
                                    handleNavigation(CALENDAR_SCREEN);
                                }
                            }}
                            style={tw` w-[25%]`}
                        >
                            <View
                                style={tw`flex justify-center gap-1 items-center`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={[
                                        tw`w-6 h-6`,
                                        {
                                            opacity:
                                                props.current_screen ===
                                                    CALENDAR_SCREEN
                                                    ? 1
                                                    : 0.5,
                                        },
                                    ]}
                                    source={
                                        props.user_role !== UserRole.User &&
                                            props.current_screen === CALENDAR_SCREEN
                                            ? Asset.ScheduleWhiteIcon
                                            : Asset.ScheduleIcon
                                    }
                                />
                                <Text
                                    style={tw`text-[14px] mt-1 text-white text-center opacity-70 ${props.current_screen === CALENDAR_SCREEN
                                        ? 'text-[14px] font-500 opacity-100'
                                        : ''
                                        }`}
                                >
                                    {props.user_role !== UserRole.User &&
                                        'Schedule'}
                                </Text>
                            </View>
                        </TouchableOpacity>

                        {/* <TouchableOpacity
                            onPress={() => {
                                // if (props.user_role === UserRole.User) {
                                //     handleNavigation(PROFILE_SCREEN);
                                // } else if (props.user_role !== UserRole.User) {
                                //     handleNavigation(STAFF_PROFILE);
                                // }
                                navigationRef.navigate(STAFF_DETAIL, {
                                    staffDetail: store.userId,
                                });
                            }}
                            style={tw` w-[25%]`}
                        >
                            <View
                                style={tw`flex justify-center gap-1 items-center`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={[
                                        tw`w-6 h-6`,
                                        {
                                            opacity:
                                                props.current_screen ===
                                                STAFF_DETAIL
                                                    ? 1
                                                    : 0.5,
                                        },
                                    ]}
                                    source={
                                        props.current_screen === STAFF_DETAIL
                                            ? Asset.ProfileWhiteIcon
                                            : Asset.ProfileWhite
                                    }
                                />
                                <Text
                                    style={tw`text-[14px] mt-1 text-white text-center opacity-70 ${
                                        props.current_screen === STAFF_DETAIL
                                            ? 'text-[14px] font-500 opacity-100'
                                            : ''
                                    }`}
                                >
                                    Profile
                                </Text>
                            </View>
                        </TouchableOpacity> */}

                        <TouchableOpacity
                            onPress={() => {
                                navigationRef.navigate(ORDER_LISTING);
                            }}
                            style={tw` w-[25%]`}
                        >
                            <View
                                style={tw`flex justify-center gap-1 items-center`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={[
                                        tw`w-6 h-6`,
                                        {
                                            opacity:
                                                props.current_screen ===
                                                    ORDER_LISTING
                                                    ? 1
                                                    : 0.5,
                                        },
                                    ]}
                                    source={
                                        props.current_screen === ORDER_LISTING
                                            ? Asset.OrderIcon
                                            : Asset.OrderIcon
                                    }
                                />
                                <Text
                                    style={tw`text-[14px] mt-1 text-white text-center opacity-70 ${props.current_screen === ORDER_LISTING
                                        ? 'text-[14px] font-500 opacity-100'
                                        : ''
                                        }`}
                                >
                                    Orders
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => handleNavigation(STAFF_PROFILE)}
                            style={tw` w-[25%]`}
                        >
                            <View
                                style={tw`flex justify-center gap-1 items-center`}
                            >
                                <Image
                                    resizeMode="contain"
                                    style={[
                                        tw`w-6 h-6`,
                                        {
                                            opacity:
                                                props.current_screen ===
                                                    STAFF_PROFILE
                                                    ? 1
                                                    : 0.5,
                                        },
                                    ]}
                                    // source={Asset.MoreWhiteIcon}
                                    source={
                                        props.current_screen === STAFF_PROFILE
                                            ? Asset.MoreIconFooter
                                            : Asset.MoreWhiteIcon
                                    }
                                />
                                <Text
                                    style={tw`text-[14px] mt-1 text-white text-center opacity-70 ${props.current_screen === STAFF_PROFILE
                                        ? 'text-[14px] font-500 opacity-100'
                                        : ''
                                        }`}
                                >
                                    More
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            ) : null}
        </>
    );
};
const _showTabFooter = (screen_name) => {
    if (TAB_FOOTER_SCREENS.includes(screen_name)) {
        return true;
    }
};

const mapStateToProps = (state) => ({
    show_tab_footer: _showTabFooter(state?.layout_store?.screenName),
    current_screen: state?.layout_store?.screenName,
    user_role: state.auth_store.role,
});

export default connect(mapStateToProps, null)(TabFooter);
