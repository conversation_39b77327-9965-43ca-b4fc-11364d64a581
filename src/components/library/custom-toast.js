import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';

import tw from '~/styles/tailwind';

import Button from '~/components/atoms/button';

const CustomToast = ({
    message,
    success,
    error,
    action_text,
    action_callback = () => {},
}) => {
    return (
        <View
            style={[
                tw`flex flex-row items-center mx-2.5 relative  px-2.5 py-1 rounded-md`,
                { minHeight: 48 },
                styles.toastContainer,
                success && styles.success,
                error && styles.error,
            ]}
        >
            <View style={tw`flex-1`}>
                <Text style={styles.toastText}>{message}</Text>
            </View>
            {!!action_text ? (
                <View>
                    <Button onPress={action_callback} link warning>
                        {action_text}
                    </Button>
                </View>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    toastContainer: {
        padding: 10,
        borderRadius: 5,
        backgroundColor: 'rgba(0, 0, 0, 0.8)', // Default background color
        alignItems: 'center', // Center the text horizontally
        justifyContent: 'center', // Center the text vertically
    },
    toastText: {
        color: 'white', // Change text color to white
        textAlign: 'center', // Center the text
    },
    success: {
        backgroundColor: 'green', // Background color for success
    },
    error: {
        backgroundColor: 'red', // Background color for error
    },
});

export default memo(CustomToast);
