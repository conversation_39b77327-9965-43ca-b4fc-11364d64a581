import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import {
    Animated,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {
    BookedCalendarData,
    CancelScheduling,
    CheckInScheduling,
} from '~/redux/actions/scheduling-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import {
    AVAILABILITY_SCREEN,
    BOOK_APPOINTMENT,
} from '~/constants/navigation-constant';

import {
    capitalizeFirstLetter,
    formatTime,
    serviceTypes,
    serviceTypesCaps,
} from '~/scripts/function';

import { useAppDispatch } from '~/hooks/redux-hooks';
import { navigationRef, useLocation } from '~/hooks/useLocation';

import ConfirmationModal from '../common/confirmation-modal';

interface BookingSlotModalProps {
    visible?: boolean;
    onClose?: any;
    selectedDate?: string;
    setShowDeleteModal?: (visible: boolean) => void;
    setDeleteCustomRangeModal?: (visible: boolean) => void;
    event?: any;
    setBookedPopupModal?: any;
}

const BookingSlotModal: React.FC<BookingSlotModalProps> = ({
    visible,
    onClose,
    selectedDate,
    setShowDeleteModal,
    setDeleteCustomRangeModal,
    event,
    setBookedPopupModal,
}) => {
    const { setLocation } = useLocation();
    const dispatch = useAppDispatch();
    const slideAnim = React.useRef(new Animated.Value(300)).current;
    const [showModal, setShowModal] = React.useState(false);
    const [modalMessage, setModalMessage] = useState<string>('');
    const [modalTitle, setModalTitle] = useState<string>('');
    const [confirmAction, setConfirmAction] = useState<() => void>(() => {});

    console.log('event---------------', event);

    const isEditable =
        event &&
        (dayjs().isBefore(dayjs(event.start)) ||
            dayjs().isSame(dayjs(event.start)));

    const handleCancel = () => {
        console.log('Action Canceled');
        setShowModal(false);
    };

    useEffect(() => {
        if (visible) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 300,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    const deleteBookedAppointment = () => {
        dispatch(
            CancelScheduling({
                scheduleId: event?.id,
            })
        )
            .unwrap()
            .then((res: any) => {
                console.log('Res deleted-----------------', res);
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    dispatch(
                        BookedCalendarData({
                            classType: event?.classType,
                            startDate: dayjs(event?.start)
                                .startOf('day')
                                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                            endDate: dayjs(event?.start)
                                .endOf('day')
                                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                        })
                    );
                    setShowModal(false);
                    onClose();
                }
            });
    };

    const confirmCheckin = () => {
        dispatch(
            CheckInScheduling({
                scheduleId: event?.id,
            })
        )
            .unwrap()
            .then((res: any) => {
                console.log('Res deleted-----------------', res);
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    dispatch(
                        BookedCalendarData({
                            classType: event?.classType,
                            startDate: dayjs(event?.start)
                                .startOf('day')
                                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                            endDate: dayjs(event?.start)
                                .endOf('day')
                                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                        })
                    );
                    setShowModal(false);
                    onClose();
                }
            });
    };

    const openConfirmationModal = (actionType: string) => {
        if (!event) return;

        const eventDate = dayjs(event.start).format(
            'dddd, MMMM D, YYYY [at] hh:mm A'
        );

        let message: any = '';
        let title = '';
        let action = () => {};
        // setBookedPopupModal(false);

        if (actionType === 'cancel') {
            title = 'Confirm Cancellation';
            message = (
                <Text>
                    The following{' '}
                    {capitalizeFirstLetter(
                        capitalizeFirstLetter(
                            serviceTypes[
                                event.classType as keyof typeof serviceTypes
                            ]
                        )
                    )}{' '}
                    {['personalAppointment', 'bookings'].includes(
                        event.classType
                    ) && (
                        <>
                            for{' '}
                            <Text style={{ fontWeight: 'bold' }}>
                                {capitalizeFirstLetter(event.clientName)}
                            </Text>{' '}
                        </>
                    )}
                    will be Cancelled on{' '}
                    <Text style={{ fontWeight: 'bold' }}>
                        {dayjs(event.start).format(
                            'dddd, MMMM D, YYYY [at] hh:mm A'
                        )}
                    </Text>
                    .
                </Text>
            );
            action = deleteBookedAppointment;
        } else if (actionType === 'checkin') {
            title = 'Confirm Check-In';
            message = (
                <Text>
                    Are you sure you want to check in{' '}
                    <Text style={{ fontWeight: 'bold' }}>
                        {event.clientName}
                    </Text>{' '}
                    for their{' '}
                    {capitalizeFirstLetter(
                        serviceTypes[
                            event.classType as keyof typeof serviceTypes
                        ]
                    )}{' '}
                    on{' '}
                    <Text style={{ fontWeight: 'bold' }}>
                        {dayjs(event.start).format(
                            'dddd, MMMM D, YYYY [at] hh:mm A'
                        )}
                    </Text>
                    ?
                </Text>
            );
            action = confirmCheckin;
        }

        // onClose?.();

        setModalTitle(title);
        setModalMessage(message);
        setConfirmAction(() => action);
        setShowModal(true);
    };

    function handleNavigation(event: any, type: string) {
        console.log('Event-------------', event);
        if (type === 'view') {
            (navigationRef as any).navigate(BOOK_APPOINTMENT, {
                scheduleId: event?.id,
                type: 'view',
                isDetails: true,
                bookClassType: event?.classType,
            });
        } else if (type === 'edit') {
            (navigationRef as any).navigate(BOOK_APPOINTMENT, {
                scheduleId: event?.id,
                type: 'edit',
                isDetails: true,
                bookClassType: event?.classType,
            });
        }
        //  else if (type === 'cancel') {
        //     navigationRef.navigate(BOOK_APPOINTMENT, {
        //         scheduleId: event?.id,
        //         type: 'cancel',
        //     });
        // }
    }

    return (
        <Modal
            transparent={true}
            visible={visible}
            animationType="none"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity style={styles.overlay} onPress={onClose} />
                <Animated.View
                    style={[
                        styles.modalContainer,
                        { transform: [{ translateY: slideAnim }] },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                    >
                        {/* <Text style={styles.closeButtonText}>×</Text> */}
                        <Image
                            style={tw` w-4 h-4`}
                            source={Asset.CloseIcon}
                            resizeMode="contain"
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() => handleNavigation(event, 'view')}
                    >
                        <Text style={styles.optionText}>
                            View{' '}
                            {serviceTypesCaps[
                                event.classType as keyof typeof serviceTypes
                            ] || 'Appointment'}
                        </Text>
                    </TouchableOpacity>
                    {/* <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() =>
                            handleNavigation('availability', 'Multiple')
                        }
                    >
                        <Text style={styles.optionText}>
                            Edit Multiple Availability
                        </Text>
                    </TouchableOpacity> */}
                    <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() => handleNavigation(event, 'edit')}
                        disabled={!isEditable}
                    >
                        <Text
                            style={[
                                styles.optionText,
                                !isEditable && styles.disabledText,
                            ]}
                        >
                            Edit{' '}
                            {serviceTypesCaps[
                                event.classType as keyof typeof serviceTypes
                            ] || 'Appointment'}
                        </Text>
                    </TouchableOpacity>

                    {event?.scheduleStatus !== 'checked-in' && (
                        <TouchableOpacity
                            onPress={() => openConfirmationModal('cancel')}
                            style={styles.optionButton}
                            disabled={!isEditable}
                        >
                            <Text
                                style={[
                                    styles.optionText,
                                    !isEditable && styles.disabledText,
                                ]}
                            >
                                Cancel{' '}
                                {capitalizeFirstLetter(
                                    serviceTypes[
                                        event.classType as keyof typeof serviceTypes
                                    ]
                                ) || 'Appointment'}
                            </Text>
                        </TouchableOpacity>
                    )}

                    {/* <TouchableOpacity
                        onPress={() => openConfirmationModal('checkin')}
                        style={styles.optionButton}
                        disabled={!isEditable}
                    >
                        <Text
                            style={[
                                styles.optionText,
                                !isEditable && styles.disabledText,
                            ]}
                        >
                            Check-In
                        </Text>
                    </TouchableOpacity> */}
                    {(event?.classType === 'personalAppointment' ||
                        event?.classType === 'bookings') &&
                        event?.scheduleStatus !== 'checked-in' && (
                            <TouchableOpacity
                                onPress={() => openConfirmationModal('checkin')}
                                style={styles.optionButton}
                                disabled={!isEditable}
                            >
                                <Text
                                    style={[
                                        styles.optionText,
                                        !isEditable && styles.disabledText,
                                    ]}
                                >
                                    Check-In
                                </Text>
                            </TouchableOpacity>
                        )}
                </Animated.View>
            </View>
            <ConfirmationModal
                isVisible={showModal}
                title={modalTitle}
                message={modalMessage}
                confirmText="Confirm"
                cancelText="Cancel"
                onConfirm={confirmAction}
                onCancel={handleCancel}
            />
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 20,
        width: '100%',
        alignItems: 'center',
    },
    closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 1,
        bottom: 10,
    },
    disabledText: {
        color: '#A9A9A9',
    },

    closeButtonText: {
        fontSize: 24,
        color: '#455560',
    },
    optionButton: {
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        borderBottomColor: '#DEDEDE',
        borderBottomWidth: 1,
        paddingVertical: 15,
        marginHorizontal: 5,
        width: '100%',
    },
    optionText: {
        color: '#455560',
    },
});

export default BookingSlotModal;
