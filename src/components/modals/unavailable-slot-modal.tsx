import React from 'react';
import {
    Animated,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { AVAILABILITY_SCREEN } from '~/constants/navigation-constant';

import { formatTime } from '~/scripts/function';

import { navigationRef } from '~/hooks/useLocation';

interface AvailabilitySlotModalProps {
    visible: boolean;
    onClose: () => void;
    selectedDate?: string;
    setShowDeleteModal?: (visible: boolean) => void;
    setDeleteCustomRangeModal?: any;
    event?: any;
}

const UnavailableSlotModal: React.FC<AvailabilitySlotModalProps> = ({
    visible,
    onClose,
    selectedDate,
    setShowDeleteModal = () => {},
    setDeleteCustomRangeModal,
    event,
}) => {
    const slideAnim = React.useRef(new Animated.Value(300)).current;

    React.useEffect(() => {
        if (visible) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 300,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    function handleNavigation(type: any, availabilityFormat: any) {
        const from = formatTime(event.start);
        const to = formatTime(event.end);
        const availabilityType = event.unavailable
            ? 'unavailable'
            : 'available';

        console.log('Avalaibity format---------------', availabilityType);

        if (availabilityFormat === 'Single') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: availabilityType,
                getDetails: true,
                selectedDate,
                availabilityFormat,
                dateRange: 'Single',
                from,
                to,
                availableClassType: event.classType,
            });
        } else if (availabilityFormat === 'Multiple') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: availabilityType,
                getDetails: true,
                selectedDate,
                availabilityFormat,
                dateRange: 'Multiple',
                from,
                to,
                availableClassType: event.classType,
            });
        } else if (availabilityFormat === 'available') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: 'available',
                selectedDate,
                markUnavailable: true,
                getDetails: true,
                availabilityFormat: 'Single',
                dateRange: 'Single',
                from,
                to,
                availableClassType: event.classType,
            });
        }
    }

    return (
        <Modal
            transparent={true}
            visible={visible}
            animationType="fade"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity onPress={onClose} />
                <View
                    style={[
                        styles.modalContainer,
                        // { transform: [{ translateY: slideAnim }] },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                    >
                        {/* <Text style={styles.closeButtonText}>×</Text> */}
                        <Image
                            style={tw` w-4 h-4`}
                            source={Asset.CloseIcon}
                            resizeMode="contain"
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() =>
                            handleNavigation('unavailability', 'Single')
                        }
                    >
                        <Text style={styles.optionText}>Edit this shift</Text>
                    </TouchableOpacity>
                    {/* <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() => {
                            onClose();
                            setShowDeleteModal(true);
                        }}
                    >
                        <Text style={styles.optionText}>
                            Clear single shift
                        </Text>
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() => {
                            onClose();
                            setDeleteCustomRangeModal(true);
                        }}
                    >
                        <Text style={styles.optionText}>Clear all shift</Text>
                    </TouchableOpacity> */}

                    {event?.unavailable && (
                        <TouchableOpacity
                            style={styles.optionButton}
                            onPress={() =>
                                handleNavigation('availability', 'available')
                            }
                        >
                            <Text style={styles.optionText}>
                                Mark Available
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 20,
        width: '100%',
        alignItems: 'center',
    },
    closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 1,
    },
    closeButtonText: {
        fontSize: 24,
        color: '#455560',
    },
    optionButton: {
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        borderBottomColor: '#DEDEDE',
        borderBottomWidth: 1,
        paddingVertical: 15,
        marginHorizontal: 5,
        width: '100%',
    },
    optionText: {
        color: '#455560',
    },
});

export default UnavailableSlotModal;
