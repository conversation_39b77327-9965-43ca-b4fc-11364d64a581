// Adjust the import based on your project structure
import React, { useEffect, useState } from 'react';
import {
    FlatList,
    Image,
    Modal,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
// Adjust the import based on your project structure
import tw from 'twrnc';
import { SetSelectedFacility } from '~/redux/slices/facility-slice';

import { Asset } from '~/assets';

import { useAppDispatch } from '~/hooks/redux-hooks';
import { useAppSelector } from '~/hooks/redux-hooks';

const LocationModal: React.FC<{ visible: boolean; onClose: () => void }> = ({
    visible,
    onClose,
}) => {
    const [search, setSearch] = useState('');
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
    }));

    const handleSearch = (text) => {
        setSearch(text);
    };

    const filteredFacilities = store.facilityList.filter((facility) =>
        facility.facilityName.toLowerCase().includes(search.toLowerCase())
    );

    const handleFacilityPress = (facility) => {
        dispatch(SetSelectedFacility(facility));
    };

    return (
        <Modal
            visible={visible}
            transparent={true}
            animationType="slide"
            onRequestClose={onClose}
        >
            <View
                style={tw`flex-1 justify-center items-center bg-black bg-opacity-50`}
            >
                <View style={tw`bg-white rounded-lg w-11/12 max-h-3/4`}>
                    <View style={tw`p-4 py-12 bg-#D0FF01]`}>
                        <Text
                            style={tw`text-[24px] text-[#455560] font-bold mb-8 text-center`}
                        >
                            Select Location
                        </Text>
                        <View
                            style={tw`border border-[#7A7A7A] rounded-md bg-white p-1 flex flex-row items-center`}
                        >
                            <Image
                                resizeMode="cover"
                                style={tw`w-8 h-8`}
                                source={Asset.LocationIcon}
                            />
                            <TextInput
                                value={search}
                                onChangeText={handleSearch}
                                placeholder="Current Location"
                                style={tw`flex-1 ml-2`}
                            />
                        </View>
                    </View>
                    <ScrollView style={tw`flex-1 p-4`}>
                        <FlatList
                            data={filteredFacilities}
                            keyExtractor={(item) => item._id}
                            renderItem={({ item }) => (
                                <View style={tw`border-b border-[#2C2C2E4D]`}>
                                    <View style={tw`p-4 rounded`}>
                                        <Text
                                            style={tw`text-lg text-[#000000]`}
                                        >
                                            {item.facilityName}
                                        </Text>
                                        <Text style={tw`text-sm`}>
                                            {item.address.addressLine1}
                                        </Text>
                                    </View>
                                    <TouchableOpacity
                                        style={tw`px-4 pb-4`}
                                        onPress={() =>
                                            handleFacilityPress(item)
                                        }
                                    >
                                        <View
                                            style={tw`rounded-md w-40 px-2 py-1.4 border bg-white`}
                                        >
                                            <Text
                                                style={tw`text-[14px] text-black text-center`}
                                            >
                                                Select this location
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            )}
                        />
                    </ScrollView>
                    <TouchableOpacity
                        onPress={onClose}
                        style={tw`p-4 bg-red-500 rounded-b-lg`}
                    >
                        <Text style={tw`text-center text-white`}>Close</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
};

export default LocationModal;
