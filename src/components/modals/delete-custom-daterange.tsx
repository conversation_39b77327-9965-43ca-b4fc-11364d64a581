import { useFocusEffect } from '@react-navigation/native';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import {
    Alert,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';

import tw from '~/styles/tailwind';

const DatePickerComponent = ({
    label,
    date,
    onDateChange,
    open,
    setOpen,
}: {
    label: any;
    date: Date;
    onDateChange: (date: Date) => void;
    open: boolean;
    setOpen: (open: boolean) => void;
}) => (
    <View style={tw`w-[45%] mt-5`}>
        <Text style={tw`text-black text-16 pb-2`}>{label}</Text>
        <TouchableOpacity
            style={tw`w-[100%] h-[32px] border-b border-[#45556066] pb-1 flex justify-center px-1`}
            onPress={() => setOpen(true)}
        >
            <Text style={tw`text-[#455560] text-[14px]`}>
                {dayjs(date).format('MMM D, YYYY')}
            </Text>
            <DatePicker
                modal
                open={open}
                date={date}
                onConfirm={(date) => {
                    setOpen(false);
                    onDateChange(date);
                }}
                onCancel={() => setOpen(false)}
                mode="date"
            />
        </TouchableOpacity>
    </View>
);

const DeleteCustomRangeModal = ({
    visible,
    onClose,
    onDelete,
    event,
    selectedDate,
}: any) => {
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(new Date());
    const [openStartDatePicker, setOpenStartDatePicker] = useState(false);
    const [openEndDatePicker, setOpenEndDatePicker] = useState(false);

    console.log('selectedDate------------------', selectedDate, event);

    // useFocusEffect(
    //     useCallback(() => {
    //         if (event) {
    //             setStartDate(dayjs(event?.start).toDate());
    //             setEndDate(dayjs(event?.end).toDate());
    //         } else {
    //             setStartDate(dayjs(selectedDate).toDate());
    //             setEndDate(dayjs(selectedDate).toDate());
    //         }
    //     }, [event, selectedDate])
    // );

    useEffect(() => {
        if (visible) {
            if (event?.start && event?.end) {
                setStartDate(dayjs(event.start).toDate());
                setEndDate(dayjs(event.end).toDate());
            } else {
                const parsedDate = dayjs(selectedDate).toDate();
                setStartDate(parsedDate);
                setEndDate(parsedDate);
            }
        }
    }, [visible, selectedDate, event]);

    const handleEndDateChange = (date: any) => {
        if (startDate <= date) {
            setEndDate(date);
        } else {
            Alert.alert(
                'Invalid Date',
                'End date must be greater than start date.'
            );
        }
    };

    return (
        <Modal
            transparent={true}
            animationType="slide"
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContainer}>
                    <Text style={tw`text-black font-bold text-lg`}>
                        Confirm Action
                    </Text>
                    <Text style={tw`text-[#455560] text-base`}>
                        Are you sure want to delete this date range
                    </Text>
                    <View
                        style={tw`flex flex-row  border-0 justify-between mb-5`}
                    >
                        <DatePickerComponent
                            label={
                                <Text style={tw`text-base  `}>Start Date</Text>
                            }
                            date={startDate}
                            onDateChange={setStartDate}
                            open={openStartDatePicker}
                            setOpen={setOpenStartDatePicker}
                        />
                        <DatePickerComponent
                            label={
                                <Text style={tw`text-base  `}>End Date</Text>
                            }
                            date={endDate}
                            onDateChange={handleEndDateChange}
                            open={openEndDatePicker}
                            setOpen={setOpenEndDatePicker}
                        />
                    </View>
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={onClose}
                        >
                            <Text style={styles.buttonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.deleteButton}
                            onPress={() => onDelete(startDate, endDate)}
                        >
                            <Text style={styles.buttonText}>Delete</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
        width: '90%',
        padding: 20,
        backgroundColor: 'white',
        borderRadius: 10,
        alignItems: 'center',
    },
    heading: {
        fontSize: 20,
        fontWeight: 'bold',
        // marginBottom: 10,
    },
    title: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 10,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    },
    deleteButton: {
        backgroundColor: 'red',
        padding: 10,
        borderRadius: 5,
        flex: 1,
        marginLeft: 5,
        alignItems: 'center',
    },
    cancelButton: {
        backgroundColor: 'gray',
        padding: 10,
        borderRadius: 5,
        flex: 1,
        marginRight: 5,
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontWeight: 'bold',
    },
});

export default DeleteCustomRangeModal;
