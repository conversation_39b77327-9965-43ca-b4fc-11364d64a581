import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, Vibration, View } from 'react-native';
import WheelPicker from 'react-native-wheely';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

function generateHeightArray({ startHeight, endHeight }: any) {
    const start = parseInt(startHeight, 10);
    const end = parseInt(endHeight, 10);

    const heightArray = [];

    for (let cm = start; cm <= end; cm++) {
        heightArray.push(`${cm} cm`);
    }

    return heightArray;
}

const startHeight = '50';
const endHeight = '272';

const heightArray = generateHeightArray({ startHeight, endHeight });

const HeightScroller = ({ updateHeight = 0 }: any) => {
    const [selectedIndex, setSelectedIndex] = useState(12);

    const OnHeightScroll = (height: any) => {
        setSelectedIndex(height);
        // let heightVal = height + 1;
        updateHeight(heightArray[height]);
    };

    useEffect(() => {
        updateHeight(heightArray[selectedIndex]);
    }, []);
    return (
        <View style={tw``}>
            <WheelPicker
                selectedIndex={selectedIndex}
                options={heightArray}
                onChange={(index) => {
                    OnHeightScroll(index);
                    // Vibration.vibrate();
                }}
                selectedIndicatorStyle={tw`border-t-2 rounded-0 border-b-2 border-[#000000] bg-[#ffffff] `}
                itemHeight={50}
                itemTextStyle={tw`text-black  font-bold text-18 `}
                containerProps={tw`mx-5 flex justify-center items-center`}
            />
        </View>
    );
};

export default HeightScroller;

const styles = StyleSheet.create({});
