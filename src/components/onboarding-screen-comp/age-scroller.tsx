import React, { memo, useEffect, useState } from 'react';
import { Vibration, View } from 'react-native';
import WheelPicker from 'react-native-wheely';

import tw from '~/styles/tailwind';

let yearsArray = [];

for (let i = 1; i <= 100; i++) {
    yearsArray.push(i + ' yrs');
}

const AgeScroller = ({ updateAge = 0 }: any) => {
    const [selectedIndex, setSelectedIndex] = useState(15);

    const OnAgeScroll = (age: any) => {
        console.log('age', age);
        setSelectedIndex(age);
        // let  = age + 1;
        updateAge(yearsArray[age].split(' ')[0]);
    };

    useEffect(() => {
        updateAge(yearsArray[selectedIndex].split(' ')[0]);
    }, []);

    return (
        <View style={tw``}>
            <WheelPicker
                selectedIndex={selectedIndex}
                options={yearsArray}
                onChange={(index) => {
                    Vibration.vibrate();
                    OnAgeScroll(index);
                }}
                selectedIndicatorStyle={tw`border-t-2 rounded-0 border-b-2 border-black bg-white `}
                itemHeight={50}
                itemTextStyle={tw`text-black  font-bold text-18 `}
                containerProps={tw`mx-5 flex justify-center items-center`}
            />
        </View>
    );
};

export default memo(AgeScroller);
