function convertDateToISO(dateString) {
    console.log('today date', dateString);
    // Parse the input date string using dayjs
    const date = dayjs(dateString);

    // Format the date to ISO string with time set to midnight UTC
    const isoString = date.utc().format('YYYY-MM-DDTHH:mm:ss[Z]');

    return isoString;

    // Example usage
    // const formattedDate = convertDateToISO('2024-10-23');
    // console.log(formattedDate); // Output: "2024-10-23T00:00:00Z"
}
