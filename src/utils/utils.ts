import uuid from 'react-native-uuid';

import {
    CA<PERSON><PERSON><PERSON>_SCREEN,
    CLIENTS_NOTES,
    CLIENT_BASIC_ASSESSMENT,
    CLIENT_BASIC_INFO,
    CLIENT_CHANGE_PASSWORD,
    CLIENT_DETAILS,
    C<PERSON>IENT_LISTING,
    HOM<PERSON>_SCREEN,
    STAFF_DETAIL,
    STAFF_PERSONAL_INFORMATION,
    STAFF_PROFILE,
    STAFF_SKILL_EXPERIENCE_TAB,
} from '~/constants/navigation-constant';

export function isMobileEmail(str: string): 'email' | 'mobile' | undefined {
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str);
    const isMobile = /^\d{10}$/.test(str); // Adjust regex as needed for your mobile format

    if (isEmail) {
        return 'email';
    } else if (isMobile) {
        return 'mobile';
    } else {
        return undefined; // Return undefined if neither
    }
}

export const uniqueId = () => {
    const unique_id = uuid.v4();
    return unique_id.slice(0, 8);
};

export const TAB_ACTIVE_SCREENS = {
    Home: [HOME_SCREEN],
    Schedule: [CALENDAR_SCREEN],
    Profile: [
        STAFF_DETAIL,
        STAFF_PERSONAL_INFORMATION,
        STAFF_SKILL_EXPERIENCE_TAB,
    ],
    More: [
        STAFF_PROFILE,
        CLIENT_LISTING,
        CLIENT_DETAILS,
        CLIENT_BASIC_INFO,
        CLIENT_BASIC_ASSESSMENT,
        CLIENTS_NOTES,
        CLIENT_CHANGE_PASSWORD,
    ],
};

export const serviceTypesCaps = {
    personalAppointment: 'Personal Appointment',
    classes: 'Classe',
    bookings: 'Booking',
    courses: 'Course',
};
